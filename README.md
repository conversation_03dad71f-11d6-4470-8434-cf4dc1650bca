# SalonProManager - نظام إدارة صالونات متكامل

نظام إدارة شامل ومتكامل مصمم خصيصاً لصالونات التجميل، صالونات الحلاقة، ومراكز السبا متعددة الفروع.

## الميزات الرئيسية

- 🏢 إدارة الفروع المتعددة
- 👥 إدارة العملاء والمناطق
- 📅 نظام حجز المواعيد المتقدم
- ✨ إدارة الخدمات والموظفين
- 📦 إدارة المنتجات والمخزون
- 💰 نظام المبيعات والفواتير
- 📊 التقارير والتحليلات
- 🔐 نظام المصادقة والصلاحيات
- 📱 تكامل WhatsApp للإشعارات
- 🌐 دعم اللغة العربية

## 🚀 الحالة الحالية للمشروع

### ✅ الوظائف المكتملة والعاملة:
- **🔐 تسجيل الدخول والمصادقة** - يعمل بالكامل
- **🏢 إدارة الفروع** - إضافة، تعديل، حذف، عرض
- **👥 إدارة العملاء** - إضافة، حذف، عرض
- **📊 لوحة القيادة** - مع الرسوم البيانية
- **📈 التقارير** - إحصائيات أساسية وتقارير
- **🌐 واجهة عربية كاملة** - تدعم RTL

### 🚧 قيد التطوير:
- إدارة المواعيد
- إدارة الخدمات
- إدارة الموظفين
- إدارة المنتجات
- المبيعات والفواتير
- الإعدادات المتقدمة

## التقنيات المستخدمة

- **Backend**: FastAPI + SQLAlchemy
- **Frontend**: Streamlit
- **Database**: SQL Server
- **Authentication**: JWT
- **WhatsApp**: Twilio API

## متطلبات التشغيل

- Python 3.8+
- SQL Server
- ODBC Driver 17 for SQL Server

## 🚀 التشغيل السريع

### الطريقة الحالية (مبسطة):

#### 1. تشغيل API المبسط:
```bash
python simple_auth.py
```
سيعمل على: http://localhost:8001

#### 2. تشغيل واجهة المستخدم:
```bash
streamlit run streamlit_app.py
```
سيعمل على: http://localhost:8501

#### 3. بيانات تسجيل الدخول:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## التثبيت والتشغيل الكامل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd SalonProManager
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
- إنشاء قاعدة بيانات SQL Server باسم `SalonProManager`
- نسخ ملف `.env.example` إلى `.env` وتحديث إعدادات قاعدة البيانات

### 5. إعداد البيانات الأولية
```bash
python init_data.py
```

### 6. تشغيل الخادم
```bash
# تشغيل API
python main.py

# في terminal آخر، تشغيل واجهة المستخدم
streamlit run streamlit_app.py
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## الوصول للتطبيق

- **واجهة المستخدم**: http://localhost:8501
- **API Documentation**: http://localhost:8000/docs
- **API**: http://localhost:8000

## هيكل المشروع

```
SalonProManager/
├── api/                    # مسارات API
│   └── routes/            # مسارات مختلفة
├── models/                # نماذج قاعدة البيانات
├── schemas/               # مخططات Pydantic
├── config.py              # إعدادات التطبيق
├── database.py            # إعداد قاعدة البيانات
├── main.py               # تطبيق FastAPI الرئيسي
├── streamlit_app.py      # واجهة المستخدم
├── init_data.py          # البيانات الأولية
└── requirements.txt      # المتطلبات
```

## المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
