# 🚀 **دليل شامل لتطوير نظام SalonProManager - نظام إدارة الصالونات المتكامل**

## 📋 **نظرة عامة على المشروع**
إنشاء نظام إدارة شامل للصالونات يسمى "SalonProManager" باستخدام Python و Streamlit و SQL Server. هذا النظام يجب أن يتعامل مع جميع جوانب عمليات الصالون بما في ذلك المبيعات والمخزون وإدارة العملاء وإدارة الموظفين والتتبع المالي مع دعم ثنائي اللغة (العربية/الإنجليزية).

---

## 🎯 **المتطلبات الأساسية**

### **التقنيات المستخدمة:**
- **الواجهة الأمامية:** Streamlit (إطار عمل Python للويب)
- **الخلفية:** Python 3.8+
- **قاعدة البيانات:** SQL Server Express
- **الاتصال:** pyodbc
- **الرسوم البيانية:** Plotly لتصور البيانات
- **لغة الواجهة:** العربية (RTL) مع دعم الإنجليزية

### **اتصال قاعدة البيانات:**
```python
connection_string = (
    "DRIVER={ODBC Driver 17 for SQL Server};"
    "SERVER=alisamaraa.ddns.net,4100;"
    "DATABASE=SalonProManager_DB;"
    "UID=sa;"
    "PWD=@a123admin4;"
    "TrustServerCertificate=yes;"
)
```

---

## 🗄️ **متطلبات هيكل قاعدة البيانات**

### **1. الجداول الأساسية المطلوب إنشاؤها:**

#### **جدول العملاء (Customers):**
```sql
CREATE TABLE Customers (
    CustomerId INT IDENTITY(1,1) PRIMARY KEY,
    customer_code NVARCHAR(50) UNIQUE,          -- كود العميل التسلسلي
    FirstName NVARCHAR(100) NOT NULL,           -- الاسم الأول
    LastName NVARCHAR(100),                     -- الاسم الأخير
    PhoneNumber NVARCHAR(20),                   -- رقم الهاتف
    Email NVARCHAR(100),                        -- البريد الإلكتروني
    DateOfBirth DATE,                           -- تاريخ الميلاد
    Notes NVARCHAR(500),                        -- ملاحظات
    branch_id INT,                              -- معرف الفرع
    CreatedAt DATETIME DEFAULT GETDATE(),       -- تاريخ الإنشاء
    is_deleted BIT DEFAULT 0                    -- حذف ناعم
)
```

#### **جدول الموظفين (employees):**
```sql
CREATE TABLE employees (
    employee_id INT IDENTITY(1,1) PRIMARY KEY,
    employee_code NVARCHAR(50) UNIQUE,          -- كود الموظف التسلسلي
    first_name NVARCHAR(100) NOT NULL,          -- الاسم الأول
    last_name NVARCHAR(100),                    -- الاسم الأخير
    phone NVARCHAR(20),                         -- رقم الهاتف
    email NVARCHAR(100),                        -- البريد الإلكتروني
    position NVARCHAR(100),                     -- المنصب
    salary DECIMAL(10,2),                       -- الراتب
    commission_rate DECIMAL(5,2) DEFAULT 15.00, -- نسبة العمولة
    hire_date DATE,                             -- تاريخ التوظيف
    branch_id INT,                              -- معرف الفرع
    is_active BIT DEFAULT 1,                    -- نشط/غير نشط
    created_at DATETIME DEFAULT GETDATE(),      -- تاريخ الإنشاء
    is_deleted BIT DEFAULT 0                    -- حذف ناعم
)
```

#### **جدول الأصناف الموحد (items) - خدمات ومنتجات:**
```sql
CREATE TABLE items (
    item_id INT IDENTITY(1,1) PRIMARY KEY,
    item_code NVARCHAR(20) UNIQUE NOT NULL,     -- كود الصنف التسلسلي
    item_name NVARCHAR(100) NOT NULL,           -- اسم الصنف
    item_type NVARCHAR(20) NOT NULL CHECK (item_type IN ('service', 'product')), -- نوع الصنف
    description NVARCHAR(500),                  -- الوصف
    category_id INT,                            -- معرف الفئة
    price_tier_1 DECIMAL(10,2) DEFAULT 0,       -- سعر المستوى الأول (بريميوم)
    price_tier_2 DECIMAL(10,2) DEFAULT 0,       -- سعر المستوى الثاني (عادي)
    price_tier_3 DECIMAL(10,2) DEFAULT 0,       -- سعر المستوى الثالث (اقتصادي)
    cost_price DECIMAL(10,2) DEFAULT 0,         -- سعر التكلفة
    stock_quantity INT DEFAULT 0,               -- كمية المخزون
    min_stock_level INT DEFAULT 5,              -- الحد الأدنى للمخزون
    duration_minutes INT DEFAULT 30,            -- مدة الخدمة بالدقائق
    commission_rate DECIMAL(5,2) DEFAULT 15.00, -- نسبة العمولة
    is_active BIT DEFAULT 1,                    -- نشط/غير نشط
    created_at DATETIME DEFAULT GETDATE(),      -- تاريخ الإنشاء
    is_deleted BIT DEFAULT 0                    -- حذف ناعم
)
```

#### **جدول الفواتير (invoices):**
```sql
CREATE TABLE invoices (
    invoice_id INT IDENTITY(1,1) PRIMARY KEY,
    invoice_code NVARCHAR(50) UNIQUE,           -- كود الفاتورة التسلسلي
    customer_id INT,                            -- معرف العميل
    branch_id INT,                              -- معرف الفرع
    employee_id INT,                            -- معرف الموظف
    invoice_date DATE,                          -- تاريخ الفاتورة
    subtotal DECIMAL(10,2),                     -- المجموع الفرعي
    tax_amount DECIMAL(10,2) DEFAULT 0,         -- مبلغ الضريبة
    discount_amount DECIMAL(10,2) DEFAULT 0,    -- مبلغ الخصم
    total_amount DECIMAL(10,2),                 -- المبلغ الإجمالي
    payment_method NVARCHAR(50),                -- طريقة الدفع
    payment_status NVARCHAR(50),                -- حالة الدفع
    notes NVARCHAR(500),                        -- ملاحظات
    responsible_employee NVARCHAR(100),         -- الموظف المسؤول
    created_at DATETIME DEFAULT GETDATE(),      -- تاريخ الإنشاء
    is_deleted BIT DEFAULT 0                    -- حذف ناعم
)
```

#### **جدول عناصر الفواتير (invoice_items):**
```sql
CREATE TABLE invoice_items (
    item_id INT IDENTITY(1,1) PRIMARY KEY,
    invoice_id INT,                             -- معرف الفاتورة
    item_type NVARCHAR(20),                     -- نوع العنصر (خدمة/منتج)
    item_code NVARCHAR(20),                     -- كود العنصر
    item_name NVARCHAR(100),                    -- اسم العنصر
    quantity INT,                               -- الكمية
    unit_price DECIMAL(10,2),                   -- سعر الوحدة
    total_price DECIMAL(10,2),                  -- السعر الإجمالي
    employee_name NVARCHAR(100),                -- اسم الموظف
    commission_amount DECIMAL(10,2) DEFAULT 0   -- مبلغ العمولة
)
```

#### **جدول الخزنة (cash_register):**
```sql
CREATE TABLE cash_register (
    transaction_id INT IDENTITY(1,1) PRIMARY KEY,
    transaction_type NVARCHAR(20),              -- نوع المعاملة (دخل/مصروف)
    amount DECIMAL(10,2),                       -- المبلغ
    description NVARCHAR(500),                  -- الوصف
    reference_type NVARCHAR(50),                -- نوع المرجع (فاتورة/مصروف)
    reference_id INT,                           -- معرف المرجع
    branch_id INT,                              -- معرف الفرع
    created_by NVARCHAR(100),                   -- منشئ المعاملة
    payment_method NVARCHAR(50),                -- طريقة الدفع
    notes NVARCHAR(500),                        -- ملاحظات
    created_at DATETIME DEFAULT GETDATE(),      -- تاريخ الإنشاء
    is_deleted BIT DEFAULT 0                    -- حذف ناعم
)
```

#### **جدول الأرقام التسلسلية (sequences):**
```sql
CREATE TABLE sequences (
    sequence_name NVARCHAR(50) PRIMARY KEY,     -- اسم التسلسل
    current_value BIGINT NOT NULL DEFAULT 0,    -- القيمة الحالية
    prefix NVARCHAR(10) DEFAULT '',             -- البادئة
    suffix NVARCHAR(10) DEFAULT '',             -- اللاحقة
    min_length INT DEFAULT 4,                   -- الحد الأدنى للطول
    branch_id INT,                              -- معرف الفرع (اختياري)
    created_at DATETIME DEFAULT GETDATE(),      -- تاريخ الإنشاء
    updated_at DATETIME DEFAULT GETDATE()       -- تاريخ التحديث
)
```

### **2. الإجراء المخزن المطلوب للأكواد التسلسلية:**
```sql
CREATE PROCEDURE dbo.GetNextSequence
    @sequence_name NVARCHAR(50),
    @branch_id INT = NULL,
    @next_code NVARCHAR(50) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    BEGIN TRY
        DECLARE @current_value BIGINT, @prefix NVARCHAR(10), @suffix NVARCHAR(10), @min_length INT;
        
        SELECT @prefix = prefix, @suffix = suffix, @min_length = min_length
        FROM sequences WITH (UPDLOCK, HOLDLOCK)
        WHERE sequence_name = @sequence_name;
        
        UPDATE sequences 
        SET current_value = current_value + 1, updated_at = GETDATE()
        WHERE sequence_name = @sequence_name;
        
        SELECT @current_value = current_value FROM sequences WHERE sequence_name = @sequence_name;
        
        SET @next_code = @prefix + RIGHT('000000000' + CAST(@current_value AS NVARCHAR), @min_length) + @suffix;
        
        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @next_code = 'ERROR';
    END CATCH
END
```

---

## 🎨 **متطلبات الواجهة والصفحات**

### **1. نظام المصادقة:**
- صفحة تسجيل دخول بالمستخدم وكلمة المرور
- اختيار الفرع للمستخدمين
- إدارة الجلسات مع أدوار المستخدمين
- مستخدم افتراضي: username="admin", password="admin123"

### **2. لوحة التحكم الرئيسية:**
- ملخص المبيعات اليومية
- رسوم بيانية للإيرادات (Plotly)
- تنبيهات المخزون المنخفض
- المعاملات الأخيرة
- أزرار الإجراءات السريعة

### **3. صفحة المبيعات والفواتير:**
- **وظيفة البحث:** البحث في الأصناف بالكود أو الاسم من قاعدة البيانات
- **اختيار العميل:** قائمة منسدلة بالعملاء الموجودين + خيار "عميل جديد"
- **اختيار الموظف:** الموظف المسؤول لتتبع العمولة
- **إضافة الأصناف:** إضافة خدمات/منتجات بالكمية والتسعير
- **الحسابات:** حساب تلقائي للمجموع الفرعي والخصم والضريبة والإجمالي
- **الحفظ في قاعدة البيانات:** توليد كود فاتورة تسلسلي وحفظ جميع البيانات
- **وظيفة الطباعة:** إنتاج فاتورة قابلة للطباعة

### **4. إدارة العملاء:**
- إضافة عملاء جدد بأكواد تسلسلية (CUS001001, CUS001002, إلخ)
- تحرير معلومات العملاء الموجودين
- البحث وتصفية العملاء
- تاريخ معاملات العميل
- وظيفة الحذف الناعم

### **5. إدارة الموظفين:**
- إضافة موظفين بأكواد تسلسلية (EMP000101, EMP000102, إلخ)
- إدارة الرواتب ومعدلات العمولة
- تتبع أداء الموظفين
- حسابات وتقارير العمولة

### **6. إدارة الأصناف (خدمات ومنتجات):**
- إدارة موحدة للخدمات والمنتجات
- أكواد تسلسلية (S001, S002 للخدمات؛ P001, P002 للمنتجات)
- نظام تسعير ثلاثي المستويات (بريميوم، عادي، اقتصادي)
- إدارة المخزون للمنتجات
- مدة الخدمة ومعدلات العمولة

### **7. الخزنة:**
- عرض رصيد الخزنة الحالي
- تسجيل المعاملات النقدية الداخلة/الخارجة
- ربط مدفوعات الفواتير تلقائياً
- تاريخ المعاملات مع التصفية

### **8. صفحة التقارير:**
- تقارير المبيعات حسب نطاق التاريخ
- تقارير عمولة الموظفين
- تقارير المخزون
- الملخصات المالية
- وظيفة التصدير

---

## 🔧 **متطلبات التنفيذ الحرجة**

### **1. توليد الأكواد التسلسلية:**
- **آمن للخيوط:** استخدام إجراء مخزن مع أقفال لمنع الأكواد المكررة
- **خاص بالفرع:** تسلسلات مختلفة للفروع المختلفة
- **أمثلة التنسيق:**
  - الفواتير: B1-INV001001, B2-INV001001
  - العملاء: CUS001001, CUS001002
  - الموظفين: EMP000101, EMP000102
  - الخدمات: S001, S002, S003
  - المنتجات: P001, P002, P003

### **2. تكامل قاعدة البيانات:**
- جميع العمليات يجب أن تحفظ مباشرة في قاعدة بيانات SQL Server
- لا تخزين محلي - كل شيء يجب أن يكون دائماً
- معالجة صحيحة للأخطاء لاتصالات قاعدة البيانات
- إدارة المعاملات لسلامة البيانات

### **3. وظيفة البحث:**
- بحث في الوقت الفعلي في جدول الأصناف بالكود أو الاسم
- عرض نتائج البحث مع معلومات التسعير والمخزون
- وظيفة إضافة سريعة للفاتورة

### **4. الحسابات المالية:**
- حساب العمولة التلقائي (15% للخدمات افتراضياً)
- حساب الضريبة (افتراضي 0%، قابل للتكوين)
- دعم الخصم (نسبة مئوية أو مبلغ ثابت)
- تتبع رصيد الخزنة

### **5. دعم متعدد الفروع:**
- فصل البيانات الخاصة بالفرع
- ترقيم تسلسلي خاص بالفرع
- التحكم في وصول المستخدم حسب الفرع

---

## 📱 **مواصفات واجهة المستخدم/تجربة المستخدم**

### **متطلبات التصميم:**
- **اللغة:** واجهة عربية مع دعم RTL
- **الموضوع:** موضوع صالون احترافي بألوان عصرية
- **التخطيط:** واجهة نظيفة ومنظمة تتجنب الفوضى
- **التنقل:** تنقل شريط جانبي مع أيقونات واضحة
- **متجاوب:** يعمل على أحجام شاشة مختلفة

### **مكونات واجهة المستخدم الرئيسية:**
```python
# مثال على هيكل Streamlit
st.set_page_config(
    page_title="SalonProManager",
    page_icon="💇‍♀️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# تنقل الشريط الجانبي
pages = {
    "🏠 الرئيسية": "dashboard",
    "💰 المبيعات والفواتير": "sales", 
    "👥 إدارة العملاء": "customers",
    "👨‍💼 إدارة الموظفين": "employees",
    "📦 إدارة الأصناف": "items",
    "💵 الخزنة": "cash",
    "📊 التقارير": "reports",
    "⚙️ الإعدادات": "settings"
}
```

### **متطلبات النماذج:**
- التحقق من صحة الإدخال لجميع النماذج
- رسائل النجاح/الخطأ باللغة العربية
- وظيفة الحفظ التلقائي حيثما كان مناسباً
- خيارات إعادة تعيين النموذج الواضحة

---

## 🔐 **الأمان وإدارة البيانات**

### **أدوار المستخدم:**
1. **مدير عام:** وصول كامل للنظام
2. **مدير فرع:** إدارة خاصة بالفرع
3. **موظف مبيعات:** إدارة المبيعات والعملاء فقط
4. **موظف:** وصول محدود للقراءة

### **سلامة البيانات:**
- تنفيذ الحذف الناعم (علامة is_deleted)
- مسارات التدقيق مع طوابع زمنية created_at/updated_at
- علاقات المفاتيح الخارجية
- التحقق من صحة البيانات في كل من الواجهة الأمامية وقاعدة البيانات

---

## 📊 **متطلبات البيانات النموذجية**

### **التهيئة مع:**
- 3 فروع افتراضية
- 5 عملاء نموذجيين بأكواد تسلسلية
- 3 موظفين نموذجيين
- 10 خدمات (S001-S010) بفئات مختلفة
- 10 منتجات (P001-P010) بكميات مخزون
- 8 فئات للتصنيف
- تهيئة الأرقام التسلسلية

---

## 🎯 **معايير النجاح**

### **يجب على النظام:**
1. ✅ الاتصال بنجاح بقاعدة بيانات SQL Server
2. ✅ توليد أكواد تسلسلية فريدة لجميع الكيانات
3. ✅ التعامل مع المستخدمين المتزامنين دون تكرار الأكواد
4. ✅ حفظ جميع بيانات الفاتورة في قاعدة البيانات فوراً
5. ✅ توفير وظيفة بحث في الوقت الفعلي
6. ✅ حساب العمولات والمجاميع المالية بشكل صحيح
7. ✅ دعم الواجهة العربية مع تخطيط RTL صحيح
8. ✅ إنتاج فواتير قابلة للطباعة
9. ✅ الحفاظ على رصيد الخزنة بدقة
10. ✅ توفير قدرات تقارير شاملة

### **متطلبات الأداء:**
- وقت تحميل الصفحة < 3 ثوان
- نتائج البحث < 1 ثانية
- عمليات قاعدة البيانات < 2 ثانية
- دعم 10+ مستخدمين متزامنين

### **معالجة الأخطاء:**
- فشل اتصالات قاعدة البيانات بأناقة
- رسائل خطأ سهلة الاستخدام باللغة العربية
- آليات احتياطية للعمليات الحرجة
- تسجيل شامل

---

## 🚀 **خطوات التنفيذ**

1. **إعداد اتصال قاعدة البيانات وإنشاء جميع الجداول**
2. **تنفيذ نظام المصادقة**
3. **إنشاء نظام توليد الأكواد التسلسلية**
4. **بناء لوحة التحكم الرئيسية مع التنقل**
5. **تنفيذ وظيفة المبيعات/الفواتير**
6. **إضافة إدارة العملاء والموظفين**
7. **إنشاء نظام إدارة الأصناف**
8. **تنفيذ وظيفة الخزنة**
9. **إضافة قدرات التقارير**
10. **اختبار جميع الميزات بدقة**

---

## 📝 **ملاحظات إضافية**

- استخدام النص العربي في جميع أنحاء الواجهة
- تنفيذ معالجة صحيحة للأخطاء لجميع عمليات قاعدة البيانات
- التأكد من دقة جميع الحسابات النقدية إلى منزلتين عشريتين
- توفير ملاحظات واضحة للمستخدم لجميع الإجراءات
- جعل النظام بديهياً لموظفي الصالون مع الحد الأدنى من التدريب
- ضمان اتساق البيانات عبر جميع الوحدات
- تنفيذ آليات النسخ الاحتياطي والاستعادة المناسبة

**هذا النظام يجب أن يكون جاهزاً للإنتاج للاستخدام الفعلي في الصالون مع بيانات العملاء الحقيقية والمعاملات المالية.**

---

## 💾 **البيانات الافتراضية المطلوبة**

### **جدول الفروع (branches):**
```sql
INSERT INTO branches (branch_name, branch_code, address, phone, manager_name) VALUES
('الفرع الرئيسي', 'B001', 'القاهرة - المعادي', '01090829393', 'أحمد محمد'),
('فرع المهندسين', 'B002', 'الجيزة - المهندسين', '01090829394', 'فاطمة علي'),
('فرع مدينة نصر', 'B003', 'القاهرة - مدينة نصر', '01090829395', 'سارة أحمد');
```

### **جدول الأرقام التسلسلية (sequences):**
```sql
INSERT INTO sequences (sequence_name, current_value, prefix, suffix, min_length, branch_id) VALUES
('invoice_number', 1000, 'INV', '', 6, NULL),
('customer_code', 1000, 'CUS', '', 6, NULL),
('service_code', 100, 'S', '', 3, NULL),
('product_code', 100, 'P', '', 3, NULL),
('employee_code', 100, 'EMP', '', 3, NULL),
('branch_invoice_1', 1000, 'B1-INV', '', 6, 1),
('branch_invoice_2', 1000, 'B2-INV', '', 6, 2),
('branch_invoice_3', 1000, 'B3-INV', '', 6, 3);
```

### **جدول الفئات (categories):**
```sql
INSERT INTO categories (category_name, description, price_tier_1, price_tier_2, price_tier_3, created_by) VALUES
('خدمات الشعر', 'جميع خدمات قص وتصفيف الشعر', 100.00, 80.00, 60.00, 'admin'),
('خدمات التجميل', 'خدمات المكياج والعناية بالبشرة', 150.00, 120.00, 90.00, 'admin'),
('منتجات العناية', 'منتجات العناية بالشعر والبشرة', 80.00, 60.00, 40.00, 'admin'),
('خدمات الأظافر', 'مانيكير وبديكير وعناية بالأظافر', 60.00, 45.00, 30.00, 'admin'),
('خدمات العرائس', 'خدمات خاصة للعرائس والمناسبات', 500.00, 400.00, 300.00, 'admin'),
('منتجات التصفيف', 'أدوات ومنتجات تصفيف الشعر', 120.00, 100.00, 80.00, 'admin'),
('خدمات الرجال', 'خدمات خاصة بالرجال', 80.00, 60.00, 40.00, 'admin'),
('منتجات طبيعية', 'منتجات طبيعية وعضوية', 200.00, 150.00, 100.00, 'admin');
```

### **جدول الخدمات الافتراضية (items - services):**
```sql
INSERT INTO items (item_code, item_name, item_type, description, price_tier_1, price_tier_2, price_tier_3, duration_minutes, commission_rate, category_id, created_by) VALUES
('S001', 'قص شعر رجالي', 'service', 'قص شعر عادي للرجال', 80.00, 60.00, 40.00, 30, 15.00, 1, 'admin'),
('S002', 'قص شعر نسائي', 'service', 'قص شعر عادي للسيدات', 120.00, 100.00, 80.00, 45, 15.00, 1, 'admin'),
('S003', 'صبغة شعر', 'service', 'صبغة شعر كاملة', 200.00, 150.00, 100.00, 120, 20.00, 1, 'admin'),
('S004', 'مكياج عادي', 'service', 'مكياج يومي', 150.00, 120.00, 90.00, 60, 15.00, 2, 'admin'),
('S005', 'مكياج عروس', 'service', 'مكياج خاص للعرائس', 500.00, 400.00, 300.00, 180, 25.00, 5, 'admin'),
('S006', 'مانيكير', 'service', 'عناية بأظافر اليدين', 60.00, 45.00, 30.00, 45, 10.00, 4, 'admin'),
('S007', 'بديكير', 'service', 'عناية بأظافر القدمين', 80.00, 60.00, 40.00, 60, 10.00, 4, 'admin'),
('S008', 'فرد شعر', 'service', 'فرد الشعر بالكيراتين', 300.00, 250.00, 200.00, 180, 20.00, 1, 'admin'),
('S009', 'تنظيف بشرة', 'service', 'تنظيف عميق للبشرة', 100.00, 80.00, 60.00, 90, 15.00, 2, 'admin'),
('S010', 'حمام كريم', 'service', 'حمام كريم مغذي للشعر', 50.00, 40.00, 30.00, 60, 10.00, 1, 'admin');
```

### **جدول المنتجات الافتراضية (items - products):**
```sql
INSERT INTO items (item_code, item_name, item_type, description, price_tier_1, price_tier_2, price_tier_3, cost_price, stock_quantity, min_stock_level, category_id, created_by) VALUES
('P001', 'شامبو للشعر الجاف', 'product', 'شامبو مرطب للشعر الجاف', 80.00, 60.00, 40.00, 30.00, 50, 5, 3, 'admin'),
('P002', 'كريم للشعر', 'product', 'كريم مغذي للشعر', 100.00, 80.00, 60.00, 40.00, 30, 5, 3, 'admin'),
('P003', 'زيت للشعر', 'product', 'زيت طبيعي للشعر', 120.00, 100.00, 80.00, 50.00, 25, 5, 6, 'admin'),
('P004', 'مزيل مكياج', 'product', 'مزيل مكياج لطيف', 60.00, 45.00, 30.00, 20.00, 40, 10, 3, 'admin'),
('P005', 'كريم مرطب', 'product', 'كريم مرطب للوجه', 150.00, 120.00, 90.00, 60.00, 20, 5, 3, 'admin'),
('P006', 'طلاء أظافر', 'product', 'طلاء أظافر عالي الجودة', 40.00, 30.00, 20.00, 15.00, 100, 20, 4, 'admin'),
('P007', 'صبغة شعر', 'product', 'صبغة شعر احترافية', 180.00, 150.00, 120.00, 80.00, 15, 3, 3, 'admin'),
('P008', 'كريم فرد', 'product', 'كريم فرد الشعر', 250.00, 200.00, 150.00, 120.00, 10, 2, 6, 'admin'),
('P009', 'ماسك للوجه', 'product', 'ماسك مرطب للوجه', 90.00, 70.00, 50.00, 35.00, 30, 5, 3, 'admin'),
('P010', 'سيروم للشعر', 'product', 'سيروم مغذي للشعر', 200.00, 160.00, 120.00, 90.00, 20, 5, 6, 'admin');
```

### **جدول الموظفين الافتراضي (employees):**
```sql
INSERT INTO employees (employee_code, first_name, last_name, phone, email, position, salary, commission_rate, hire_date, branch_id, created_by) VALUES
('EMP000101', 'أحمد', 'محمد', '01111111111', '<EMAIL>', 'مدير فرع', 5000.00, 20.00, '2024-01-01', 1, 'admin'),
('EMP000102', 'فاطمة', 'علي', '01222222222', '<EMAIL>', 'خبيرة تجميل', 3500.00, 15.00, '2024-01-15', 1, 'admin'),
('EMP000103', 'محمد', 'حسن', '01333333333', '<EMAIL>', 'حلاق', 3000.00, 15.00, '2024-02-01', 2, 'admin');
```

### **جدول العملاء الافتراضي (Customers):**
```sql
INSERT INTO Customers (customer_code, FirstName, LastName, PhoneNumber, Email, branch_id) VALUES
('CUS001001', 'as', 'ass', '01090829393', '<EMAIL>', 1),
('CUS001002', 'عميل', 'تجريبي', '01090829394', '<EMAIL>', 1),
('CUS001003', 'على', '', '01090829395', '<EMAIL>', 2);
```

---

## 🔧 **دوال Python المطلوبة**

### **دالة الاتصال بقاعدة البيانات:**
```python
def get_db_connection():
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=Terra;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        return None
```

### **دالة توليد الأكواد التسلسلية:**
```python
def get_next_sequence_code(sequence_name, branch_id=None):
    try:
        conn = get_db_connection()
        if conn is None:
            return None

        cursor = conn.cursor()
        cursor.execute("""
            DECLARE @next_code NVARCHAR(50);
            EXEC GetNextSequence ?, ?, @next_code OUTPUT;
            SELECT @next_code;
        """, (sequence_name, branch_id))

        result = cursor.fetchone()
        conn.close()

        if result and result[0] != 'ERROR':
            return result[0]
        return None
    except Exception as e:
        return None
```

### **دالة البحث في الأصناف:**
```python
def search_items(search_term):
    try:
        conn = get_db_connection()
        if conn is None:
            return []

        cursor = conn.cursor()
        cursor.execute("""
            SELECT item_code, item_name, item_type, price_tier_2,
                   ISNULL(stock_quantity, 0) as stock_quantity
            FROM items
            WHERE is_active = 1 AND is_deleted = 0
            AND (item_code LIKE ? OR item_name LIKE ?)
            ORDER BY item_type, item_code
        """, (f'%{search_term}%', f'%{search_term}%'))

        results = cursor.fetchall()
        conn.close()
        return results
    except Exception as e:
        return []
```

---

## 🎊 **الخلاصة**

هذا الدليل يوفر خارطة طريق شاملة لتطوير نظام SalonProManager المتكامل. النظام مصمم ليكون حلاً شاملاً لإدارة الصالونات مع التركيز على سهولة الاستخدام والموثوقية والأداء. جميع المتطلبات محددة بوضوح لضمان التنفيذ الناجح والنشر في بيئة الإنتاج.

**ملف محفوظ باسم:** `SalonProManager_Complete_Prompt.md`

**يحتوي على:**
- ✅ جميع متطلبات النظام
- ✅ هيكل قاعدة البيانات الكامل
- ✅ الإجراءات المخزنة المطلوبة
- ✅ البيانات الافتراضية
- ✅ دوال Python الأساسية
- ✅ متطلبات الواجهة والتصميم
- ✅ معايير النجاح والأداء

**هذا الملف جاهز لإعطائه لأي مطور أو AI Agent لتطوير النظام بالكامل!** 🚀
