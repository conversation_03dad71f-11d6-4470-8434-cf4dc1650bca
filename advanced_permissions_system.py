"""
نظام الصلاحيات المتقدم والفئات السعرية
Advanced Permissions System & Price Categories
"""

import streamlit as st
from datetime import datetime

# نظام الصلاحيات المتقدم
PERMISSIONS_SYSTEM = {
    "customers": {
        "add": "إضافة عملاء",
        "view": "عرض العملاء", 
        "edit": "تعديل العملاء",
        "delete": "حذف العملاء",
        "search": "البحث في العملاء",
        "export": "تصدير بيانات العملاء"
    },
    "services": {
        "add": "إضافة خدمات",
        "view": "عرض الخدمات",
        "edit": "تعديل الخدمات", 
        "delete": "حذف الخدمات",
        "search": "البحث في الخدمات",
        "pricing": "تعديل الأسعار"
    },
    "products": {
        "add": "إضافة منتجات",
        "view": "عرض المنتجات",
        "edit": "تعديل المنتجات",
        "delete": "حذف المنتجات", 
        "search": "البحث في المنتجات",
        "inventory": "إدارة المخزون"
    },
    "appointments": {
        "add": "حجز مواعيد",
        "view": "عرض المواعيد",
        "edit": "تعديل المواعيد",
        "delete": "إلغاء المواعيد",
        "search": "البحث في المواعيد"
    },
    "invoices": {
        "add": "إنشاء فواتير",
        "view": "عرض الفواتير",
        "edit": "تعديل الفواتير",
        "delete": "حذف الفواتير",
        "print": "طباعة الفواتير",
        "export": "تصدير الفواتير"
    },
    "employees": {
        "add": "إضافة موظفين",
        "view": "عرض الموظفين",
        "edit": "تعديل الموظفين",
        "delete": "حذف الموظفين",
        "permissions": "إدارة الصلاحيات"
    },
    "branches": {
        "add": "إضافة فروع",
        "view": "عرض الفروع",
        "edit": "تعديل الفروع",
        "delete": "حذف الفروع",
        "access": "الوصول للفروع"
    },
    "reports": {
        "sales": "تقارير المبيعات",
        "customers": "تقارير العملاء",
        "employees": "تقارير الموظفين",
        "financial": "التقارير المالية",
        "export": "تصدير التقارير"
    },
    "settings": {
        "general": "الإعدادات العامة",
        "pricing": "إعدادات الأسعار",
        "backup": "النسخ الاحتياطي",
        "users": "إدارة المستخدمين"
    }
}

# الفئات السعرية
PRICE_CATEGORIES = {
    "premium": {
        "name": "فئة بريميوم",
        "description": "أسعار مرتفعة للفروع الراقية",
        "multiplier": 1.3,
        "services": {
            "قص شعر بروفيشنال": 260,
            "دقن ستايلنج": 195,
            "صبغة شعر كاملة": 390,
            "استشوار برو": 260,
            "مكواة كيرلي بروفيشنال": 650,
            "بشرة لايت": 650,
            "هيدرو فيشال": 2600,
            "أكسچنيو الفاخرة": 3900,
            "باديكير يد وقدم": 650,
            "باديكير عناية كاملة + مساج": 910,
            "كولاجين للشعر": 1950,
            "بوتوكس للشعر": 2340,
            "كيراتين للشعر": 2600
        },
        "products": {
            "شامبو لوريال": 156,
            "كريم فرد الشعر": 325,
            "صبغة شعر احترافية": 455,
            "كريم تنظيف البشرة": 130,
            "ماسك الوجه": 104,
            "مكواة شعر احترافية": 1560,
            "مجفف شعر": 1170,
            "أحمر شفاه": 195,
            "كريم أساس": 260
        }
    },
    "standard": {
        "name": "فئة عادية",
        "description": "الأسعار الأساسية",
        "multiplier": 1.0,
        "services": {
            "قص شعر بروفيشنال": 200,
            "دقن ستايلنج": 150,
            "صبغة شعر كاملة": 300,
            "استشوار برو": 200,
            "مكواة كيرلي بروفيشنال": 500,
            "بشرة لايت": 500,
            "هيدرو فيشال": 2000,
            "أكسچنيو الفاخرة": 3000,
            "باديكير يد وقدم": 500,
            "باديكير عناية كاملة + مساج": 700,
            "كولاجين للشعر": 1500,
            "بوتوكس للشعر": 1800,
            "كيراتين للشعر": 2000
        },
        "products": {
            "شامبو لوريال": 120,
            "كريم فرد الشعر": 250,
            "صبغة شعر احترافية": 350,
            "كريم تنظيف البشرة": 100,
            "ماسك الوجه": 80,
            "مكواة شعر احترافية": 1200,
            "مجفف شعر": 900,
            "أحمر شفاه": 150,
            "كريم أساس": 200
        }
    },
    "economy": {
        "name": "فئة اقتصادية",
        "description": "أسعار مخفضة للفروع الشعبية",
        "multiplier": 0.8,
        "services": {
            "قص شعر بروفيشنال": 160,
            "دقن ستايلنج": 120,
            "صبغة شعر كاملة": 240,
            "استشوار برو": 160,
            "مكواة كيرلي بروفيشنال": 400,
            "بشرة لايت": 400,
            "هيدرو فيشال": 1600,
            "أكسچنيو الفاخرة": 2400,
            "باديكير يد وقدم": 400,
            "باديكير عناية كاملة + مساج": 560,
            "كولاجين للشعر": 1200,
            "بوتوكس للشعر": 1440,
            "كيراتين للشعر": 1600
        },
        "products": {
            "شامبو لوريال": 96,
            "كريم فرد الشعر": 200,
            "صبغة شعر احترافية": 280,
            "كريم تنظيف البشرة": 80,
            "ماسك الوجه": 64,
            "مكواة شعر احترافية": 960,
            "مجفف شعر": 720,
            "أحمر شفاه": 120,
            "كريم أساس": 160
        }
    }
}

# ربط الفروع بالفئات السعرية
BRANCH_PRICE_CATEGORIES = {
    1: "premium",    # فرع مدينة نصر - فئة بريميوم
    2: "standard",   # فرع جسر السويس - فئة عادية
    3: "economy",    # فرع الزهراء - فئة اقتصادية
    0: "standard"    # الإدارة العامة - فئة عادية
}

# أدوار المستخدمين مع الصلاحيات
USER_ROLES = {
    "super_admin": {
        "name": "مدير عام",
        "permissions": "all",
        "branches": "all"
    },
    "branch_manager": {
        "name": "مدير فرع",
        "permissions": [
            "customers.add", "customers.view", "customers.edit", "customers.search",
            "services.view", "services.search", "services.pricing",
            "products.view", "products.search", "products.inventory",
            "appointments.add", "appointments.view", "appointments.edit", "appointments.search",
            "invoices.add", "invoices.view", "invoices.edit", "invoices.print",
            "employees.view", "employees.edit",
            "reports.sales", "reports.customers", "reports.employees"
        ],
        "branches": "assigned"
    },
    "cashier": {
        "name": "كاشير",
        "permissions": [
            "customers.view", "customers.search",
            "services.view", "services.search",
            "products.view", "products.search",
            "appointments.view", "appointments.search",
            "invoices.add", "invoices.view", "invoices.print"
        ],
        "branches": "assigned"
    },
    "specialist": {
        "name": "متخصص",
        "permissions": [
            "customers.view", "customers.search",
            "services.view", "services.search",
            "appointments.view", "appointments.edit",
            "invoices.view"
        ],
        "branches": "assigned"
    },
    "receptionist": {
        "name": "موظف استقبال",
        "permissions": [
            "customers.add", "customers.view", "customers.search",
            "appointments.add", "appointments.view", "appointments.edit", "appointments.search",
            "services.view", "services.search"
        ],
        "branches": "assigned"
    }
}

def check_permission(user_permissions, required_permission):
    """فحص الصلاحية"""
    if user_permissions == "all":
        return True
    
    if isinstance(user_permissions, list):
        return required_permission in user_permissions
    
    return False

def get_branch_prices(branch_id, item_type="services"):
    """الحصول على أسعار الفرع"""
    category = BRANCH_PRICE_CATEGORIES.get(branch_id, "standard")
    return PRICE_CATEGORIES[category][item_type]

def get_user_accessible_branches(user_role, assigned_branches=None):
    """الحصول على الفروع المتاحة للمستخدم"""
    role_info = USER_ROLES.get(user_role, {})
    
    if role_info.get("branches") == "all":
        return [0, 1, 2, 3]  # جميع الفروع
    elif role_info.get("branches") == "assigned":
        return assigned_branches or []
    else:
        return []

def create_user_permissions_interface():
    """واجهة إدارة صلاحيات المستخدمين"""
    st.subheader("🔐 إدارة صلاحيات المستخدمين")
    
    # اختيار المستخدم
    col1, col2 = st.columns(2)
    
    with col1:
        username = st.text_input("اسم المستخدم")
        user_role = st.selectbox("الدور", list(USER_ROLES.keys()))
        
    with col2:
        # اختيار الفروع المتاحة
        available_branches = {
            "الإدارة العامة": 0,
            "فرع مدينة نصر": 1,
            "فرع جسر السويس": 2,
            "فرع الزهراء": 3
        }
        
        selected_branches = st.multiselect(
            "الفروع المتاحة",
            list(available_branches.keys()),
            help="اختر الفروع التي يمكن للمستخدم الوصول إليها"
        )
    
    # عرض الصلاحيات حسب الدور
    st.subheader("📋 الصلاحيات المتاحة")
    
    role_info = USER_ROLES.get(user_role, {})
    permissions = role_info.get("permissions", [])
    
    if permissions == "all":
        st.success("🔓 صلاحيات كاملة - يمكن الوصول لجميع الوظائف")
    else:
        # عرض الصلاحيات بشكل منظم
        for module, module_permissions in PERMISSIONS_SYSTEM.items():
            with st.expander(f"📁 {module}"):
                for perm_key, perm_name in module_permissions.items():
                    full_permission = f"{module}.{perm_key}"
                    has_permission = full_permission in permissions
                    
                    if has_permission:
                        st.success(f"✅ {perm_name}")
                    else:
                        st.error(f"❌ {perm_name}")
    
    return {
        "username": username,
        "role": user_role,
        "branches": [available_branches[b] for b in selected_branches],
        "permissions": permissions
    }

def create_price_category_interface():
    """واجهة إدارة الفئات السعرية"""
    st.subheader("💰 إدارة الفئات السعرية")
    
    # عرض الفئات السعرية
    col1, col2, col3 = st.columns(3)
    
    for i, (category_key, category_info) in enumerate(PRICE_CATEGORIES.items()):
        col = [col1, col2, col3][i]
        
        with col:
            st.write(f"**{category_info['name']}**")
            st.write(category_info['description'])
            st.metric("معامل السعر", f"{category_info['multiplier']}x")
            
            # عرض بعض الأسعار كمثال
            st.write("**أمثلة على الأسعار:**")
            sample_services = list(category_info['services'].items())[:3]
            for service, price in sample_services:
                st.write(f"• {service}: {price} ج.م")
    
    st.divider()
    
    # ربط الفروع بالفئات
    st.subheader("🏢 ربط الفروع بالفئات السعرية")
    
    branch_names = {
        0: "الإدارة العامة",
        1: "فرع مدينة نصر", 
        2: "فرع جسر السويس",
        3: "فرع الزهراء"
    }
    
    for branch_id, branch_name in branch_names.items():
        current_category = BRANCH_PRICE_CATEGORIES.get(branch_id, "standard")
        category_name = PRICE_CATEGORIES[current_category]["name"]
        
        col1, col2 = st.columns([2, 1])
        with col1:
            st.write(f"**{branch_name}**")
        with col2:
            st.info(f"📊 {category_name}")

if __name__ == "__main__":
    st.title("🔧 نظام الصلاحيات والفئات السعرية")
    
    tab1, tab2 = st.tabs(["إدارة الصلاحيات", "الفئات السعرية"])
    
    with tab1:
        user_data = create_user_permissions_interface()
        
        if st.button("حفظ إعدادات المستخدم"):
            st.success("تم حفظ إعدادات المستخدم بنجاح!")
            st.json(user_data)
    
    with tab2:
        create_price_category_interface()
