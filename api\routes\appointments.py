"""
مسارات المواعيد
Appointments Routes
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from database import get_db
from models.appointment import Appointment
from models.base import User
from api.routes.auth import get_current_user

router = APIRouter()

@router.get("/")
async def get_appointments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة المواعيد"""
    appointments = db.query(Appointment).all()
    return appointments
