"""
مسارات الفروع
Branches Routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from database import get_db
from models.all_models import Branch, User
from schemas.base import Branch as BranchSchema, BranchCreate, BranchUpdate
from api.routes.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=List[BranchSchema])
async def get_branches(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة الفروع"""
    branches = db.query(Branch).offset(skip).limit(limit).all()
    return branches

@router.get("/{branch_id}", response_model=BranchSchema)
async def get_branch(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على فرع محدد"""
    branch = db.query(Branch).filter(Branch.branch_id == branch_id).first()
    if not branch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الفرع غير موجود"
        )
    return branch

@router.post("/", response_model=BranchSchema)
async def create_branch(
    branch: BranchCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء فرع جديد"""
    db_branch = Branch(**branch.dict())
    db.add(db_branch)
    db.commit()
    db.refresh(db_branch)
    return db_branch

@router.put("/{branch_id}", response_model=BranchSchema)
async def update_branch(
    branch_id: int,
    branch_update: BranchUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث فرع"""
    db_branch = db.query(Branch).filter(Branch.branch_id == branch_id).first()
    if not db_branch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الفرع غير موجود"
        )
    
    update_data = branch_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_branch, field, value)
    
    db.commit()
    db.refresh(db_branch)
    return db_branch

@router.delete("/{branch_id}")
async def delete_branch(
    branch_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف فرع"""
    db_branch = db.query(Branch).filter(Branch.branch_id == branch_id).first()
    if not db_branch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الفرع غير موجود"
        )
    
    db.delete(db_branch)
    db.commit()
    return {"message": "تم حذف الفرع بنجاح"}
