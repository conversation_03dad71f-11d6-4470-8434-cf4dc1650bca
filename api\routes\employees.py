"""
مسارات الموظفين
Employees Routes
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from database import get_db
from models.employee import Employee
from models.base import User
from api.routes.auth import get_current_user

router = APIRouter()

@router.get("/")
async def get_employees(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة الموظفين"""
    employees = db.query(Employee).all()
    return employees
