"""
مسارات الخدمات
Services Routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from database import get_db
from models.service import Service
from models.base import User
from api.routes.auth import get_current_user

router = APIRouter()

# سنحتاج لإنشاء مخططات الخدمات لاحقاً
@router.get("/")
async def get_services(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة الخدمات"""
    services = db.query(Service).all()
    return services
