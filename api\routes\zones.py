"""
مسارات المناطق
Zones Routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from database import get_db
from models.all_models import Zone, User
from schemas.base import Zone as ZoneSchema, ZoneCreate
from api.routes.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=List[ZoneSchema])
async def get_zones(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة المناطق"""
    zones = db.query(Zone).all()
    return zones

@router.post("/", response_model=ZoneSchema)
async def create_zone(
    zone: ZoneCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء منطقة جديدة"""
    db_zone = Zone(**zone.dict())
    db.add(db_zone)
    db.commit()
    db.refresh(db_zone)
    return db_zone
