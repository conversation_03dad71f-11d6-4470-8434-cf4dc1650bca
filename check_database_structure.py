#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات الحقيقي
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_table_structure(table_name):
    """فحص هيكل جدول معين"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print(f"\n🔍 فحص هيكل جدول {table_name}:")
        print("-" * 50)
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = ?
        """, (table_name,))
        
        if cursor.fetchone()[0] == 0:
            print(f"❌ الجدول {table_name} غير موجود")
            return False
        
        # جلب أعمدة الجدول
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ?
            ORDER BY ORDINAL_POSITION
        """, (table_name,))
        
        columns = cursor.fetchall()
        
        if columns:
            print(f"✅ الجدول {table_name} موجود مع {len(columns)} عمود:")
            for col in columns:
                nullable = "يقبل NULL" if col[2] == "YES" else "لا يقبل NULL"
                default = f"افتراضي: {col[3]}" if col[3] else "بدون قيمة افتراضية"
                print(f"   • {col[0]} ({col[1]}) - {nullable} - {default}")
            
            # عرض عينة من البيانات
            try:
                cursor.execute(f"SELECT TOP 3 * FROM {table_name}")
                sample_data = cursor.fetchall()
                
                if sample_data:
                    print(f"\n📊 عينة من البيانات ({len(sample_data)} سجل):")
                    for i, row in enumerate(sample_data, 1):
                        print(f"   السجل {i}: {list(row)}")
                else:
                    print("\n📊 الجدول فارغ")
            except Exception as e:
                print(f"\n⚠️ لا يمكن جلب عينة البيانات: {str(e)}")
        else:
            print(f"❌ لا يمكن جلب أعمدة الجدول {table_name}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول {table_name}: {str(e)}")
        return False

def list_all_tables():
    """عرض جميع الجداول في قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("📋 جميع الجداول في قاعدة البيانات:")
        print("=" * 50)
        
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)
        
        tables = cursor.fetchall()
        
        if tables:
            for i, table in enumerate(tables, 1):
                print(f"{i:2d}. {table[0]}")
        else:
            print("❌ لا توجد جداول في قاعدة البيانات")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جلب قائمة الجداول: {str(e)}")
        return False

def create_test_invoice_directly():
    """إنشاء فاتورة تجريبية مباشرة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("\n🔄 إنشاء فاتورة تجريبية مباشرة...")
        
        # إدراج فاتورة تجريبية
        cursor.execute("""
            INSERT INTO invoices (
                customer_id, branch_id, employee_id, invoice_date,
                subtotal, tax_amount, discount_amount, total_amount,
                payment_method, payment_status, responsible_employee, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            1,  # customer_id
            1,  # branch_id
            1,  # employee_id
            '2025-01-07',  # invoice_date
            200.00,  # subtotal
            0.00,   # tax_amount
            20.00,  # discount_amount
            180.00,  # total_amount
            'نقدي',  # payment_method
            'مدفوعة',  # payment_status
            'أحمد محمد',  # responsible_employee
            'فاتورة تجريبية مباشرة'  # notes
        ))
        
        # الحصول على معرف الفاتورة
        cursor.execute("SELECT SCOPE_IDENTITY()")
        result = cursor.fetchone()
        invoice_id = int(result[0]) if result and result[0] else None
        
        if invoice_id:
            print(f"✅ تم إنشاء فاتورة برقم {invoice_id}")
            
            # إضافة عناصر للفاتورة
            items = [
                ('service', 'S001', 'قص شعر رجالي', 1, 50.00, 50.00, 'أحمد محمد', 7.50),
                ('service', 'S002', 'حلاقة ذقن', 1, 25.00, 25.00, 'أحمد محمد', 3.75),
                ('product', 'P001', 'شامبو للشعر', 2, 30.00, 60.00, 'غير محدد', 0.00),
                ('product', 'P002', 'كريم للشعر', 1, 35.00, 35.00, 'غير محدد', 0.00)
            ]
            
            for item in items:
                cursor.execute("""
                    INSERT INTO invoice_items (
                        invoice_id, item_type, item_code, item_name,
                        quantity, unit_price, total_price, employee_name, commission_amount
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (invoice_id,) + item)
            
            print(f"✅ تم إضافة {len(items)} عنصر للفاتورة")
            
            # إضافة المعاملة للخزنة
            cursor.execute("""
                INSERT INTO cash_register (
                    transaction_type, amount, description, reference_type, 
                    reference_id, branch_id, created_by, payment_method, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'income',  # transaction_type
                180.00,    # amount
                f'فاتورة رقم {invoice_id} - عميل تجريبي',  # description
                'invoice', # reference_type
                invoice_id, # reference_id
                1,         # branch_id
                'admin',   # created_by
                'نقدي',    # payment_method
                'دفعة نقدية من فاتورة تجريبية'  # notes
            ))
            
            print("✅ تم إضافة المعاملة للخزنة")
            
            conn.commit()
            print(f"🎉 تم حفظ الفاتورة {invoice_id} بالكامل!")
        else:
            print("❌ فشل في الحصول على معرف الفاتورة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفاتورة التجريبية: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 فحص هيكل قاعدة البيانات")
    print("=" * 60)
    
    # عرض جميع الجداول
    list_all_tables()
    
    # فحص الجداول المهمة
    important_tables = ['customers', 'Customers', 'invoices', 'invoice_items', 'cash_register', 'branches', 'employees']
    
    for table in important_tables:
        check_table_structure(table)
    
    print("\n" + "=" * 60)
    
    # إنشاء فاتورة تجريبية
    if create_test_invoice_directly():
        print("\n✅ تم إنشاء فاتورة تجريبية بنجاح!")
    else:
        print("\n❌ فشل في إنشاء فاتورة تجريبية!")
    
    print("\n" + "=" * 60)
    print("🏁 انتهى فحص قاعدة البيانات")
    
    input("\nاضغط Enter للخروج...")
