#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص حالة النظام والتأكد من وجود جميع التحديثات
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_tables_status():
    """فحص حالة الجداول"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 فحص حالة الجداول...")
        
        # قائمة الجداول المطلوبة
        required_tables = [
            'invoices', 'Customers', 'employees', 'branches', 
            'categories', 'items', 'sequences', 'cash_register', 'invoice_items'
        ]
        
        existing_tables = []
        cursor.execute("""
            SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)
        
        for table in cursor.fetchall():
            existing_tables.append(table[0])
        
        print("📋 الجداول الموجودة:")
        for table in required_tables:
            if table in existing_tables:
                # عدد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count} سجل")
            else:
                print(f"   ❌ {table}: غير موجود")
        
        # فحص جدول items بالتفصيل
        if 'items' in existing_tables:
            print("\n📦 تفاصيل جدول items:")
            cursor.execute("SELECT COUNT(*) FROM items WHERE item_type = 'service'")
            services = cursor.fetchone()[0]
            cursor.execute("SELECT COUNT(*) FROM items WHERE item_type = 'product'")
            products = cursor.fetchone()[0]
            print(f"   🔧 الخدمات: {services}")
            print(f"   📦 المنتجات: {products}")
            
            # عينة من الأصناف
            cursor.execute("""
                SELECT TOP 5 item_code, item_name, item_type, price_tier_2
                FROM items ORDER BY item_code
            """)
            sample_items = cursor.fetchall()
            print("   📋 عينة من الأصناف:")
            for item in sample_items:
                print(f"      • {item[0]} - {item[1]} ({item[2]}) - {item[3]} ج.م")
        
        # فحص الأكواد التسلسلية
        if 'sequences' in existing_tables:
            print("\n🔢 الأرقام التسلسلية:")
            cursor.execute("SELECT sequence_name, current_value, prefix FROM sequences ORDER BY sequence_name")
            sequences = cursor.fetchall()
            for seq in sequences:
                print(f"   • {seq[0]}: {seq[2]}{seq[1]:06d}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {str(e)}")
        return False

def check_stored_procedures():
    """فحص الإجراءات المخزنة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("\n🔧 فحص الإجراءات المخزنة...")
        
        # فحص وجود GetNextSequence
        cursor.execute("""
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES 
            WHERE ROUTINE_NAME = 'GetNextSequence' AND ROUTINE_TYPE = 'PROCEDURE'
        """)
        
        if cursor.fetchone()[0] > 0:
            print("   ✅ GetNextSequence: موجود")
            
            # اختبار الإجراء
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence 'customer_code', NULL, @next_code OUTPUT;
                SELECT @next_code;
            """)
            result = cursor.fetchone()
            if result and result[0] != 'ERROR':
                print(f"   ✅ اختبار التوليد: {result[0]}")
            else:
                print("   ❌ اختبار التوليد: فشل")
        else:
            print("   ❌ GetNextSequence: غير موجود")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الإجراءات: {str(e)}")
        return False

def test_items_search():
    """اختبار البحث في الأصناف"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("\n🔍 اختبار البحث في الأصناف...")
        
        # البحث بكلمة "شعر"
        cursor.execute("""
            SELECT item_code, item_name, item_type, price_tier_2
            FROM items 
            WHERE is_active = 1 AND is_deleted = 0
            AND (item_code LIKE '%شعر%' OR item_name LIKE '%شعر%')
            ORDER BY item_code
        """)
        
        results = cursor.fetchall()
        print(f"   🎯 نتائج البحث عن 'شعر': {len(results)} نتيجة")
        for result in results[:3]:
            print(f"      • {result[0]} - {result[1]} ({result[2]}) - {result[3]} ج.م")
        
        # البحث بكود S001
        cursor.execute("""
            SELECT item_code, item_name, item_type, price_tier_2
            FROM items 
            WHERE is_active = 1 AND is_deleted = 0
            AND item_code = 'S001'
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"   🎯 البحث بكود S001: {result[0]} - {result[1]}")
        else:
            print("   ❌ البحث بكود S001: لا توجد نتائج")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث: {str(e)}")
        return False

def check_recent_invoices():
    """فحص الفواتير الحديثة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("\n📄 فحص الفواتير الحديثة...")
        
        cursor.execute("""
            SELECT TOP 5 
                ISNULL(invoice_code, 'بدون كود') as invoice_code,
                total_amount, 
                ISNULL(responsible_employee, 'غير محدد') as responsible_employee,
                created_at
            FROM invoices 
            ORDER BY created_at DESC
        """)
        
        invoices = cursor.fetchall()
        print(f"   📊 عدد الفواتير: {len(invoices)}")
        
        for invoice in invoices:
            print(f"      • {invoice[0]} - {invoice[1]} ج.م - {invoice[2]} - {invoice[3]}")
        
        # فحص الأكواد التسلسلية في الفواتير
        cursor.execute("""
            SELECT COUNT(*) FROM invoices WHERE invoice_code IS NOT NULL AND invoice_code != ''
        """)
        coded_invoices = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        
        print(f"   🔢 فواتير بأكواد: {coded_invoices}/{total_invoices}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الفواتير: {str(e)}")
        return False

def fix_missing_data():
    """إصلاح البيانات المفقودة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("\n🔧 إصلاح البيانات المفقودة...")
        
        # التأكد من وجود جدول items
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'items')
            BEGIN
                PRINT 'إنشاء جدول items...'
                CREATE TABLE items (
                    item_id INT IDENTITY(1,1) PRIMARY KEY,
                    item_code NVARCHAR(20) UNIQUE NOT NULL,
                    item_name NVARCHAR(100) NOT NULL,
                    item_type NVARCHAR(20) NOT NULL CHECK (item_type IN ('service', 'product')),
                    description NVARCHAR(500),
                    category_id INT,
                    price_tier_1 DECIMAL(10,2) DEFAULT 0,
                    price_tier_2 DECIMAL(10,2) DEFAULT 0,
                    price_tier_3 DECIMAL(10,2) DEFAULT 0,
                    cost_price DECIMAL(10,2) DEFAULT 0,
                    stock_quantity INT DEFAULT 0,
                    min_stock_level INT DEFAULT 5,
                    duration_minutes INT DEFAULT 30,
                    commission_rate DECIMAL(5,2) DEFAULT 15.00,
                    is_active BIT DEFAULT 1,
                    created_at DATETIME DEFAULT GETDATE(),
                    updated_at DATETIME DEFAULT GETDATE(),
                    created_by NVARCHAR(100),
                    is_deleted BIT DEFAULT 0
                )
            END
        """)
        
        # إضافة أصناف افتراضية إذا لم تكن موجودة
        cursor.execute("SELECT COUNT(*) FROM items")
        items_count = cursor.fetchone()[0]
        
        if items_count == 0:
            print("   📦 إضافة أصناف افتراضية...")
            
            # خدمات افتراضية
            services = [
                ('S001', 'قص شعر رجالي', 'قص شعر عادي للرجال', 80.00, 60.00, 40.00),
                ('S002', 'قص شعر نسائي', 'قص شعر عادي للسيدات', 120.00, 100.00, 80.00),
                ('S003', 'صبغة شعر', 'صبغة شعر كاملة', 200.00, 150.00, 100.00),
                ('S004', 'مكياج عادي', 'مكياج يومي', 150.00, 120.00, 90.00),
                ('S005', 'مكياج عروس', 'مكياج خاص للعرائس', 500.00, 400.00, 300.00)
            ]
            
            for service in services:
                cursor.execute("""
                    INSERT INTO items (item_code, item_name, item_type, description, 
                                     price_tier_1, price_tier_2, price_tier_3, 
                                     duration_minutes, commission_rate, created_by)
                    VALUES (?, ?, 'service', ?, ?, ?, ?, 30, 15.00, 'system')
                """, service)
            
            # منتجات افتراضية
            products = [
                ('P001', 'شامبو للشعر الجاف', 'شامبو مرطب للشعر الجاف', 80.00, 60.00, 40.00),
                ('P002', 'كريم للشعر', 'كريم مغذي للشعر', 100.00, 80.00, 60.00),
                ('P003', 'زيت للشعر', 'زيت طبيعي للشعر', 120.00, 100.00, 80.00),
                ('P004', 'مزيل مكياج', 'مزيل مكياج لطيف', 60.00, 45.00, 30.00),
                ('P005', 'كريم مرطب', 'كريم مرطب للوجه', 150.00, 120.00, 90.00)
            ]
            
            for product in products:
                cursor.execute("""
                    INSERT INTO items (item_code, item_name, item_type, description, 
                                     price_tier_1, price_tier_2, price_tier_3, 
                                     stock_quantity, created_by)
                    VALUES (?, ?, 'product', ?, ?, ?, ?, 50, 'system')
                """, product)
            
            print("   ✅ تم إضافة 10 أصناف افتراضية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 فحص حالة نظام SalonProManager")
    print("=" * 50)
    
    # فحص الجداول
    if check_tables_status():
        print("✅ فحص الجداول مكتمل")
    
    # فحص الإجراءات المخزنة
    if check_stored_procedures():
        print("✅ فحص الإجراءات مكتمل")
    
    # اختبار البحث
    if test_items_search():
        print("✅ اختبار البحث مكتمل")
    
    # فحص الفواتير
    if check_recent_invoices():
        print("✅ فحص الفواتير مكتمل")
    
    # إصلاح البيانات المفقودة
    if fix_missing_data():
        print("✅ إصلاح البيانات مكتمل")
    
    print("\n" + "=" * 50)
    print("🎉 فحص النظام مكتمل!")
    print("💡 إذا كانت هناك مشاكل، تم إصلاحها تلقائياً")
    print("🔄 أعد تشغيل النظام لرؤية التحديثات")
    
    input("\nاضغط Enter للخروج...")
