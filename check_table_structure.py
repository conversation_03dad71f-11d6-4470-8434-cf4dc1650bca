#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل جداول قاعدة البيانات
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_table_structure(table_name):
    """فحص هيكل جدول محدد"""
    try:
        conn = get_db_connection()
        if conn is None:
            return

        cursor = conn.cursor()
        
        # جلب معلومات الأعمدة
        query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
        """
        
        cursor.execute(query, table_name)
        columns = cursor.fetchall()
        
        if columns:
            print(f"📋 هيكل جدول {table_name}:")
            print("-" * 80)
            print(f"{'اسم العمود':<20} {'نوع البيانات':<15} {'يقبل NULL':<10} {'القيمة الافتراضية':<20} {'الطول':<10}")
            print("-" * 80)
            
            for col in columns:
                column_name = col[0]
                data_type = col[1]
                is_nullable = "نعم" if col[2] == "YES" else "لا"
                default_value = col[3] if col[3] else "بدون"
                max_length = col[4] if col[4] else "غير محدد"
                
                print(f"{column_name:<20} {data_type:<15} {is_nullable:<10} {str(default_value):<20} {str(max_length):<10}")
        else:
            print(f"❌ الجدول {table_name} غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص جدول {table_name}: {str(e)}")

def check_all_tables():
    """فحص جميع الجداول"""
    try:
        conn = get_db_connection()
        if conn is None:
            return

        cursor = conn.cursor()
        
        # جلب أسماء جميع الجداول
        query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """
        
        cursor.execute(query)
        tables = cursor.fetchall()
        
        if tables:
            print("📊 الجداول الموجودة في قاعدة البيانات:")
            for table in tables:
                print(f"   • {table[0]}")
            
            print("\n" + "="*80)
            
            # فحص كل جدول
            for table in tables:
                table_name = table[0]
                check_table_structure(table_name)
                print("\n" + "="*80)
        else:
            print("❌ لا توجد جداول في قاعدة البيانات")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {str(e)}")

if __name__ == "__main__":
    print("🔍 فحص هيكل قاعدة البيانات SalonProManager_DB")
    print("=" * 60)
    
    check_all_tables()
    
    input("\nاضغط Enter للخروج...")
