"""
فحص بنية الجداول
Check Table Structure
"""

import pyodbc

def check_table_structure():
    """فحص بنية الجداول"""
    
    # معلومات الاتصال
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    try:
        connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # فحص جدول العملاء
        print("=== جدول العملاء (customers) ===")
        cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'customers'")
        customer_columns = cursor.fetchall()
        for col in customer_columns:
            print(f"  {col[0]} - {col[1]}")
        
        # فحص جدول الفروع
        print("\n=== جدول الفروع (branches) ===")
        cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'branches'")
        branch_columns = cursor.fetchall()
        for col in branch_columns:
            print(f"  {col[0]} - {col[1]}")
        
        # فحص جدول المناطق
        print("\n=== جدول المناطق (zones) ===")
        cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'zones'")
        zone_columns = cursor.fetchall()
        for col in zone_columns:
            print(f"  {col[0]} - {col[1]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_table_structure()
