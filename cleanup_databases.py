#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت حذف قواعد البيانات غير المطلوبة
"""

import pyodbc

def get_master_connection():
    """الاتصال بقاعدة البيانات الرئيسية master"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=master;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات الرئيسية: {str(e)}")
        return None

def list_salon_databases():
    """عرض جميع قواعد البيانات التي تحتوي على كلمة salon (عدا SalonProManager_DB)"""
    try:
        conn = get_master_connection()
        if conn is None:
            return []

        cursor = conn.cursor()

        # البحث عن قواعد البيانات التي تحتوي على كلمة salon عدا SalonProManager_DB
        query = """
        SELECT name
        FROM sys.databases
        WHERE name LIKE '%salon%'
        AND name NOT IN ('master', 'tempdb', 'model', 'msdb', 'SalonProManager_DB')
        ORDER BY name
        """

        cursor.execute(query)
        databases = cursor.fetchall()

        salon_dbs = [db[0] for db in databases]
        conn.close()

        return salon_dbs

    except Exception as e:
        print(f"❌ خطأ في جلب قواعد البيانات: {str(e)}")
        return []

def drop_database(db_name):
    """حذف قاعدة بيانات محددة"""
    try:
        print(f"🔄 جاري حذف قاعدة البيانات: {db_name}")

        # الاتصال الأول - إنهاء الاتصالات النشطة
        conn1 = get_master_connection()
        if conn1 is None:
            return False

        conn1.autocommit = True  # تفعيل الـ autocommit على الاتصال
        cursor1 = conn1.cursor()

        # إنهاء جميع الاتصالات النشطة بقاعدة البيانات
        kill_connections_query = f"ALTER DATABASE [{db_name}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE"
        cursor1.execute(kill_connections_query)
        cursor1.close()
        conn1.close()

        # الاتصال الثاني - حذف قاعدة البيانات
        conn2 = get_master_connection()
        if conn2 is None:
            return False

        conn2.autocommit = True  # تفعيل الـ autocommit على الاتصال
        cursor2 = conn2.cursor()

        # حذف قاعدة البيانات
        drop_query = f"DROP DATABASE [{db_name}]"
        cursor2.execute(drop_query)

        cursor2.close()
        conn2.close()

        print(f"✅ تم حذف قاعدة البيانات: {db_name}")
        return True

    except Exception as e:
        print(f"❌ خطأ في حذف قاعدة البيانات {db_name}: {str(e)}")
        return False

def cleanup_salon_databases():
    """حذف جميع قواعد البيانات التي تحتوي على كلمة salon (عدا SalonProManager_DB)"""
    print("🔍 جاري البحث عن قواعد البيانات غير المطلوبة التي تحتوي على كلمة 'salon'...")
    print("💡 ملاحظة: سيتم الاحتفاظ بقاعدة البيانات SalonProManager_DB")

    salon_databases = list_salon_databases()

    if not salon_databases:
        print("✅ لا توجد قواعد بيانات غير مطلوبة تحتوي على كلمة 'salon'")
        print("✅ قاعدة البيانات SalonProManager_DB محفوظة ولن يتم حذفها")
        return

    print(f"📋 تم العثور على {len(salon_databases)} قاعدة بيانات غير مطلوبة:")
    for i, db_name in enumerate(salon_databases, 1):
        print(f"   {i}. {db_name}")

    print("\n⚠️ تحذير: سيتم حذف قواعد البيانات المذكورة أعلاه فقط!")
    print("✅ قاعدة البيانات SalonProManager_DB لن يتم حذفها")
    print("هذا الإجراء لا يمكن التراجع عنه!")

    # تأكيد العملية
    confirm = input("\nهل تريد المتابعة؟ اكتب 'نعم' للتأكيد: ")
    
    if confirm.lower() in ['نعم', 'yes', 'y']:
        print("\n🔄 بدء عملية الحذف...")
        
        success_count = 0
        failed_count = 0
        
        for db_name in salon_databases:
            if drop_database(db_name):
                success_count += 1
            else:
                failed_count += 1
        
        print("\n" + "="*50)
        print("📊 ملخص العملية:")
        print(f"✅ تم حذف {success_count} قاعدة بيانات بنجاح")
        if failed_count > 0:
            print(f"❌ فشل حذف {failed_count} قاعدة بيانات")
        print("="*50)
        
        if success_count > 0:
            print("🎉 تم تنظيف قواعد البيانات بنجاح!")
            print("💡 قاعدة البيانات الوحيدة المتبقية هي: SalonProManager_DB")
    else:
        print("❌ تم إلغاء العملية")

def list_all_databases():
    """عرض جميع قواعد البيانات الموجودة"""
    try:
        conn = get_master_connection()
        if conn is None:
            return

        cursor = conn.cursor()
        
        query = """
        SELECT name, create_date, collation_name
        FROM sys.databases 
        WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb')
        ORDER BY name
        """
        
        cursor.execute(query)
        databases = cursor.fetchall()
        
        if databases:
            print("📋 قواعد البيانات الموجودة:")
            print("-" * 60)
            for db in databases:
                print(f"📊 {db[0]} (تاريخ الإنشاء: {db[1]})")
        else:
            print("✅ لا توجد قواعد بيانات مخصصة")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في جلب قواعد البيانات: {str(e)}")

if __name__ == "__main__":
    print("🗄️ أداة تنظيف قواعد البيانات")
    print("=" * 40)
    
    # عرض جميع قواعد البيانات أولاً
    print("📋 قواعد البيانات الحالية:")
    list_all_databases()
    
    print("\n" + "=" * 40)
    
    # تنظيف قواعد البيانات التي تحتوي على salon
    cleanup_salon_databases()
    
    print("\n" + "=" * 40)
    print("📋 قواعد البيانات بعد التنظيف:")
    list_all_databases()
    
    input("\nاضغط Enter للخروج...")
