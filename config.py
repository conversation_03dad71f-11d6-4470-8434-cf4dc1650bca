"""
إعدادات النظام الأساسية
SalonProManager Configuration
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Database Configuration
    DATABASE_URL = os.getenv(
        "DATABASE_URL",
        "mssql+pyodbc://sa:@<EMAIL>,4100/SalonProManager?driver=ODBC+Driver+17+for+SQL+Server"
    )
    
    # Security
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    
    # WhatsApp API Configuration
    TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
    TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
    WHATSAPP_FROM = os.getenv("WHATSAPP_FROM", "whatsapp:+***********")
    
    # Application Settings
    APP_NAME = "SalonProManager"
    APP_VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"
    
    # File Upload Settings
    UPLOAD_FOLDER = "uploads"
    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
    
    # Pagination
    DEFAULT_PAGE_SIZE = 20
    MAX_PAGE_SIZE = 100
    
    # Languages
    SUPPORTED_LANGUAGES = ["ar", "en"]
    DEFAULT_LANGUAGE = "ar"

settings = Settings()
