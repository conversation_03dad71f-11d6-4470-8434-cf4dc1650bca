#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول الفئات وإصلاح مشكلة Categories
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_categories_table():
    """إنشاء جدول الفئات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء جدول categories...")
        
        # إنشاء جدول الفئات
        create_table_sql = """
        CREATE TABLE categories (
            category_id INT IDENTITY(1,1) PRIMARY KEY,
            category_name NVARCHAR(100) NOT NULL UNIQUE,
            description NVARCHAR(500),
            price_tier_1 DECIMAL(10,2) DEFAULT 0,  -- فئة بريميوم
            price_tier_2 DECIMAL(10,2) DEFAULT 0,  -- فئة عادية
            price_tier_3 DECIMAL(10,2) DEFAULT 0,  -- فئة اقتصادية
            is_active BIT DEFAULT 1,
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE(),
            created_by NVARCHAR(100),
            is_deleted BIT DEFAULT 0
        )
        """
        
        cursor.execute(create_table_sql)
        print("✅ تم إنشاء جدول categories بنجاح")
        
        # إدراج فئات افتراضية
        default_categories = [
            ('خدمات الشعر', 'جميع خدمات قص وتصفيف الشعر', 100.00, 80.00, 60.00),
            ('خدمات التجميل', 'خدمات المكياج والعناية بالبشرة', 150.00, 120.00, 90.00),
            ('منتجات العناية', 'منتجات العناية بالشعر والبشرة', 80.00, 60.00, 40.00),
            ('خدمات الأظافر', 'مانيكير وبديكير وعناية بالأظافر', 60.00, 45.00, 30.00),
            ('خدمات العرائس', 'خدمات خاصة للعرائس والمناسبات', 500.00, 400.00, 300.00),
            ('منتجات التصفيف', 'أدوات ومنتجات تصفيف الشعر', 120.00, 100.00, 80.00),
            ('خدمات الرجال', 'خدمات خاصة بالرجال', 80.00, 60.00, 40.00),
            ('منتجات طبيعية', 'منتجات طبيعية وعضوية', 200.00, 150.00, 100.00)
        ]
        
        for category in default_categories:
            cursor.execute("""
                INSERT INTO categories (category_name, description, price_tier_1, price_tier_2, price_tier_3, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            """, category + ('admin',))
        
        print(f"✅ تم إدراج {len(default_categories)} فئة افتراضية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول categories: {str(e)}")
        return False

def update_services_and_products_tables():
    """تحديث جداول الخدمات والمنتجات لتشمل الفئات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 تحديث جداول الخدمات والمنتجات...")
        
        # إضافة عمود category_id لجدول services إذا لم يكن موجود
        try:
            cursor.execute("""
                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'services' AND COLUMN_NAME = 'category_id'
                )
                BEGIN
                    ALTER TABLE services ADD category_id INT
                END
            """)
            print("✅ تم إضافة عمود category_id لجدول services")
        except Exception as e:
            print(f"⚠️ مشكلة في تحديث جدول services: {str(e)}")
        
        # إضافة عمود category_id لجدول products إذا لم يكن موجود
        try:
            cursor.execute("""
                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'category_id'
                )
                BEGIN
                    ALTER TABLE products ADD category_id INT
                END
            """)
            print("✅ تم إضافة عمود category_id لجدول products")
        except Exception as e:
            print(f"⚠️ مشكلة في تحديث جدول products: {str(e)}")
        
        # تحديث الخدمات الموجودة بفئات افتراضية
        try:
            cursor.execute("""
                UPDATE services 
                SET category_id = 1 
                WHERE category_id IS NULL AND service_name LIKE '%شعر%'
            """)
            
            cursor.execute("""
                UPDATE services 
                SET category_id = 2 
                WHERE category_id IS NULL AND service_name LIKE '%مكياج%'
            """)
            
            cursor.execute("""
                UPDATE services 
                SET category_id = 4 
                WHERE category_id IS NULL AND service_name LIKE '%مانيكير%'
            """)
            
            cursor.execute("""
                UPDATE services 
                SET category_id = 5 
                WHERE category_id IS NULL AND service_name LIKE '%عروس%'
            """)
            
            cursor.execute("""
                UPDATE services 
                SET category_id = 7 
                WHERE category_id IS NULL AND service_name LIKE '%رجال%'
            """)
            
            cursor.execute("""
                UPDATE services 
                SET category_id = 1 
                WHERE category_id IS NULL
            """)
            
            print("✅ تم تحديث فئات الخدمات")
        except Exception as e:
            print(f"⚠️ مشكلة في تحديث فئات الخدمات: {str(e)}")
        
        # تحديث المنتجات الموجودة بفئات افتراضية
        try:
            cursor.execute("""
                UPDATE products 
                SET category_id = 3 
                WHERE category_id IS NULL AND product_name LIKE '%شامبو%'
            """)
            
            cursor.execute("""
                UPDATE products 
                SET category_id = 3 
                WHERE category_id IS NULL AND product_name LIKE '%كريم%'
            """)
            
            cursor.execute("""
                UPDATE products 
                SET category_id = 6 
                WHERE category_id IS NULL AND product_name LIKE '%زيت%'
            """)
            
            cursor.execute("""
                UPDATE products 
                SET category_id = 8 
                WHERE category_id IS NULL AND product_name LIKE '%طبيعي%'
            """)
            
            cursor.execute("""
                UPDATE products 
                SET category_id = 3 
                WHERE category_id IS NULL
            """)
            
            print("✅ تم تحديث فئات المنتجات")
        except Exception as e:
            print(f"⚠️ مشكلة في تحديث فئات المنتجات: {str(e)}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الجداول: {str(e)}")
        return False

def test_categories_operations():
    """اختبار عمليات الفئات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 اختبار عمليات الفئات...")
        
        # عدد الفئات
        cursor.execute("SELECT COUNT(*) FROM categories")
        categories_count = cursor.fetchone()[0]
        print(f"📊 عدد الفئات: {categories_count}")
        
        # عرض الفئات
        cursor.execute("""
            SELECT category_id, category_name, price_tier_1, price_tier_2, price_tier_3
            FROM categories
            WHERE is_active = 1
            ORDER BY category_name
        """)
        
        categories = cursor.fetchall()
        
        if categories:
            print("📋 الفئات المتاحة:")
            for category in categories:
                print(f"   • {category[1]} - بريميوم: {category[2]} ج.م, عادي: {category[3]} ج.م, اقتصادي: {category[4]} ج.م")
        
        # اختبار إضافة فئة جديدة
        try:
            cursor.execute("""
                INSERT INTO categories (category_name, description, price_tier_1, price_tier_2, price_tier_3, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                'فئة تجريبية', 'فئة للاختبار', 100.00, 80.00, 60.00, 'admin'
            ))
            
            cursor.execute("SELECT @@IDENTITY")
            new_category_id = cursor.fetchone()[0]
            print(f"✅ تم إنشاء فئة تجريبية برقم {new_category_id}")
            
            # حذف الفئة التجريبية
            cursor.execute("DELETE FROM categories WHERE category_id = ?", (new_category_id,))
            print("✅ تم حذف الفئة التجريبية")
            
        except Exception as e:
            print(f"⚠️ مشكلة في اختبار إضافة الفئة: {str(e)}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفئات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🏗️ إنشاء وإعداد نظام الفئات")
    print("=" * 50)
    
    # إنشاء جدول الفئات
    if create_categories_table():
        print("✅ جدول الفئات جاهز")
    
    # تحديث جداول الخدمات والمنتجات
    if update_services_and_products_tables():
        print("✅ تحديث الجداول مكتمل")
    
    # اختبار العمليات
    if test_categories_operations():
        print("✅ اختبار الفئات نجح")
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد نظام الفئات بنجاح!")
    print("💡 الآن يمكن:")
    print("   • إضافة فئات جديدة")
    print("   • تحديد أسعار متدرجة للفئات")
    print("   • ربط الخدمات والمنتجات بالفئات")
    print("   • إدارة الفئات من واجهة النظام")
    
    input("\nاضغط Enter للخروج...")
