"""
إنشاء الجداول المطلوبة للنظام المصري
Create Required Tables for Egyptian System
"""

import pyodbc

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
    return pyodbc.connect(connection_string)

def create_services_table():
    """إنشاء جدول الخدمات"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول الخدمات
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='services' AND xtype='U')
        CREATE TABLE services (
            service_id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            description NVARCHAR(500),
            price DECIMAL(10,2) NOT NULL,
            duration INT NOT NULL, -- بالدقائق
            commission_type NVARCHAR(20) DEFAULT 'percentage',
            commission_value DECIMAL(10,2) DEFAULT 0,
            is_active BIT DEFAULT 1,
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE()
        )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إنشاء جدول الخدمات")

def create_employees_table():
    """إنشاء جدول الموظفين"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول الموظفين
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='employees' AND xtype='U')
        CREATE TABLE employees (
            employee_id INT IDENTITY(1,1) PRIMARY KEY,
            first_name NVARCHAR(50) NOT NULL,
            last_name NVARCHAR(50),
            phone NVARCHAR(20),
            email NVARCHAR(100),
            position NVARCHAR(50),
            specialization NVARCHAR(100),
            hire_date DATE DEFAULT GETDATE(),
            salary DECIMAL(10,2) DEFAULT 0,
            commission_rate DECIMAL(5,2) DEFAULT 0,
            is_active BIT DEFAULT 1,
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE()
        )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إنشاء جدول الموظفين")

def create_appointments_table():
    """إنشاء جدول المواعيد"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول المواعيد
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='appointments' AND xtype='U')
        CREATE TABLE appointments (
            appointment_id INT IDENTITY(1,1) PRIMARY KEY,
            customer_id INT,
            employee_id INT,
            branch_id INT,
            service_id INT,
            appointment_date DATE NOT NULL,
            appointment_time TIME NOT NULL,
            duration INT DEFAULT 60,
            status NVARCHAR(20) DEFAULT 'scheduled',
            notes NVARCHAR(500),
            total_price DECIMAL(10,2) DEFAULT 0,
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
        )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إنشاء جدول المواعيد")

def create_products_table():
    """إنشاء جدول المنتجات"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول المنتجات
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='products' AND xtype='U')
        CREATE TABLE products (
            product_id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            description NVARCHAR(500),
            category NVARCHAR(50),
            brand NVARCHAR(50),
            price DECIMAL(10,2) NOT NULL,
            cost DECIMAL(10,2) DEFAULT 0,
            stock_quantity INT DEFAULT 0,
            min_stock_level INT DEFAULT 5,
            barcode NVARCHAR(50),
            is_active BIT DEFAULT 1,
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE()
        )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إنشاء جدول المنتجات")

def create_invoices_table():
    """إنشاء جدول الفواتير"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول الفواتير
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='invoices' AND xtype='U')
        CREATE TABLE invoices (
            invoice_id INT IDENTITY(1,1) PRIMARY KEY,
            customer_id INT,
            branch_id INT,
            employee_id INT,
            invoice_date DATE DEFAULT GETDATE(),
            subtotal DECIMAL(10,2) DEFAULT 0,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            discount_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) DEFAULT 0,
            payment_method NVARCHAR(20) DEFAULT 'cash',
            payment_status NVARCHAR(20) DEFAULT 'paid',
            notes NVARCHAR(500),
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
        )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إنشاء جدول الفواتير")

def create_invoice_items_table():
    """إنشاء جدول عناصر الفواتير"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # إنشاء جدول عناصر الفواتير
    cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='invoice_items' AND xtype='U')
        CREATE TABLE invoice_items (
            item_id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_id INT,
            service_id INT,
            product_id INT,
            item_type NVARCHAR(20) DEFAULT 'service', -- service or product
            quantity INT DEFAULT 1,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
        )
    """)
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إنشاء جدول عناصر الفواتير")

def main():
    """إنشاء جميع الجداول المطلوبة"""
    print("🔧 بدء إنشاء الجداول المطلوبة...")
    
    try:
        create_services_table()
        create_employees_table()
        create_appointments_table()
        create_products_table()
        create_invoices_table()
        create_invoice_items_table()
        
        print("\n🎉 تم إنشاء جميع الجداول بنجاح!")
        print("📊 الجداول المنشأة:")
        print("   - جدول الخدمات (services)")
        print("   - جدول الموظفين (employees)")
        print("   - جدول المواعيد (appointments)")
        print("   - جدول المنتجات (products)")
        print("   - جدول الفواتير (invoices)")
        print("   - جدول عناصر الفواتير (invoice_items)")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")

if __name__ == "__main__":
    main()
