#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جداول الفواتير في قاعدة البيانات
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_invoices_table():
    """إنشاء جدول الفواتير"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء جدول invoices...")
        
        # إنشاء جدول الفواتير
        create_invoices_sql = """
        CREATE TABLE invoices (
            invoice_id INT IDENTITY(1,1) PRIMARY KEY,
            customer_id INT,
            branch_id INT,
            employee_id INT,
            invoice_date DATE NOT NULL,
            subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
            tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_method NVARCHAR(50) NOT NULL DEFAULT 'نقدي',
            payment_status NVARCHAR(50) NOT NULL DEFAULT 'مدفوعة',
            notes NVARCHAR(MAX),
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE(),
            is_deleted BIT DEFAULT 0,
            deleted_at DATETIME,
            deleted_by NVARCHAR(100)
        )
        """
        
        cursor.execute(create_invoices_sql)
        print("✅ تم إنشاء جدول invoices بنجاح")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول invoices: {str(e)}")
        return False

def create_invoice_items_table():
    """إنشاء جدول عناصر الفواتير"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء جدول invoice_items...")
        
        # إنشاء جدول عناصر الفواتير
        create_items_sql = """
        CREATE TABLE invoice_items (
            item_id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_id INT NOT NULL,
            item_type NVARCHAR(20) NOT NULL, -- 'service' أو 'product'
            item_code NVARCHAR(20),
            item_name NVARCHAR(200) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            employee_name NVARCHAR(100),
            commission_amount DECIMAL(10,2) DEFAULT 0,
            created_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id) ON DELETE CASCADE
        )
        """
        
        cursor.execute(create_items_sql)
        print("✅ تم إنشاء جدول invoice_items بنجاح")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول invoice_items: {str(e)}")
        return False

def create_employees_table():
    """إنشاء جدول الموظفين إذا لم يكن موجوداً"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # التحقق من وجود جدول الموظفين
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'employees'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("🔄 إنشاء جدول employees...")
            
            create_employees_sql = """
            CREATE TABLE employees (
                employee_id INT IDENTITY(1,1) PRIMARY KEY,
                first_name NVARCHAR(100) NOT NULL,
                last_name NVARCHAR(100),
                phone NVARCHAR(20),
                email NVARCHAR(100),
                position NVARCHAR(100),
                branch_id INT,
                hire_date DATE,
                salary DECIMAL(10,2),
                commission_rate DECIMAL(5,2) DEFAULT 0,
                is_active BIT DEFAULT 1,
                created_at DATETIME DEFAULT GETDATE(),
                updated_at DATETIME DEFAULT GETDATE()
            )
            """
            
            cursor.execute(create_employees_sql)
            print("✅ تم إنشاء جدول employees بنجاح")
            
            # إدراج موظفين افتراضيين
            insert_employees_sql = """
            INSERT INTO employees (first_name, last_name, phone, position, branch_id, commission_rate)
            VALUES 
                ('أحمد', 'محمد', '01000000001', 'حلاق', 1, 15.0),
                ('فاطمة', 'علي', '01000000002', 'مصففة شعر', 1, 20.0),
                ('سارة', 'أحمد', '01000000003', 'أخصائية تجميل', 1, 25.0)
            """
            
            cursor.execute(insert_employees_sql)
            print("✅ تم إدراج موظفين افتراضيين")
        else:
            print("✅ جدول employees موجود بالفعل")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول employees: {str(e)}")
        return False

def create_branches_table():
    """إنشاء جدول الفروع إذا لم يكن موجوداً"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # التحقق من وجود جدول الفروع
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'branches'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("🔄 إنشاء جدول branches...")
            
            create_branches_sql = """
            CREATE TABLE branches (
                branch_id INT IDENTITY(1,1) PRIMARY KEY,
                branch_name NVARCHAR(100) NOT NULL,
                address NVARCHAR(200),
                phone NVARCHAR(20),
                manager_name NVARCHAR(100),
                is_active BIT DEFAULT 1,
                created_at DATETIME DEFAULT GETDATE()
            )
            """
            
            cursor.execute(create_branches_sql)
            print("✅ تم إنشاء جدول branches بنجاح")
            
            # إدراج فروع افتراضية
            insert_branches_sql = """
            INSERT INTO branches (branch_name, address, phone, manager_name)
            VALUES 
                ('الفرع الرئيسي', 'مدينة نصر، القاهرة', '01090829393', 'أحمد مدير'),
                ('فرع جسر السويس', 'جسر السويس، القاهرة', '01090829394', 'محمد مدير'),
                ('فرع الزهراء', 'مدينة الزهراء، القاهرة', '01090829395', 'فاطمة مدير')
            """
            
            cursor.execute(insert_branches_sql)
            print("✅ تم إدراج فروع افتراضية")
        else:
            print("✅ جدول branches موجود بالفعل")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول branches: {str(e)}")
        return False

def insert_sample_invoice():
    """إدراج فاتورة تجريبية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إدراج فاتورة تجريبية...")
        
        # إدراج فاتورة تجريبية
        insert_invoice_sql = """
        INSERT INTO invoices (
            customer_id, branch_id, employee_id, invoice_date,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_method, payment_status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_invoice_sql, (
            1,  # customer_id
            1,  # branch_id
            1,  # employee_id
            '2025-01-07',  # invoice_date
            100.00,  # subtotal
            0.00,   # tax_amount
            10.00,  # discount_amount
            90.00,  # total_amount
            'نقدي',  # payment_method
            'مدفوعة',  # payment_status
            'فاتورة تجريبية'  # notes
        ))
        
        # الحصول على معرف الفاتورة
        invoice_id = cursor.lastrowid
        
        # إدراج عناصر الفاتورة
        insert_items_sql = """
        INSERT INTO invoice_items (
            invoice_id, item_type, item_code, item_name,
            quantity, unit_price, total_price, employee_name, commission_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        # عنصر 1: خدمة قص شعر
        cursor.execute(insert_items_sql, (
            invoice_id, 'service', 'S001', 'قص شعر رجالي',
            1, 50.00, 50.00, 'أحمد محمد', 7.50
        ))
        
        # عنصر 2: منتج شامبو
        cursor.execute(insert_items_sql, (
            invoice_id, 'product', 'P001', 'شامبو للشعر الجاف',
            2, 25.00, 50.00, 'غير محدد', 0.00
        ))
        
        conn.commit()
        print(f"✅ تم إدراج فاتورة تجريبية برقم {invoice_id}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إدراج فاتورة تجريبية: {str(e)}")
        return False

if __name__ == "__main__":
    print("🏗️ إنشاء جداول نظام الفواتير")
    print("=" * 50)
    
    # إنشاء جدول الفروع
    if create_branches_table():
        print("✅ جدول الفروع جاهز")
    
    # إنشاء جدول الموظفين
    if create_employees_table():
        print("✅ جدول الموظفين جاهز")
    
    # إنشاء جدول الفواتير
    if create_invoices_table():
        print("✅ جدول الفواتير جاهز")
    
    # إنشاء جدول عناصر الفواتير
    if create_invoice_items_table():
        print("✅ جدول عناصر الفواتير جاهز")
    
    # إدراج فاتورة تجريبية
    if insert_sample_invoice():
        print("✅ فاتورة تجريبية جاهزة")
    
    print("\n" + "=" * 50)
    print("🎉 تم إنشاء جميع جداول نظام الفواتير بنجاح!")
    print("💡 يمكنك الآن استخدام نظام الفواتير في التطبيق")
    
    input("\nاضغط Enter للخروج...")
