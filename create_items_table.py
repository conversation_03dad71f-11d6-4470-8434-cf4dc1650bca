#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول items وإصلاح مشكلة الأصناف
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_items_table():
    """إنشاء جدول items (الأصناف الموحد)"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء جدول items...")
        
        # إنشاء جدول الأصناف الموحد (يجمع الخدمات والمنتجات)
        create_table_sql = """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'items')
        BEGIN
            CREATE TABLE items (
                item_id INT IDENTITY(1,1) PRIMARY KEY,
                item_code NVARCHAR(20) UNIQUE NOT NULL,
                item_name NVARCHAR(100) NOT NULL,
                item_type NVARCHAR(20) NOT NULL CHECK (item_type IN ('service', 'product')),
                description NVARCHAR(500),
                category_id INT,
                
                -- أسعار متدرجة (3 مستويات)
                price_tier_1 DECIMAL(10,2) DEFAULT 0,  -- بريميوم
                price_tier_2 DECIMAL(10,2) DEFAULT 0,  -- عادي
                price_tier_3 DECIMAL(10,2) DEFAULT 0,  -- اقتصادي
                
                -- للمنتجات فقط
                cost_price DECIMAL(10,2) DEFAULT 0,
                stock_quantity INT DEFAULT 0,
                min_stock_level INT DEFAULT 5,
                
                -- للخدمات فقط
                duration_minutes INT DEFAULT 30,
                commission_rate DECIMAL(5,2) DEFAULT 15.00,  -- نسبة العمولة
                
                -- معلومات عامة
                is_active BIT DEFAULT 1,
                created_at DATETIME DEFAULT GETDATE(),
                updated_at DATETIME DEFAULT GETDATE(),
                created_by NVARCHAR(100),
                is_deleted BIT DEFAULT 0,
                
                -- فهارس للبحث السريع
                INDEX IX_items_code (item_code),
                INDEX IX_items_type (item_type),
                INDEX IX_items_category (category_id),
                INDEX IX_items_active (is_active, is_deleted)
            )
        END
        """
        
        cursor.execute(create_table_sql)
        print("✅ تم إنشاء جدول items بنجاح")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول items: {str(e)}")
        return False

def migrate_existing_data():
    """نقل البيانات من جداول services و products إلى جدول items"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 نقل البيانات الموجودة...")
        
        # نقل الخدمات من جدول services
        try:
            cursor.execute("""
                INSERT INTO items (
                    item_code, item_name, item_type, description, category_id,
                    price_tier_1, price_tier_2, price_tier_3, duration_minutes,
                    commission_rate, is_active, created_by
                )
                SELECT 
                    ISNULL(service_code, 'S' + RIGHT('000' + CAST(service_id AS NVARCHAR), 3)) as item_code,
                    service_name as item_name,
                    'service' as item_type,
                    description,
                    category_id,
                    ISNULL(price_tier_1, 0) as price_tier_1,
                    ISNULL(price_tier_2, 0) as price_tier_2,
                    ISNULL(price_tier_3, 0) as price_tier_3,
                    ISNULL(duration_minutes, 30) as duration_minutes,
                    15.00 as commission_rate,
                    ISNULL(is_active, 1) as is_active,
                    'migration' as created_by
                FROM services
                WHERE NOT EXISTS (
                    SELECT 1 FROM items 
                    WHERE item_code = ISNULL(service_code, 'S' + RIGHT('000' + CAST(service_id AS NVARCHAR), 3))
                )
            """)
            
            services_migrated = cursor.rowcount
            print(f"✅ تم نقل {services_migrated} خدمة")
            
        except Exception as e:
            print(f"⚠️ مشكلة في نقل الخدمات: {str(e)}")
            services_migrated = 0
        
        # نقل المنتجات من جدول products
        try:
            cursor.execute("""
                INSERT INTO items (
                    item_code, item_name, item_type, description, category_id,
                    price_tier_1, price_tier_2, price_tier_3, cost_price,
                    stock_quantity, min_stock_level, is_active, created_by
                )
                SELECT 
                    ISNULL(product_code, 'P' + RIGHT('000' + CAST(product_id AS NVARCHAR), 3)) as item_code,
                    product_name as item_name,
                    'product' as item_type,
                    description,
                    category_id,
                    ISNULL(price_tier_1, 0) as price_tier_1,
                    ISNULL(price_tier_2, 0) as price_tier_2,
                    ISNULL(price_tier_3, 0) as price_tier_3,
                    ISNULL(cost_price, 0) as cost_price,
                    ISNULL(stock_quantity, 0) as stock_quantity,
                    ISNULL(min_stock_level, 5) as min_stock_level,
                    ISNULL(is_active, 1) as is_active,
                    'migration' as created_by
                FROM products
                WHERE NOT EXISTS (
                    SELECT 1 FROM items 
                    WHERE item_code = ISNULL(product_code, 'P' + RIGHT('000' + CAST(product_id AS NVARCHAR), 3))
                )
            """)
            
            products_migrated = cursor.rowcount
            print(f"✅ تم نقل {products_migrated} منتج")
            
        except Exception as e:
            print(f"⚠️ مشكلة في نقل المنتجات: {str(e)}")
            products_migrated = 0
        
        conn.commit()
        conn.close()
        
        total_migrated = services_migrated + products_migrated
        print(f"✅ إجمالي العناصر المنقولة: {total_migrated}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نقل البيانات: {str(e)}")
        return False

def add_default_items():
    """إضافة أصناف افتراضية إذا لم تكن موجودة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إضافة أصناف افتراضية...")
        
        # خدمات افتراضية
        default_services = [
            ('S001', 'قص شعر رجالي', 'قص شعر عادي للرجال', 1, 80.00, 60.00, 40.00, 30, 15.00),
            ('S002', 'قص شعر نسائي', 'قص شعر عادي للسيدات', 1, 120.00, 100.00, 80.00, 45, 15.00),
            ('S003', 'صبغة شعر', 'صبغة شعر كاملة', 1, 200.00, 150.00, 100.00, 120, 20.00),
            ('S004', 'مكياج عادي', 'مكياج يومي', 2, 150.00, 120.00, 90.00, 60, 15.00),
            ('S005', 'مكياج عروس', 'مكياج خاص للعرائس', 5, 500.00, 400.00, 300.00, 180, 25.00),
            ('S006', 'مانيكير', 'عناية بأظافر اليدين', 4, 60.00, 45.00, 30.00, 45, 10.00),
            ('S007', 'بديكير', 'عناية بأظافر القدمين', 4, 80.00, 60.00, 40.00, 60, 10.00),
            ('S008', 'فرد شعر', 'فرد الشعر بالكيراتين', 1, 300.00, 250.00, 200.00, 180, 20.00),
            ('S009', 'تنظيف بشرة', 'تنظيف عميق للبشرة', 2, 100.00, 80.00, 60.00, 90, 15.00),
            ('S010', 'حمام كريم', 'حمام كريم مغذي للشعر', 1, 50.00, 40.00, 30.00, 60, 10.00)
        ]
        
        # منتجات افتراضية
        default_products = [
            ('P001', 'شامبو للشعر الجاف', 'شامبو مرطب للشعر الجاف', 3, 80.00, 60.00, 40.00, 30.00, 50, 5),
            ('P002', 'كريم للشعر', 'كريم مغذي للشعر', 3, 100.00, 80.00, 60.00, 40.00, 30, 5),
            ('P003', 'زيت للشعر', 'زيت طبيعي للشعر', 6, 120.00, 100.00, 80.00, 50.00, 25, 5),
            ('P004', 'مزيل مكياج', 'مزيل مكياج لطيف', 3, 60.00, 45.00, 30.00, 20.00, 40, 10),
            ('P005', 'كريم مرطب', 'كريم مرطب للوجه', 3, 150.00, 120.00, 90.00, 60.00, 20, 5),
            ('P006', 'طلاء أظافر', 'طلاء أظافر عالي الجودة', 4, 40.00, 30.00, 20.00, 15.00, 100, 20),
            ('P007', 'صبغة شعر', 'صبغة شعر احترافية', 3, 180.00, 150.00, 120.00, 80.00, 15, 3),
            ('P008', 'كريم فرد', 'كريم فرد الشعر', 6, 250.00, 200.00, 150.00, 120.00, 10, 2),
            ('P009', 'ماسك للوجه', 'ماسك مرطب للوجه', 3, 90.00, 70.00, 50.00, 35.00, 30, 5),
            ('P010', 'سيروم للشعر', 'سيروم مغذي للشعر', 6, 200.00, 160.00, 120.00, 90.00, 20, 5)
        ]
        
        # إدراج الخدمات
        for service in default_services:
            cursor.execute("""
                IF NOT EXISTS (SELECT 1 FROM items WHERE item_code = ?)
                BEGIN
                    INSERT INTO items (
                        item_code, item_name, item_type, description, category_id,
                        price_tier_1, price_tier_2, price_tier_3, duration_minutes,
                        commission_rate, created_by
                    ) VALUES (?, ?, 'service', ?, ?, ?, ?, ?, ?, ?, 'default')
                END
            """, (service[0], service[0], service[1], service[2], service[3], 
                  service[4], service[5], service[6], service[7], service[8]))
        
        print(f"✅ تم إدراج {len(default_services)} خدمة افتراضية")
        
        # إدراج المنتجات
        for product in default_products:
            cursor.execute("""
                IF NOT EXISTS (SELECT 1 FROM items WHERE item_code = ?)
                BEGIN
                    INSERT INTO items (
                        item_code, item_name, item_type, description, category_id,
                        price_tier_1, price_tier_2, price_tier_3, cost_price,
                        stock_quantity, min_stock_level, created_by
                    ) VALUES (?, ?, 'product', ?, ?, ?, ?, ?, ?, ?, ?, 'default')
                END
            """, (product[0], product[0], product[1], product[2], product[3], 
                  product[4], product[5], product[6], product[7], product[8], product[9]))
        
        print(f"✅ تم إدراج {len(default_products)} منتج افتراضي")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الأصناف الافتراضية: {str(e)}")
        return False

def test_items_operations():
    """اختبار عمليات جدول items"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 اختبار عمليات جدول items...")
        
        # عدد الأصناف
        cursor.execute("SELECT COUNT(*) FROM items")
        total_items = cursor.fetchone()[0]
        print(f"📊 إجمالي الأصناف: {total_items}")
        
        # عدد الخدمات
        cursor.execute("SELECT COUNT(*) FROM items WHERE item_type = 'service'")
        services_count = cursor.fetchone()[0]
        print(f"🛠️ الخدمات: {services_count}")
        
        # عدد المنتجات
        cursor.execute("SELECT COUNT(*) FROM items WHERE item_type = 'product'")
        products_count = cursor.fetchone()[0]
        print(f"📦 المنتجات: {products_count}")
        
        # عرض عينة من الأصناف
        cursor.execute("""
            SELECT TOP 5 item_code, item_name, item_type, price_tier_2
            FROM items
            WHERE is_active = 1
            ORDER BY item_code
        """)
        
        sample_items = cursor.fetchall()
        
        if sample_items:
            print("📋 عينة من الأصناف:")
            for item in sample_items:
                print(f"   • {item[0]} - {item[1]} ({item[2]}) - {item[3]} ج.م")
        
        # اختبار البحث
        cursor.execute("""
            SELECT item_code, item_name, price_tier_2
            FROM items
            WHERE item_name LIKE '%شعر%' AND is_active = 1
            ORDER BY item_code
        """)
        
        hair_items = cursor.fetchall()
        print(f"🔍 أصناف الشعر: {len(hair_items)} صنف")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العمليات: {str(e)}")
        return False

def update_sequences_for_items():
    """تحديث الأرقام التسلسلية للأصناف"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 تحديث الأرقام التسلسلية للأصناف...")
        
        # تحديث sequence للخدمات
        cursor.execute("""
            UPDATE sequences 
            SET current_value = (
                SELECT ISNULL(MAX(CAST(SUBSTRING(item_code, 2, 10) AS INT)), 100)
                FROM items 
                WHERE item_type = 'service' AND item_code LIKE 'S%'
            )
            WHERE sequence_name = 'service_code'
        """)
        
        # تحديث sequence للمنتجات
        cursor.execute("""
            UPDATE sequences 
            SET current_value = (
                SELECT ISNULL(MAX(CAST(SUBSTRING(item_code, 2, 10) AS INT)), 100)
                FROM items 
                WHERE item_type = 'product' AND item_code LIKE 'P%'
            )
            WHERE sequence_name = 'product_code'
        """)
        
        print("✅ تم تحديث الأرقام التسلسلية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الأرقام التسلسلية: {str(e)}")
        return False

if __name__ == "__main__":
    print("🏗️ إنشاء وإعداد جدول الأصناف الموحد")
    print("=" * 60)
    
    # إنشاء جدول items
    if create_items_table():
        print("✅ جدول items تم إنشاؤه")
    
    # نقل البيانات الموجودة
    if migrate_existing_data():
        print("✅ تم نقل البيانات الموجودة")
    
    # إضافة أصناف افتراضية
    if add_default_items():
        print("✅ تم إضافة الأصناف الافتراضية")
    
    # تحديث الأرقام التسلسلية
    if update_sequences_for_items():
        print("✅ تم تحديث الأرقام التسلسلية")
    
    # اختبار العمليات
    if test_items_operations():
        print("✅ اختبار العمليات نجح")
    
    print("\n" + "=" * 60)
    print("🎉 تم إعداد جدول الأصناف بنجاح!")
    print("💡 الآن يمكن:")
    print("   • إضافة خدمات ومنتجات في جدول موحد")
    print("   • البحث في جميع الأصناف بسهولة")
    print("   • إدارة الأسعار المتدرجة")
    print("   • تتبع المخزون للمنتجات")
    print("   • حساب العمولات للخدمات")
    
    input("\nاضغط Enter للخروج...")
