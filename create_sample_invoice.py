#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء فاتورة تجريبية مع عناصرها
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_sample_invoice():
    """إنشاء فاتورة تجريبية مع عناصرها"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء فاتورة تجريبية...")
        
        # إدراج فاتورة تجريبية
        insert_invoice_sql = """
        INSERT INTO invoices (
            customer_id, branch_id, employee_id, invoice_date,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_method, payment_status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_invoice_sql, (
            1,  # customer_id
            1,  # branch_id
            1,  # employee_id
            datetime.now().date(),  # invoice_date
            150.00,  # subtotal
            0.00,   # tax_amount
            15.00,  # discount_amount
            135.00,  # total_amount
            'نقدي',  # payment_method
            'مدفوعة',  # payment_status
            'فاتورة تجريبية - نظام SalonProManager'  # notes
        ))
        
        # الحصول على معرف الفاتورة باستخدام SCOPE_IDENTITY
        cursor.execute("SELECT SCOPE_IDENTITY()")
        result = cursor.fetchone()
        invoice_id = int(result[0]) if result and result[0] else None
        
        if not invoice_id:
            print("❌ فشل في الحصول على معرف الفاتورة")
            return False
        
        print(f"✅ تم إنشاء الفاتورة برقم {invoice_id}")
        
        # إدراج عناصر الفاتورة
        print("🔄 إضافة عناصر الفاتورة...")
        
        insert_items_sql = """
        INSERT INTO invoice_items (
            invoice_id, item_type, item_code, item_name,
            quantity, unit_price, total_price, employee_name, commission_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        # العناصر التجريبية
        items = [
            {
                'type': 'service',
                'code': 'S001',
                'name': 'قص شعر رجالي',
                'quantity': 1,
                'unit_price': 50.00,
                'total_price': 50.00,
                'employee': 'أحمد محمد',
                'commission': 7.50
            },
            {
                'type': 'service',
                'code': 'S002',
                'name': 'حلاقة ذقن',
                'quantity': 1,
                'unit_price': 25.00,
                'total_price': 25.00,
                'employee': 'أحمد محمد',
                'commission': 3.75
            },
            {
                'type': 'product',
                'code': 'P001',
                'name': 'شامبو للشعر الجاف',
                'quantity': 1,
                'unit_price': 45.00,
                'total_price': 45.00,
                'employee': 'غير محدد',
                'commission': 0.00
            },
            {
                'type': 'product',
                'code': 'P002',
                'name': 'كريم للشعر',
                'quantity': 1,
                'unit_price': 30.00,
                'total_price': 30.00,
                'employee': 'غير محدد',
                'commission': 0.00
            }
        ]
        
        # إدراج كل عنصر
        for item in items:
            cursor.execute(insert_items_sql, (
                invoice_id,
                item['type'],
                item['code'],
                item['name'],
                item['quantity'],
                item['unit_price'],
                item['total_price'],
                item['employee'],
                item['commission']
            ))
            print(f"   ✅ تم إضافة: {item['name']} ({item['type']})")
        
        conn.commit()
        print(f"\n🎉 تم إنشاء فاتورة كاملة برقم {invoice_id} مع {len(items)} عنصر!")
        
        # عرض تفاصيل الفاتورة
        print("\n📋 تفاصيل الفاتورة:")
        cursor.execute("SELECT * FROM invoices WHERE invoice_id = ?", (invoice_id,))
        invoice = cursor.fetchone()
        
        if invoice:
            print(f"   • رقم الفاتورة: {invoice[0]}")
            print(f"   • معرف العميل: {invoice[1]}")
            print(f"   • معرف الفرع: {invoice[2]}")
            print(f"   • تاريخ الفاتورة: {invoice[4]}")
            print(f"   • المجموع الفرعي: {invoice[5]} ج.م")
            print(f"   • الخصم: {invoice[7]} ج.م")
            print(f"   • الإجمالي: {invoice[8]} ج.م")
            print(f"   • طريقة الدفع: {invoice[9]}")
            print(f"   • حالة الدفع: {invoice[10]}")
        
        # عرض عناصر الفاتورة
        print("\n🛍️ عناصر الفاتورة:")
        cursor.execute("SELECT * FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
        items_result = cursor.fetchall()
        
        for item in items_result:
            print(f"   • {item[4]} - {item[3]} - الكمية: {item[5]} - السعر: {item[6]} ج.م - الإجمالي: {item[7]} ج.م")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفاتورة التجريبية: {str(e)}")
        return False

def test_invoice_system():
    """اختبار نظام الفواتير"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 اختبار نظام الفواتير...")
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        invoice_count = cursor.fetchone()[0]
        print(f"📊 عدد الفواتير: {invoice_count}")
        
        # عدد عناصر الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoice_items")
        items_count = cursor.fetchone()[0]
        print(f"🛍️ عدد عناصر الفواتير: {items_count}")
        
        # إجمالي المبيعات
        cursor.execute("SELECT SUM(total_amount) FROM invoices WHERE is_deleted = 0")
        total_sales = cursor.fetchone()[0] or 0
        print(f"💰 إجمالي المبيعات: {total_sales} ج.م")
        
        # آخر فاتورة
        cursor.execute("""
            SELECT TOP 1 invoice_id, total_amount, payment_method, created_at 
            FROM invoices 
            ORDER BY created_at DESC
        """)
        last_invoice = cursor.fetchone()
        
        if last_invoice:
            print(f"📄 آخر فاتورة: رقم {last_invoice[0]} - {last_invoice[1]} ج.م - {last_invoice[2]} - {last_invoice[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧾 إنشاء فاتورة تجريبية مع عناصرها")
    print("=" * 60)
    
    # إنشاء فاتورة تجريبية
    if create_sample_invoice():
        print("\n✅ تم إنشاء الفاتورة التجريبية بنجاح!")
    else:
        print("\n❌ فشل في إنشاء الفاتورة التجريبية!")
    
    print("\n" + "=" * 60)
    
    # اختبار النظام
    if test_invoice_system():
        print("\n✅ نظام الفواتير يعمل بشكل صحيح!")
    else:
        print("\n❌ هناك مشكلة في نظام الفواتير!")
    
    print("\n" + "=" * 60)
    print("🎯 النظام جاهز لإنشاء الفواتير!")
    print("💡 يمكنك الآن استخدام صفحة الفواتير في التطبيق")
    
    input("\nاضغط Enter للخروج...")
