"""
إنشاء الجداول مباشرة في SQL Server
Create Tables Directly in SQL Server
"""

import pyodbc
from passlib.context import CryptContext

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    """تشفير كلمة المرور"""
    return pwd_context.hash(password)

def create_tables_and_data():
    """إنشاء الجداول والبيانات الأولية"""
    
    # معلومات الاتصال
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    try:
        connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
        
        print("🔗 الاتصال بقاعدة البيانات...")
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # إنشاء الجداول
        print("🏗️ إنشاء الجداول...")
        
        # جدول الأدوار
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='roles' AND xtype='U')
            CREATE TABLE roles (
                role_id INT IDENTITY(1,1) PRIMARY KEY,
                name NVARCHAR(50) NOT NULL UNIQUE,
                description NVARCHAR(255)
            )
        """)
        
        # جدول الفروع
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='branches' AND xtype='U')
            CREATE TABLE branches (
                branch_id INT IDENTITY(1,1) PRIMARY KEY,
                name NVARCHAR(100) NOT NULL,
                address NVARCHAR(255),
                phone NVARCHAR(20),
                working_hours NVARCHAR(255),
                is_active BIT DEFAULT 1,
                created_at DATETIME DEFAULT GETDATE(),
                updated_at DATETIME DEFAULT GETDATE()
            )
        """)
        
        # جدول المستخدمين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
            CREATE TABLE users (
                user_id INT IDENTITY(1,1) PRIMARY KEY,
                username NVARCHAR(50) NOT NULL UNIQUE,
                password_hash NVARCHAR(255) NOT NULL,
                full_name NVARCHAR(100) NOT NULL,
                email NVARCHAR(100),
                phone NVARCHAR(20),
                role_id INT FOREIGN KEY REFERENCES roles(role_id),
                is_active BIT DEFAULT 1,
                created_at DATETIME DEFAULT GETDATE(),
                updated_at DATETIME DEFAULT GETDATE()
            )
        """)
        
        # جدول المناطق
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='zones' AND xtype='U')
            CREATE TABLE zones (
                zone_id INT IDENTITY(1,1) PRIMARY KEY,
                name NVARCHAR(100) NOT NULL,
                description NVARCHAR(255)
            )
        """)
        
        # جدول العملاء
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='customers' AND xtype='U')
            CREATE TABLE customers (
                customer_id INT IDENTITY(1,1) PRIMARY KEY,
                first_name NVARCHAR(50) NOT NULL,
                last_name NVARCHAR(50) NOT NULL,
                phone NVARCHAR(20) NOT NULL,
                email NVARCHAR(100),
                birth_date DATE,
                zone_id INT FOREIGN KEY REFERENCES zones(zone_id),
                notes NTEXT,
                created_at DATETIME DEFAULT GETDATE(),
                updated_at DATETIME DEFAULT GETDATE()
            )
        """)
        
        conn.commit()
        print("✅ تم إنشاء الجداول بنجاح!")
        
        # إدراج البيانات الأولية
        print("📊 إدراج البيانات الأولية...")
        
        # إدراج الأدوار
        roles_data = [
            ("مدير نظام", "صلاحيات كاملة لجميع الفروع"),
            ("مدير فرع", "صلاحيات كاملة للفرع المحدد"),
            ("موظف استقبال", "إدارة المواعيد والعملاء"),
            ("خبير تجميل", "تقديم الخدمات وإدارة المواعيد الخاصة")
        ]
        
        for role_name, role_desc in roles_data:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM roles WHERE name = ?)
                INSERT INTO roles (name, description) VALUES (?, ?)
            """, role_name, role_name, role_desc)
        
        # إدراج فرع افتراضي
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM branches WHERE name = N'الفرع الرئيسي')
            INSERT INTO branches (name, address, phone, working_hours) 
            VALUES (N'الفرع الرئيسي', N'الرياض، المملكة العربية السعودية', '+966501234567', N'9:00 ص - 10:00 م')
        """)
        
        # إدراج مستخدم افتراضي (مدير النظام)
        admin_password_hash = get_password_hash("admin123")
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
            INSERT INTO users (username, password_hash, full_name, email, role_id) 
            VALUES ('admin', ?, N'مدير النظام', '<EMAIL>', 1)
        """, admin_password_hash)
        
        # إدراج مناطق افتراضية
        zones_data = [
            ("الرياض", "منطقة الرياض"),
            ("جدة", "منطقة جدة"),
            ("الدمام", "منطقة الدمام"),
            ("مكة المكرمة", "منطقة مكة المكرمة"),
            ("المدينة المنورة", "منطقة المدينة المنورة")
        ]
        
        for zone_name, zone_desc in zones_data:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM zones WHERE name = ?)
                INSERT INTO zones (name, description) VALUES (?, ?)
            """, zone_name, zone_name, zone_desc)
        
        conn.commit()
        print("✅ تم إدراج البيانات الأولية بنجاح!")
        
        cursor.close()
        conn.close()
        
        print("\n" + "="*60)
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
        print("بيانات تسجيل الدخول الافتراضية:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🏢 SalonProManager - إعداد قاعدة البيانات")
    print("="*60)
    create_tables_and_data()
