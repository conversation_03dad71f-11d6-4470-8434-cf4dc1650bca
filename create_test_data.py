#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء بيانات تجريبية شاملة في قاعدة البيانات
"""

import pyodbc
from datetime import datetime, timedelta
import random

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_test_customers():
    """إنشاء عملاء تجريبيين"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء عملاء تجريبيين...")
        
        # حذف العملاء الموجودين
        cursor.execute("DELETE FROM customers")
        
        customers = [
            ('أحمد', 'محمد', '01012345678', '<EMAIL>', 1),
            ('فاطمة', 'علي', '01023456789', '<EMAIL>', 1),
            ('محمد', 'حسن', '01034567890', '<EMAIL>', 1),
            ('سارة', 'أحمد', '01045678901', '<EMAIL>', 2),
            ('نور', 'محمود', '01056789012', '<EMAIL>', 2),
            ('ليلى', 'حسام', '01067890123', '<EMAIL>', 3),
            ('مريم', 'سالم', '01078901234', '<EMAIL>', 3),
            ('هدى', 'عبدالله', '01089012345', '<EMAIL>', 1)
        ]
        
        for customer in customers:
            cursor.execute("""
                INSERT INTO customers (first_name, last_name, phone, email, branch_id)
                VALUES (?, ?, ?, ?, ?)
            """, customer)
        
        conn.commit()
        print(f"✅ تم إنشاء {len(customers)} عميل تجريبي")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء العملاء: {str(e)}")
        return False

def create_test_services():
    """إنشاء خدمات تجريبية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء خدمات تجريبية...")
        
        # التحقق من وجود جدول الخدمات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'services')
            BEGIN
                CREATE TABLE services (
                    service_id INT IDENTITY(1,1) PRIMARY KEY,
                    service_code NVARCHAR(20) UNIQUE,
                    service_name NVARCHAR(200) NOT NULL,
                    description NVARCHAR(500),
                    price_premium DECIMAL(10,2) DEFAULT 0,
                    price_standard DECIMAL(10,2) DEFAULT 0,
                    price_economy DECIMAL(10,2) DEFAULT 0,
                    duration_minutes INT DEFAULT 30,
                    commission_rate DECIMAL(5,2) DEFAULT 15.0,
                    is_active BIT DEFAULT 1,
                    created_at DATETIME DEFAULT GETDATE()
                )
            END
        """)
        
        # حذف الخدمات الموجودة
        cursor.execute("DELETE FROM services")
        
        services = [
            ('S001', 'قص شعر رجالي', 'قص شعر عادي للرجال', 60.00, 50.00, 40.00, 30, 15.0),
            ('S002', 'قص شعر نسائي', 'قص شعر عادي للسيدات', 80.00, 70.00, 60.00, 45, 20.0),
            ('S003', 'صبغة شعر', 'صبغة شعر كاملة', 200.00, 180.00, 150.00, 120, 25.0),
            ('S004', 'حلاقة ذقن', 'حلاقة وتهذيب الذقن', 30.00, 25.00, 20.00, 20, 10.0),
            ('S005', 'تسريحة عروس', 'تسريحة خاصة للعرائس', 500.00, 400.00, 300.00, 180, 30.0),
            ('S006', 'مكياج', 'مكياج كامل', 150.00, 120.00, 100.00, 60, 20.0),
            ('S007', 'عناية بالبشرة', 'جلسة عناية بالبشرة', 120.00, 100.00, 80.00, 90, 18.0),
            ('S008', 'مانيكير', 'عناية بالأظافر', 50.00, 40.00, 30.00, 45, 15.0)
        ]
        
        for service in services:
            cursor.execute("""
                INSERT INTO services (service_code, service_name, description, 
                                    price_premium, price_standard, price_economy, 
                                    duration_minutes, commission_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, service)
        
        conn.commit()
        print(f"✅ تم إنشاء {len(services)} خدمة تجريبية")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الخدمات: {str(e)}")
        return False

def create_test_products():
    """إنشاء منتجات تجريبية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء منتجات تجريبية...")
        
        # التحقق من وجود جدول المنتجات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'products')
            BEGIN
                CREATE TABLE products (
                    product_id INT IDENTITY(1,1) PRIMARY KEY,
                    product_code NVARCHAR(20) UNIQUE,
                    product_name NVARCHAR(200) NOT NULL,
                    description NVARCHAR(500),
                    price_premium DECIMAL(10,2) DEFAULT 0,
                    price_standard DECIMAL(10,2) DEFAULT 0,
                    price_economy DECIMAL(10,2) DEFAULT 0,
                    stock_quantity INT DEFAULT 0,
                    min_stock_level INT DEFAULT 5,
                    is_active BIT DEFAULT 1,
                    created_at DATETIME DEFAULT GETDATE()
                )
            END
        """)
        
        # حذف المنتجات الموجودة
        cursor.execute("DELETE FROM products")
        
        products = [
            ('P001', 'شامبو للشعر الجاف', 'شامبو مرطب للشعر الجاف', 60.00, 50.00, 40.00, 50),
            ('P002', 'بلسم للشعر', 'بلسم مغذي للشعر', 45.00, 35.00, 25.00, 30),
            ('P003', 'كريم للشعر', 'كريم تصفيف الشعر', 40.00, 30.00, 20.00, 25),
            ('P004', 'زيت للشعر', 'زيت طبيعي للشعر', 80.00, 70.00, 60.00, 20),
            ('P005', 'ماسك للوجه', 'ماسك مرطب للوجه', 100.00, 80.00, 60.00, 15),
            ('P006', 'كريم للوجه', 'كريم مرطب للوجه', 120.00, 100.00, 80.00, 20),
            ('P007', 'مزيل مكياج', 'مزيل مكياج لطيف', 50.00, 40.00, 30.00, 35),
            ('P008', 'أحمر شفاه', 'أحمر شفاه طويل الأمد', 80.00, 60.00, 40.00, 40)
        ]
        
        for product in products:
            cursor.execute("""
                INSERT INTO products (product_code, product_name, description, 
                                    price_premium, price_standard, price_economy, stock_quantity)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, product)
        
        conn.commit()
        print(f"✅ تم إنشاء {len(products)} منتج تجريبي")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المنتجات: {str(e)}")
        return False

def create_test_invoices():
    """إنشاء فواتير تجريبية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء فواتير تجريبية...")
        
        # حذف الفواتير الموجودة
        cursor.execute("DELETE FROM invoice_items")
        cursor.execute("DELETE FROM invoices")
        cursor.execute("DELETE FROM cash_register WHERE reference_type = 'invoice'")
        
        # إنشاء فواتير لآخر 7 أيام
        for i in range(10):
            # تاريخ عشوائي في آخر 7 أيام
            invoice_date = datetime.now() - timedelta(days=random.randint(0, 7))
            
            # عميل عشوائي (1-8)
            customer_id = random.randint(1, 8)
            
            # فرع عشوائي (1-3)
            branch_id = random.randint(1, 3)
            
            # موظف عشوائي (1-3)
            employee_id = random.randint(1, 3)
            
            # موظف مسؤول
            employees = ['أحمد محمد', 'فاطمة علي', 'سارة أحمد']
            responsible_employee = random.choice(employees)
            
            # مبالغ عشوائية
            subtotal = random.uniform(100, 500)
            discount_amount = random.uniform(0, subtotal * 0.1)
            tax_amount = 0  # ضريبة صفر كما طلبت
            total_amount = subtotal - discount_amount + tax_amount
            
            # طريقة دفع عشوائية
            payment_methods = ['نقدي', 'كارت', 'تحويل بنكي']
            payment_method = random.choice(payment_methods)
            
            # إدراج الفاتورة
            cursor.execute("""
                INSERT INTO invoices (
                    customer_id, branch_id, employee_id, invoice_date,
                    subtotal, tax_amount, discount_amount, total_amount,
                    payment_method, payment_status, responsible_employee, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                customer_id, branch_id, employee_id, invoice_date,
                subtotal, tax_amount, discount_amount, total_amount,
                payment_method, 'مدفوعة', responsible_employee,
                f'فاتورة تجريبية رقم {i+1}'
            ))
            
            # الحصول على معرف الفاتورة
            cursor.execute("SELECT SCOPE_IDENTITY()")
            invoice_id = int(cursor.fetchone()[0])
            
            # إضافة عناصر عشوائية للفاتورة
            num_items = random.randint(1, 4)
            current_total = 0
            
            for j in range(num_items):
                # اختيار عشوائي بين خدمة ومنتج
                if random.choice([True, False]):
                    # خدمة
                    service_id = random.randint(1, 8)
                    cursor.execute("SELECT service_code, service_name, price_standard FROM services WHERE service_id = ?", (service_id,))
                    service = cursor.fetchone()
                    
                    if service:
                        item_code = service[0]
                        item_name = service[1]
                        unit_price = float(service[2])
                        quantity = 1
                        total_price = unit_price * quantity
                        commission = total_price * 0.15
                        
                        cursor.execute("""
                            INSERT INTO invoice_items (
                                invoice_id, item_type, item_code, item_name,
                                quantity, unit_price, total_price, employee_name, commission_amount
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            invoice_id, 'service', item_code, item_name,
                            quantity, unit_price, total_price, responsible_employee, commission
                        ))
                        current_total += total_price
                else:
                    # منتج
                    product_id = random.randint(1, 8)
                    cursor.execute("SELECT product_code, product_name, price_standard FROM products WHERE product_id = ?", (product_id,))
                    product = cursor.fetchone()
                    
                    if product:
                        item_code = product[0]
                        item_name = product[1]
                        unit_price = float(product[2])
                        quantity = random.randint(1, 3)
                        total_price = unit_price * quantity
                        
                        cursor.execute("""
                            INSERT INTO invoice_items (
                                invoice_id, item_type, item_code, item_name,
                                quantity, unit_price, total_price, employee_name, commission_amount
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            invoice_id, 'product', item_code, item_name,
                            quantity, unit_price, total_price, 'غير محدد', 0
                        ))
                        current_total += total_price
            
            # إضافة المعاملة للخزنة إذا كان الدفع نقدي
            if payment_method == 'نقدي':
                cursor.execute("""
                    INSERT INTO cash_register (
                        transaction_type, amount, description, reference_type, 
                        reference_id, branch_id, created_by, payment_method, transaction_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    'income', total_amount, f'فاتورة رقم {invoice_id}', 'invoice',
                    invoice_id, branch_id, 'admin', payment_method, invoice_date
                ))
        
        conn.commit()
        print(f"✅ تم إنشاء 10 فواتير تجريبية مع عناصرها")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفواتير: {str(e)}")
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 اختبار عمليات قاعدة البيانات...")
        
        # اختبار العملاء
        cursor.execute("SELECT COUNT(*) FROM customers")
        customers_count = cursor.fetchone()[0]
        print(f"📊 عدد العملاء: {customers_count}")
        
        # اختبار الخدمات
        cursor.execute("SELECT COUNT(*) FROM services")
        services_count = cursor.fetchone()[0]
        print(f"🔧 عدد الخدمات: {services_count}")
        
        # اختبار المنتجات
        cursor.execute("SELECT COUNT(*) FROM products")
        products_count = cursor.fetchone()[0]
        print(f"📦 عدد المنتجات: {products_count}")
        
        # اختبار الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        invoices_count = cursor.fetchone()[0]
        print(f"🧾 عدد الفواتير: {invoices_count}")
        
        # اختبار عناصر الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoice_items")
        items_count = cursor.fetchone()[0]
        print(f"🛍️ عدد عناصر الفواتير: {items_count}")
        
        # اختبار الخزنة
        cursor.execute("SELECT COUNT(*) FROM cash_register")
        cash_count = cursor.fetchone()[0]
        print(f"💰 عدد معاملات الخزنة: {cash_count}")
        
        # حساب رصيد الخزنة
        cursor.execute("""
            SELECT SUM(
                CASE 
                    WHEN transaction_type = 'income' THEN amount
                    WHEN transaction_type = 'expense' THEN -amount
                    ELSE 0
                END
            ) as balance
            FROM cash_register
            WHERE is_deleted = 0
        """)
        balance = cursor.fetchone()[0] or 0
        print(f"💵 رصيد الخزنة الإجمالي: {balance:.2f} ج.م")
        
        # إجمالي المبيعات
        cursor.execute("SELECT SUM(total_amount) FROM invoices")
        total_sales = cursor.fetchone()[0] or 0
        print(f"💰 إجمالي المبيعات: {total_sales:.2f} ج.م")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🏗️ إنشاء بيانات تجريبية شاملة")
    print("=" * 60)
    
    # إنشاء العملاء
    if create_test_customers():
        print("✅ العملاء جاهزون")
    
    # إنشاء الخدمات
    if create_test_services():
        print("✅ الخدمات جاهزة")
    
    # إنشاء المنتجات
    if create_test_products():
        print("✅ المنتجات جاهزة")
    
    # إنشاء الفواتير
    if create_test_invoices():
        print("✅ الفواتير جاهزة")
    
    print("\n" + "=" * 60)
    
    # اختبار النظام
    if test_database_operations():
        print("\n✅ جميع العمليات تعمل بنجاح!")
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء جميع البيانات التجريبية بنجاح!")
    print("💡 الآن يمكنك اختبار النظام بالكامل")
    
    input("\nاضغط Enter للخروج...")
