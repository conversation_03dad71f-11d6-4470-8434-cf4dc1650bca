#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للنظام مع الأكواد التسلسلية
"""

import pyodbc
from datetime import datetime
import threading
import time

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def get_next_sequence_code(sequence_name, branch_id=None):
    """الحصول على الكود التسلسلي التالي"""
    try:
        conn = get_db_connection()
        if conn is None:
            return None
        
        cursor = conn.cursor()
        cursor.execute("""
            DECLARE @next_code NVARCHAR(50);
            EXEC GetNextSequence ?, ?, @next_code OUTPUT;
            SELECT @next_code;
        """, (sequence_name, branch_id))
        
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0] != 'ERROR':
            return result[0]
        return None
    except Exception as e:
        print(f"❌ خطأ في توليد الكود: {str(e)}")
        return None

def test_concurrent_invoice_creation():
    """اختبار إنشاء فواتير متزامنة"""
    print("🔄 اختبار إنشاء فواتير متزامنة...")
    
    generated_codes = []
    errors = []
    
    def create_invoice(thread_id):
        try:
            conn = get_db_connection()
            if conn:
                cursor = conn.cursor()
                
                # توليد كود الفاتورة
                branch_id = thread_id  # كل thread يستخدم فرع مختلف
                sequence_name = f'branch_invoice_{branch_id}'
                
                cursor.execute("""
                    DECLARE @next_code NVARCHAR(50);
                    EXEC GetNextSequence ?, ?, @next_code OUTPUT;
                    SELECT @next_code;
                """, (sequence_name, branch_id))
                
                result = cursor.fetchone()
                if result and result[0] != 'ERROR':
                    invoice_code = result[0]
                    generated_codes.append(f"Thread-{thread_id}: {invoice_code}")
                    
                    # إنشاء فاتورة تجريبية
                    cursor.execute("""
                        INSERT INTO invoices (
                            invoice_code, customer_id, branch_id, employee_id, invoice_date,
                            subtotal, tax_amount, discount_amount, total_amount,
                            payment_method, payment_status, notes, responsible_employee
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        invoice_code, 1, branch_id, 1, datetime.now().date(),
                        100.00, 0.00, 10.00, 90.00,
                        'نقدي', 'مدفوعة', f'فاتورة اختبار من Thread-{thread_id}', 'أحمد محمد'
                    ))
                    
                    conn.commit()
                    print(f"✅ Thread-{thread_id} created invoice: {invoice_code}")
                else:
                    errors.append(f"Thread-{thread_id}: فشل في توليد الكود")
                
                conn.close()
        except Exception as e:
            errors.append(f"Thread-{thread_id}: {str(e)}")
    
    # تشغيل 3 threads متزامنة
    threads = []
    for i in range(1, 4):  # فروع 1, 2, 3
        thread = threading.Thread(target=create_invoice, args=(i,))
        threads.append(thread)
        thread.start()
    
    # انتظار انتهاء جميع الـ threads
    for thread in threads:
        thread.join()
    
    print(f"✅ تم إنشاء {len(generated_codes)} فاتورة")
    print(f"❌ عدد الأخطاء: {len(errors)}")
    
    if errors:
        for error in errors:
            print(f"   ❌ {error}")
    
    return len(errors) == 0

def test_customer_creation():
    """اختبار إنشاء عملاء بأكواد تسلسلية"""
    print("🔄 اختبار إنشاء عملاء...")
    
    try:
        conn = get_db_connection()
        if conn is None:
            return False
        
        cursor = conn.cursor()
        
        # إنشاء 3 عملاء جدد
        customers = [
            ('عميل اختبار 1', 'Test Customer 1', '01111111111'),
            ('عميل اختبار 2', 'Test Customer 2', '01222222222'),
            ('عميل اختبار 3', 'Test Customer 3', '01333333333')
        ]
        
        created_customers = []
        
        for customer in customers:
            # توليد كود العميل
            customer_code = get_next_sequence_code('customer_code')
            
            if customer_code:
                cursor.execute("""
                    INSERT INTO Customers (customer_code, FirstName, LastName, PhoneNumber, CreatedAt)
                    VALUES (?, ?, ?, ?, GETDATE())
                """, (customer_code, customer[0], customer[1], customer[2]))
                
                created_customers.append(customer_code)
                print(f"✅ تم إنشاء العميل: {customer_code} - {customer[0]}")
            else:
                print(f"❌ فشل في توليد كود للعميل: {customer[0]}")
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء {len(created_customers)} عميل بنجاح")
        return len(created_customers) == len(customers)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العملاء: {str(e)}")
        return False

def check_system_integrity():
    """فحص سلامة النظام"""
    print("🔍 فحص سلامة النظام...")
    
    try:
        conn = get_db_connection()
        if conn is None:
            return False
        
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        essential_tables = ['invoices', 'Customers', 'employees', 'sequences', 'categories']
        
        for table in essential_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"📋 {table}: {count} سجل")
        
        # فحص الأكواد التسلسلية
        cursor.execute("""
            SELECT sequence_name, current_value, prefix 
            FROM sequences 
            ORDER BY sequence_name
        """)
        
        sequences = cursor.fetchall()
        print("\n🔢 الأرقام التسلسلية:")
        for seq in sequences:
            print(f"   • {seq[0]}: {seq[2]}{seq[1]:06d}")
        
        # فحص الفواتير الحديثة
        cursor.execute("""
            SELECT TOP 5 invoice_code, total_amount, created_at 
            FROM invoices 
            ORDER BY created_at DESC
        """)
        
        recent_invoices = cursor.fetchall()
        print("\n📄 آخر الفواتير:")
        for invoice in recent_invoices:
            print(f"   • {invoice[0]}: {invoice[1]} ج.م - {invoice[2]}")
        
        # فحص العملاء الحديثين
        cursor.execute("""
            SELECT TOP 5 customer_code, FirstName, CreatedAt 
            FROM Customers 
            WHERE customer_code IS NOT NULL
            ORDER BY CreatedAt DESC
        """)
        
        recent_customers = cursor.fetchall()
        print("\n👥 آخر العملاء:")
        for customer in recent_customers:
            print(f"   • {customer[0]}: {customer[1]} - {customer[2]}")
        
        # فحص رصيد الخزنة
        cursor.execute("""
            SELECT SUM(
                CASE 
                    WHEN transaction_type = 'income' THEN amount
                    WHEN transaction_type = 'expense' THEN -amount
                    ELSE 0
                END
            ) FROM cash_register WHERE is_deleted = 0
        """)
        balance = cursor.fetchone()[0] or 0
        print(f"\n💰 رصيد الخزنة: {balance:.2f} ج.م")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص النظام: {str(e)}")
        return False

def test_sequence_uniqueness():
    """اختبار فرادة الأرقام التسلسلية"""
    print("🔄 اختبار فرادة الأرقام التسلسلية...")
    
    generated_codes = set()
    duplicates = []
    
    # توليد 20 كود للفواتير
    for i in range(20):
        code = get_next_sequence_code('invoice_number')
        if code:
            if code in generated_codes:
                duplicates.append(code)
            else:
                generated_codes.add(code)
    
    print(f"✅ تم توليد {len(generated_codes)} كود فريد")
    
    if duplicates:
        print(f"❌ تم العثور على {len(duplicates)} كود مكرر:")
        for dup in duplicates:
            print(f"   • {dup}")
        return False
    else:
        print("✅ جميع الأكواد فريدة")
        return True

def performance_test():
    """اختبار الأداء"""
    print("⚡ اختبار الأداء...")
    
    start_time = time.time()
    
    # توليد 100 كود
    codes = []
    for i in range(100):
        code = get_next_sequence_code('customer_code')
        if code:
            codes.append(code)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"✅ تم توليد {len(codes)} كود في {duration:.2f} ثانية")
    print(f"📊 متوسط الوقت لكل كود: {(duration/len(codes)*1000):.2f} مللي ثانية")
    
    return duration < 10  # يجب أن يكون أقل من 10 ثواني

if __name__ == "__main__":
    print("🧪 الاختبار النهائي الشامل لنظام SalonProManager")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 6
    
    # اختبار سلامة النظام
    if check_system_integrity():
        print("✅ اختبار سلامة النظام: نجح")
        tests_passed += 1
    else:
        print("❌ اختبار سلامة النظام: فشل")
    
    print("\n" + "-" * 50)
    
    # اختبار فرادة الأرقام
    if test_sequence_uniqueness():
        print("✅ اختبار فرادة الأرقام: نجح")
        tests_passed += 1
    else:
        print("❌ اختبار فرادة الأرقام: فشل")
    
    print("\n" + "-" * 50)
    
    # اختبار الأداء
    if performance_test():
        print("✅ اختبار الأداء: نجح")
        tests_passed += 1
    else:
        print("❌ اختبار الأداء: فشل")
    
    print("\n" + "-" * 50)
    
    # اختبار إنشاء العملاء
    if test_customer_creation():
        print("✅ اختبار إنشاء العملاء: نجح")
        tests_passed += 1
    else:
        print("❌ اختبار إنشاء العملاء: فشل")
    
    print("\n" + "-" * 50)
    
    # اختبار الفواتير المتزامنة
    if test_concurrent_invoice_creation():
        print("✅ اختبار الفواتير المتزامنة: نجح")
        tests_passed += 1
    else:
        print("❌ اختبار الفواتير المتزامنة: فشل")
    
    print("\n" + "-" * 50)
    
    # فحص نهائي للنظام
    if check_system_integrity():
        print("✅ الفحص النهائي: نجح")
        tests_passed += 1
    else:
        print("❌ الفحص النهائي: فشل")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبار نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("✅ الأكواد التسلسلية تعمل بشكل صحيح")
        print("✅ النظام آمن للعمل المتزامن")
        print("✅ قاعدة البيانات سليمة ومتكاملة")
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج النظام لمراجعة")
    
    print("\n💡 ملاحظات مهمة:")
    print("   • استخدم الأكواد التسلسلية في جميع العمليات")
    print("   • النظام يدعم العمل المتزامن بأمان")
    print("   • جميع البيانات محفوظة في قاعدة البيانات")
    print("   • الفواتير والعملاء لهم أكواد فريدة")
    
    input("\nاضغط Enter للخروج...")
