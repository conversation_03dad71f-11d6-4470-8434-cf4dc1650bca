#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الجداول المفقودة وإنشاء الأكواد التسلسلية الآمنة
"""

import pyodbc

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_missing_tables():
    """إنشاء الجداول المفقودة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء الجداول المفقودة...")
        
        # إنشاء جدول branches
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'branches')
            BEGIN
                CREATE TABLE branches (
                    branch_id INT IDENTITY(1,1) PRIMARY KEY,
                    branch_name NVARCHAR(100) NOT NULL,
                    branch_code NVARCHAR(20) UNIQUE,
                    address NVARCHAR(500),
                    phone NVARCHAR(20),
                    manager_name NVARCHAR(100),
                    is_active BIT DEFAULT 1,
                    created_at DATETIME DEFAULT GETDATE(),
                    is_deleted BIT DEFAULT 0
                )
                
                -- إدراج فروع افتراضية
                INSERT INTO branches (branch_name, branch_code, address, phone, manager_name)
                VALUES 
                    ('الفرع الرئيسي', 'B001', 'القاهرة - المعادي', '01090829393', 'أحمد محمد'),
                    ('فرع المهندسين', 'B002', 'الجيزة - المهندسين', '01090829394', 'فاطمة علي'),
                    ('فرع مدينة نصر', 'B003', 'القاهرة - مدينة نصر', '01090829395', 'سارة أحمد')
            END
        """)
        print("✅ تم إنشاء جدول branches")
        
        # إنشاء جدول services
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'services')
            BEGIN
                CREATE TABLE services (
                    service_id INT IDENTITY(1,1) PRIMARY KEY,
                    service_code NVARCHAR(20) UNIQUE,
                    service_name NVARCHAR(100) NOT NULL,
                    description NVARCHAR(500),
                    price_tier_1 DECIMAL(10,2) DEFAULT 0,
                    price_tier_2 DECIMAL(10,2) DEFAULT 0,
                    price_tier_3 DECIMAL(10,2) DEFAULT 0,
                    duration_minutes INT DEFAULT 30,
                    category_id INT,
                    is_active BIT DEFAULT 1,
                    created_at DATETIME DEFAULT GETDATE(),
                    is_deleted BIT DEFAULT 0
                )
                
                -- إدراج خدمات افتراضية
                INSERT INTO services (service_code, service_name, description, price_tier_1, price_tier_2, price_tier_3, duration_minutes, category_id)
                VALUES 
                    ('S001', 'قص شعر رجالي', 'قص شعر عادي للرجال', 80.00, 60.00, 40.00, 30, 1),
                    ('S002', 'قص شعر نسائي', 'قص شعر عادي للسيدات', 120.00, 100.00, 80.00, 45, 1),
                    ('S003', 'صبغة شعر', 'صبغة شعر كاملة', 200.00, 150.00, 100.00, 120, 1),
                    ('S004', 'مكياج عادي', 'مكياج يومي', 150.00, 120.00, 90.00, 60, 2),
                    ('S005', 'مكياج عروس', 'مكياج خاص للعرائس', 500.00, 400.00, 300.00, 180, 5),
                    ('S006', 'مانيكير', 'عناية بأظافر اليدين', 60.00, 45.00, 30.00, 45, 4),
                    ('S007', 'بديكير', 'عناية بأظافر القدمين', 80.00, 60.00, 40.00, 60, 4)
            END
        """)
        print("✅ تم إنشاء جدول services")
        
        # إنشاء جدول products
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'products')
            BEGIN
                CREATE TABLE products (
                    product_id INT IDENTITY(1,1) PRIMARY KEY,
                    product_code NVARCHAR(20) UNIQUE,
                    product_name NVARCHAR(100) NOT NULL,
                    description NVARCHAR(500),
                    price_tier_1 DECIMAL(10,2) DEFAULT 0,
                    price_tier_2 DECIMAL(10,2) DEFAULT 0,
                    price_tier_3 DECIMAL(10,2) DEFAULT 0,
                    cost_price DECIMAL(10,2) DEFAULT 0,
                    stock_quantity INT DEFAULT 0,
                    min_stock_level INT DEFAULT 5,
                    category_id INT,
                    is_active BIT DEFAULT 1,
                    created_at DATETIME DEFAULT GETDATE(),
                    is_deleted BIT DEFAULT 0
                )
                
                -- إدراج منتجات افتراضية
                INSERT INTO products (product_code, product_name, description, price_tier_1, price_tier_2, price_tier_3, cost_price, stock_quantity, category_id)
                VALUES 
                    ('P001', 'شامبو للشعر الجاف', 'شامبو مرطب للشعر الجاف', 80.00, 60.00, 40.00, 30.00, 50, 3),
                    ('P002', 'كريم للشعر', 'كريم مغذي للشعر', 100.00, 80.00, 60.00, 40.00, 30, 3),
                    ('P003', 'زيت للشعر', 'زيت طبيعي للشعر', 120.00, 100.00, 80.00, 50.00, 25, 6),
                    ('P004', 'مزيل مكياج', 'مزيل مكياج لطيف', 60.00, 45.00, 30.00, 20.00, 40, 3),
                    ('P005', 'كريم مرطب', 'كريم مرطب للوجه', 150.00, 120.00, 90.00, 60.00, 20, 3),
                    ('P006', 'طلاء أظافر', 'طلاء أظافر عالي الجودة', 40.00, 30.00, 20.00, 15.00, 100, 4)
            END
        """)
        print("✅ تم إنشاء جدول products")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {str(e)}")
        return False

def add_code_columns_safely():
    """إضافة أعمدة الأكواد بأمان"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إضافة أعمدة الأكواد بأمان...")
        
        # إضافة كود الفاتورة بدون UNIQUE أولاً
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'invoice_code'
            )
            BEGIN
                ALTER TABLE invoices ADD invoice_code NVARCHAR(50)
            END
        """)
        print("✅ تم إضافة عمود invoice_code")
        
        # إضافة كود العميل
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'customer_code'
            )
            BEGIN
                ALTER TABLE Customers ADD customer_code NVARCHAR(50)
            END
        """)
        print("✅ تم إضافة عمود customer_code")
        
        # إضافة كود الموظف
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'employees' AND COLUMN_NAME = 'employee_code'
            )
            BEGIN
                ALTER TABLE employees ADD employee_code NVARCHAR(50)
            END
        """)
        print("✅ تم إضافة عمود employee_code")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة أعمدة الأكواد: {str(e)}")
        return False

def fix_sequence_procedure():
    """إصلاح stored procedure للأرقام التسلسلية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إصلاح stored procedure للأرقام التسلسلية...")
        
        # حذف الإجراء المخزن الحالي
        cursor.execute("""
            IF OBJECT_ID('dbo.GetNextSequence', 'P') IS NOT NULL
                DROP PROCEDURE dbo.GetNextSequence
        """)
        
        # إنشاء إجراء مخزن محسن للعمل المتزامن
        create_procedure_sql = """
        CREATE PROCEDURE dbo.GetNextSequence
            @sequence_name NVARCHAR(50),
            @branch_id INT = NULL,
            @next_code NVARCHAR(50) OUTPUT
        AS
        BEGIN
            SET NOCOUNT ON;
            
            DECLARE @current_value BIGINT;
            DECLARE @prefix NVARCHAR(10);
            DECLARE @suffix NVARCHAR(10);
            DECLARE @min_length INT;
            DECLARE @formatted_number NVARCHAR(20);
            
            -- بدء معاملة لضمان الأمان
            BEGIN TRANSACTION;
            
            BEGIN TRY
                -- استخدام UPDLOCK و HOLDLOCK لضمان عدم التداخل
                SELECT @prefix = prefix, @suffix = suffix, @min_length = min_length
                FROM sequences WITH (UPDLOCK, HOLDLOCK)
                WHERE sequence_name = @sequence_name 
                    AND (@branch_id IS NULL OR branch_id = @branch_id OR branch_id IS NULL);
                
                -- تحديث القيمة والحصول على القيمة الجديدة
                UPDATE sequences 
                SET current_value = current_value + 1,
                    updated_at = GETDATE()
                WHERE sequence_name = @sequence_name 
                    AND (@branch_id IS NULL OR branch_id = @branch_id OR branch_id IS NULL);
                
                -- الحصول على القيمة المحدثة
                SELECT @current_value = current_value
                FROM sequences
                WHERE sequence_name = @sequence_name 
                    AND (@branch_id IS NULL OR branch_id = @branch_id OR branch_id IS NULL);
                
                -- تنسيق الرقم
                SET @formatted_number = RIGHT('000000000' + CAST(@current_value AS NVARCHAR), @min_length);
                SET @next_code = @prefix + @formatted_number + @suffix;
                
                COMMIT TRANSACTION;
            END TRY
            BEGIN CATCH
                ROLLBACK TRANSACTION;
                SET @next_code = 'ERROR';
            END CATCH
        END
        """
        
        cursor.execute(create_procedure_sql)
        print("✅ تم إنشاء stored procedure محسن")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح stored procedure: {str(e)}")
        return False

def update_existing_records_with_codes():
    """تحديث السجلات الموجودة بأكواد تسلسلية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 تحديث السجلات الموجودة بأكواد...")
        
        # تحديث العملاء بأكواد
        cursor.execute("""
            SELECT CustomerId FROM Customers 
            WHERE customer_code IS NULL OR customer_code = ''
            ORDER BY CustomerId
        """)
        customers = cursor.fetchall()
        
        for customer in customers:
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence 'customer_code', NULL, @next_code OUTPUT;
                UPDATE Customers SET customer_code = @next_code WHERE CustomerId = ?;
            """, (customer[0],))
        
        print(f"✅ تم تحديث {len(customers)} عميل بأكواد")
        
        # تحديث الموظفين بأكواد
        cursor.execute("""
            SELECT employee_id FROM employees 
            WHERE employee_code IS NULL OR employee_code = ''
            ORDER BY employee_id
        """)
        employees = cursor.fetchall()
        
        for employee in employees:
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence 'employee_code', NULL, @next_code OUTPUT;
                UPDATE employees SET employee_code = @next_code WHERE employee_id = ?;
            """, (employee[0],))
        
        print(f"✅ تم تحديث {len(employees)} موظف بأكواد")
        
        # تحديث الفواتير بأكواد
        cursor.execute("""
            SELECT invoice_id, branch_id FROM invoices 
            WHERE invoice_code IS NULL OR invoice_code = ''
            ORDER BY invoice_id
        """)
        invoices = cursor.fetchall()
        
        for invoice in invoices:
            branch_id = invoice[1] if invoice[1] else 1
            sequence_name = f'branch_invoice_{branch_id}'
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence ?, ?, @next_code OUTPUT;
                UPDATE invoices SET invoice_code = @next_code WHERE invoice_id = ?;
            """, (sequence_name, branch_id, invoice[0]))
        
        print(f"✅ تم تحديث {len(invoices)} فاتورة بأكواد")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث السجلات: {str(e)}")
        return False

def test_sequence_generation():
    """اختبار توليد الأرقام التسلسلية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 اختبار توليد الأرقام التسلسلية...")
        
        # اختبار توليد أكواد مختلفة
        test_sequences = ['customer_code', 'employee_code', 'invoice_number']
        
        for seq_name in test_sequences:
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence ?, NULL, @next_code OUTPUT;
                SELECT @next_code;
            """, (seq_name,))
            
            result = cursor.fetchone()
            if result and result[0] != 'ERROR':
                print(f"✅ {seq_name}: {result[0]}")
            else:
                print(f"❌ {seq_name}: فشل في التوليد")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التوليد: {str(e)}")
        return False

def add_unique_constraints():
    """إضافة قيود الفرادة بعد تحديث البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إضافة قيود الفرادة...")
        
        # إضافة فهرس فريد لكود الفاتورة
        try:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_invoices_invoice_code')
                BEGIN
                    CREATE UNIQUE INDEX IX_invoices_invoice_code ON invoices(invoice_code)
                    WHERE invoice_code IS NOT NULL AND invoice_code != ''
                END
            """)
            print("✅ تم إضافة فهرس فريد لكود الفاتورة")
        except Exception as e:
            print(f"⚠️ مشكلة في فهرس كود الفاتورة: {str(e)}")
        
        # إضافة فهرس فريد لكود العميل
        try:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_customers_customer_code')
                BEGIN
                    CREATE UNIQUE INDEX IX_customers_customer_code ON Customers(customer_code)
                    WHERE customer_code IS NOT NULL AND customer_code != ''
                END
            """)
            print("✅ تم إضافة فهرس فريد لكود العميل")
        except Exception as e:
            print(f"⚠️ مشكلة في فهرس كود العميل: {str(e)}")
        
        # إضافة فهرس فريد لكود الموظف
        try:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_employees_employee_code')
                BEGIN
                    CREATE UNIQUE INDEX IX_employees_employee_code ON employees(employee_code)
                    WHERE employee_code IS NOT NULL AND employee_code != ''
                END
            """)
            print("✅ تم إضافة فهرس فريد لكود الموظف")
        except Exception as e:
            print(f"⚠️ مشكلة في فهرس كود الموظف: {str(e)}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قيود الفرادة: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 إصلاح النظام وإضافة الأكواد التسلسلية")
    print("=" * 60)
    
    # إنشاء الجداول المفقودة
    if create_missing_tables():
        print("✅ الجداول المفقودة تم إنشاؤها")
    
    # إضافة أعمدة الأكواد بأمان
    if add_code_columns_safely():
        print("✅ أعمدة الأكواد تم إضافتها")
    
    # إصلاح stored procedure
    if fix_sequence_procedure():
        print("✅ stored procedure تم إصلاحه")
    
    # تحديث السجلات الموجودة
    if update_existing_records_with_codes():
        print("✅ السجلات تم تحديثها بأكواد")
    
    # اختبار التوليد
    if test_sequence_generation():
        print("✅ اختبار التوليد نجح")
    
    # إضافة قيود الفرادة
    if add_unique_constraints():
        print("✅ قيود الفرادة تم إضافتها")
    
    print("\n" + "=" * 60)
    print("🎉 تم إصلاح النظام بالكامل!")
    print("💡 الآن النظام يدعم:")
    print("   • أكواد تسلسلية آمنة للعمل المتزامن")
    print("   • أكواد فريدة للعملاء والموظفين والفواتير")
    print("   • جداول كاملة للخدمات والمنتجات والفروع")
    print("   • حماية من التكرار في البيئة المتزامنة")
    
    input("\nاضغط Enter للخروج...")
