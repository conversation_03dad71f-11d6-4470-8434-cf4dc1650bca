"""
إعداد البيانات الأولية
Initialize Database with Sample Data
"""

from sqlalchemy.orm import Session
from database import SessionLocal, create_tables
from models.all_models import Role, User, Branch, Zone
from passlib.context import CryptContext

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    """تشفير كلمة المرور"""
    return pwd_context.hash(password)

def init_database():
    """إعداد قاعدة البيانات والبيانات الأولية"""
    
    # إنشاء الجداول
    create_tables()
    
    db = SessionLocal()
    
    try:
        # إنشاء الأدوار
        roles_data = [
            {"name": "مدير نظام", "description": "صلاحيات كاملة لجميع الفروع"},
            {"name": "مدير فرع", "description": "صلاحيات كاملة للفرع المحدد"},
            {"name": "موظف استقبال", "description": "إدارة المواعيد والعملاء"},
            {"name": "خبير تجميل", "description": "تقديم الخدمات وإدارة المواعيد الخاصة"}
        ]
        
        for role_data in roles_data:
            existing_role = db.query(Role).filter(Role.name == role_data["name"]).first()
            if not existing_role:
                role = Role(**role_data)
                db.add(role)
        
        db.commit()
        
        # إنشاء المستخدم الافتراضي (مدير النظام)
        admin_role = db.query(Role).filter(Role.name == "مدير نظام").first()
        existing_admin = db.query(User).filter(User.username == "admin").first()
        
        if not existing_admin and admin_role:
            admin_user = User(
                username="admin",
                password_hash=get_password_hash("admin123"),
                full_name="مدير النظام",
                email="<EMAIL>",
                role_id=admin_role.role_id,
                is_active=True
            )
            db.add(admin_user)
        
        # إنشاء فرع افتراضي
        existing_branch = db.query(Branch).filter(Branch.name == "الفرع الرئيسي").first()
        if not existing_branch:
            main_branch = Branch(
                name="الفرع الرئيسي",
                address="الرياض، المملكة العربية السعودية",
                phone="+966501234567",
                working_hours="9:00 ص - 10:00 م",
                is_active=True
            )
            db.add(main_branch)
        
        # إنشاء مناطق افتراضية
        zones_data = [
            {"name": "الرياض", "description": "منطقة الرياض"},
            {"name": "جدة", "description": "منطقة جدة"},
            {"name": "الدمام", "description": "منطقة الدمام"},
            {"name": "مكة المكرمة", "description": "منطقة مكة المكرمة"},
            {"name": "المدينة المنورة", "description": "منطقة المدينة المنورة"}
        ]
        
        for zone_data in zones_data:
            existing_zone = db.query(Zone).filter(Zone.name == zone_data["name"]).first()
            if not existing_zone:
                zone = Zone(**zone_data)
                db.add(zone)
        
        db.commit()
        print("تم إعداد البيانات الأولية بنجاح!")
        print("بيانات تسجيل الدخول الافتراضية:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
        
    except Exception as e:
        print(f"خطأ في إعداد البيانات: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
