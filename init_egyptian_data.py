"""
إعداد البيانات المصرية الجديدة
Egyptian Data Initialization
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
    return pyodbc.connect(connection_string)

def init_egyptian_branches():
    """إضافة الفروع المصرية"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # حذف الفروع القديمة
    cursor.execute("DELETE FROM branches")
    
    # إضافة الفروع الجديدة
    branches = [
        {
            'name': 'فرع مدينة نصر',
            'address': '1 محمود سامي البارودي – من حسنين هيكل – بجوار كافيه أزوزة، عباس العقاد',
            'phone': '01090829393',
            'working_hours': 'يومياً من 10 صباحاً حتى 12 منتصف الليل',
            'location_url': 'https://maps.google.com/?q=30.060099,31.339352'
        },
        {
            'name': 'فرع جسر السويس (أحمد عصمت)',
            'address': 'شارع أحمد عصمت – أمام بوابة 4 نادي الشمس – قدام كشري قشطة',
            'phone': '01090829393',
            'working_hours': 'يومياً من 10 صباحاً حتى 12 منتصف الليل',
            'location_url': 'https://maps.google.com/?q=30.122385,31.342529'
        },
        {
            'name': 'فرع الزهراء',
            'address': 'مدينة الزهراء - القاهرة',
            'phone': '01090829393',
            'working_hours': 'يومياً من 10 صباحاً حتى 12 منتصف الليل',
            'location_url': ''
        }
    ]
    
    for branch in branches:
        cursor.execute("""
            INSERT INTO branches (name, address, phone, working_hours, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, 1, GETDATE(), GETDATE())
        """, branch['name'], branch['address'], branch['phone'], branch['working_hours'])
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إضافة الفروع المصرية بنجاح")

def init_egyptian_services():
    """إضافة الخدمات المصرية"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # حذف الخدمات القديمة
    cursor.execute("DELETE FROM services")
    
    # خدمات الشعر والستايل
    hair_services = [
        ('قص شعر بروفيشنال', 'قص شعر احترافي بأحدث الطرق', 200, 45),
        ('دقن ستايلنج', 'تهذيب وتشكيل الدقن بشكل احترافي', 150, 30),
        ('قص أطفال', 'قص شعر للأطفال بكفاءة وراحة نفسية', 150, 30),
        ('استشوار برو', 'تصفيف الشعر بالاستشوار المحترف', 200, 60),
        ('مكواة ستايل', 'فرد الشعر بالمكواة', 200, 45),
        ('ويڤي / كيرلي طبيعي', 'تمويج الشعر الطبيعي', 200, 60),
        ('مكواة كيرلي بروفيشنال', 'تمويج احترافي للشعر', 500, 90),
        ('صبغة شعر كاملة', 'صبغ الشعر بالكامل بألوان متنوعة', 300, 120),
        ('صبغة دقن', 'صبغ الدقن بألوان طبيعية', 200, 45),
        ('تمليس', 'فرد الشعر بالكيراتين', 300, 180),
        ('جلسة قشرة', 'علاج قشرة الشعر', 300, 60),
        ('ترميم', 'ترميم الشعر التالف', 300, 90),
        ('حمام زيت', 'تغذية الشعر بالزيوت الطبيعية', 300, 60),
        ('واكس ستايل', 'تثبيت الشعر بالواكس', 150, 15)
    ]
    
    # خدمات الباديكير
    pedicure_services = [
        ('باديكير يد وقدم', 'عناية كاملة لليدين والقدمين', 500, 90),
        ('باديكير يد وقدم عناية كاملة + مساج', 'عناية فاخرة مع المساج والعلاج', 700, 120)
    ]
    
    # خدمات البشرة
    skin_services = [
        ('بشرة لايت', 'تنظيف وتجديد إشراقة البشرة', 500, 60),
        ('بشرة إبرة دهون + والتر سونك', 'تقنية متطورة لعلاج البشرة', 1000, 90),
        ('بشرة إكسترا', 'عناية متكاملة للبشرة', 1500, 120),
        ('هيدرو فيشال', 'جلسة فاخرة وعناية عميقة للبشرة', 2000, 150),
        ('أكسچنيو الفاخرة', 'أعلى مستوى من الانتعاش والعناية', 3000, 180)
    ]
    
    # خدمات الكولاجين والبوتوكس
    treatment_services = [
        ('كولاجين للشعر', 'علاج الشعر بالكولاجين', 1500, 180),
        ('بوتوكس للشعر', 'علاج الشعر بالبوتوكس', 1800, 200),
        ('كيراتين للشعر', 'فرد وعلاج الشعر بالكيراتين', 2000, 240)
    ]
    
    all_services = hair_services + pedicure_services + skin_services + treatment_services
    
    for service in all_services:
        cursor.execute("""
            INSERT INTO services (name, description, price, duration, is_active)
            VALUES (?, ?, ?, ?, 1)
        """, service[0], service[1], service[2], service[3])
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إضافة الخدمات المصرية بنجاح")

def init_egyptian_employees():
    """إضافة الموظفين المصريين"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # الحصول على معرفات الفروع
    cursor.execute("SELECT branch_id, name FROM branches")
    branches = {row[1]: row[0] for row in cursor.fetchall()}
    
    # حذف الموظفين القدامى
    cursor.execute("DELETE FROM employees")
    
    # إضافة الموظفين الجدد
    employees = [
        # فرع الزهراء
        {
            'name': 'عمر محمد',
            'phone': '01090829393',
            'position': 'مدير',
            'branch': 'فرع الزهراء',
            'specialization': 'إدارة عامة'
        },
        # فرع أحمد عصمت
        {
            'name': 'عمر فلانتينو',
            'phone': '01090829393',
            'position': 'مدير',
            'branch': 'فرع جسر السويس (أحمد عصمت)',
            'specialization': 'إدارة عامة'
        },
        {
            'name': 'أسامة',
            'phone': '01090829393',
            'position': 'صنايعي حلاقة',
            'branch': 'فرع جسر السويس (أحمد عصمت)',
            'specialization': 'حلاقة وتصفيف شعر'
        },
        # فرع مدينة نصر
        {
            'name': 'أحمد عمر',
            'phone': '01090829393',
            'position': 'مدير',
            'branch': 'فرع مدينة نصر',
            'specialization': 'إدارة عامة'
        },
        {
            'name': 'ياسمين',
            'phone': '01090829393',
            'position': 'أخصائية تنظيف بشرة',
            'branch': 'فرع مدينة نصر',
            'specialization': 'عناية بالبشرة وتنظيف'
        }
    ]
    
    for emp in employees:
        branch_id = branches.get(emp['branch'])
        if branch_id:
            cursor.execute("""
                INSERT INTO employees (first_name, last_name, phone, position, specialization, is_active, created_at, updated_at)
                VALUES (?, '', ?, ?, ?, 1, GETDATE(), GETDATE())
            """, emp['name'], emp['phone'], emp['position'], emp['specialization'])
    
    conn.commit()
    cursor.close()
    conn.close()
    print("✅ تم إضافة الموظفين المصريين بنجاح")

def main():
    """تشغيل جميع عمليات الإعداد"""
    print("🇪🇬 بدء إعداد البيانات المصرية...")
    
    try:
        init_egyptian_branches()
        init_egyptian_services()
        init_egyptian_employees()
        
        print("\n🎉 تم إعداد جميع البيانات المصرية بنجاح!")
        print("📍 الفروع: مدينة نصر، جسر السويس، الزهراء")
        print("👥 الموظفين: 5 موظفين موزعين على الفروع")
        print("✨ الخدمات: جميع خدمات الصالون المصري")
        print("💰 العملة: جنيه مصري")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد البيانات: {e}")

if __name__ == "__main__":
    main()
