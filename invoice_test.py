import streamlit as st
from datetime import datetime
import pandas as pd
import json

# إعداد الصفحة
st.set_page_config(
    page_title="SalonProManager - نظام إدارة الصالون",
    page_icon="💄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# تهيئة session state
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'user' not in st.session_state:
    st.session_state.user = None
if 'current_page' not in st.session_state:
    st.session_state.current_page = 'login'
if 'current_branch_id' not in st.session_state:
    st.session_state.current_branch_id = 1
if 'current_branch_name' not in st.session_state:
    st.session_state.current_branch_name = 'فرع مدينة نصر'
if 'invoice_items' not in st.session_state:
    st.session_state.invoice_items = []
if 'invoices' not in st.session_state:
    st.session_state.invoices = []
if 'customers' not in st.session_state:
    st.session_state.customers = []

# بيانات النظام
BRANCHES = {
    1: {"name": "فرع مدينة نصر", "price_tier": "premium"},
    2: {"name": "فرع جسر السويس", "price_tier": "standard"},
    3: {"name": "فرع الزهراء", "price_tier": "economy"}
}

# نظام الأسعار المتعدد
SERVICES_PRICES = {
    "قص شعر بروفيشنال": {"premium": 260, "standard": 200, "economy": 160},
    "دقن ستايلنج": {"premium": 195, "standard": 150, "economy": 120},
    "صبغة شعر كاملة": {"premium": 390, "standard": 300, "economy": 240},
    "استشوار برو": {"premium": 260, "standard": 200, "economy": 160},
    "مكواة كيرلي بروفيشنال": {"premium": 650, "standard": 500, "economy": 400},
    "بشرة لايت": {"premium": 650, "standard": 500, "economy": 400},
    "هيدرو فيشال": {"premium": 2600, "standard": 2000, "economy": 1600},
    "أكسچنيو الفاخرة": {"premium": 3900, "standard": 3000, "economy": 2400},
    "باديكير يد وقدم": {"premium": 650, "standard": 500, "economy": 400},
    "باديكير عناية كاملة + مساج": {"premium": 910, "standard": 700, "economy": 560},
    "كولاجين للشعر": {"premium": 1950, "standard": 1500, "economy": 1200},
    "بوتوكس للشعر": {"premium": 2340, "standard": 1800, "economy": 1440},
    "كيراتين للشعر": {"premium": 2600, "standard": 2000, "economy": 1600}
}

PRODUCTS_PRICES = {
    "شامبو لوريال": {"premium": 156, "standard": 120, "economy": 96},
    "كريم فرد الشعر": {"premium": 325, "standard": 250, "economy": 200},
    "صبغة شعر احترافية": {"premium": 455, "standard": 350, "economy": 280},
    "كريم تنظيف البشرة": {"premium": 130, "standard": 100, "economy": 80},
    "ماسك الوجه": {"premium": 104, "standard": 80, "economy": 64},
    "مكواة شعر احترافية": {"premium": 1560, "standard": 1200, "economy": 960},
    "مجفف شعر": {"premium": 1170, "standard": 900, "economy": 720},
    "أحمر شفاه": {"premium": 195, "standard": 150, "economy": 120},
    "كريم أساس": {"premium": 260, "standard": 200, "economy": 160}
}

EMPLOYEES = ["عمر محمد", "عمر فلانتينو", "أسامة", "أحمد عمر", "ياسمين"]

# دالة للحصول على السعر حسب الفرع
def get_price(item_name, item_type, branch_id):
    tier = BRANCHES[branch_id]["price_tier"]
    if item_type == "service":
        return SERVICES_PRICES.get(item_name, {}).get(tier, 0)
    else:
        return PRODUCTS_PRICES.get(item_name, {}).get(tier, 0)

# CSS للتصميم - نمط كاشير احترافي
st.markdown("""
<style>
.main-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}
.cashier-container {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1rem;
    margin: 0.5rem 0;
    border: 2px solid #e2e8f0;
}
.search-box {
    background: white;
    border: 2px solid #3b82f6;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}
.item-suggestion {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    margin: 0.25rem 0;
    cursor: pointer;
    transition: all 0.2s ease;
}
.item-suggestion:hover {
    background: #f0f9ff;
    border-color: #3b82f6;
    transform: translateY(-1px);
}
.invoice-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 1rem 0;
}
.table-header {
    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 1rem;
    font-weight: bold;
}
.table-row {
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
}
.table-row:nth-child(even) {
    background: #f8fafc;
}
.total-section {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1rem 0;
}
.branch-selector {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: center;
}
.price-tier {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 0.5rem;
}
.tier-premium { background: #fbbf24; color: #92400e; }
.tier-standard { background: #60a5fa; color: #1e40af; }
.tier-economy { background: #34d399; color: #065f46; }
</style>
""", unsafe_allow_html=True)

# دالة تسجيل الدخول
def login_page():
    st.markdown("""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; padding: 3rem; border-radius: 20px; text-align: center; margin: 2rem 0;">
        <h1>🔐 تسجيل الدخول</h1>
        <h3>SalonProManager</h3>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        with st.form("login_form"):
            username = st.text_input("اسم المستخدم", placeholder="أدخل اسم المستخدم")
            password = st.text_input("كلمة المرور", type="password", placeholder="أدخل كلمة المرور")
            branch_id = st.selectbox("اختر الفرع",
                options=list(BRANCHES.keys()),
                format_func=lambda x: BRANCHES[x]["name"])

            login_btn = st.form_submit_button("🚀 دخول", use_container_width=True)

            if login_btn:
                if username == "admin" and password == "123":
                    st.session_state.logged_in = True
                    st.session_state.user = {
                        "username": username,
                        "full_name": "المدير العام",
                        "role": "admin"
                    }
                    st.session_state.current_branch_id = branch_id
                    st.session_state.current_branch_name = BRANCHES[branch_id]["name"]
                    st.session_state.current_page = "cashier"
                    st.rerun()
                else:
                    st.error("❌ اسم المستخدم أو كلمة المرور غير صحيحة")

# دالة الشريط الجانبي
def sidebar():
    with st.sidebar:
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white; padding: 1rem; border-radius: 12px; text-align: center; margin-bottom: 1rem;">
            <h3>👤 {st.session_state.user['full_name']}</h3>
            <p>🏢 {st.session_state.current_branch_name}</p>
        </div>
        """, unsafe_allow_html=True)

        # قائمة التنقل
        pages = {
            "cashier": "🧾 نظام الكاشير",
            "dashboard": "📊 لوحة التحكم",
            "invoices": "📋 إدارة الفواتير",
            "customers": "👥 إدارة العملاء",
            "reports": "📈 التقارير",
            "settings": "⚙️ الإعدادات"
        }

        for page_key, page_name in pages.items():
            if st.button(page_name, key=f"nav_{page_key}", use_container_width=True):
                st.session_state.current_page = page_key
                st.rerun()

        st.divider()

        if st.button("🚪 تسجيل الخروج", use_container_width=True):
            st.session_state.logged_in = False
            st.session_state.user = None
            st.session_state.current_page = "login"
            st.rerun()

# تهيئة session state
if 'invoice_items' not in st.session_state:
    st.session_state.invoice_items = []

# معلومات الفاتورة
st.markdown('<div class="invoice-card">', unsafe_allow_html=True)
st.markdown("### 👤 معلومات العميل والفاتورة")

col1, col2, col3 = st.columns(3)

with col1:
    st.markdown("**بيانات العميل:**")
    customer_name = st.text_input("اسم العميل", value="عميل جديد")
    customer_phone = st.text_input("رقم الهاتف", value="")

with col2:
    st.markdown("**تفاصيل الفاتورة:**")
    invoice_date = st.date_input("تاريخ الفاتورة", value=datetime.now().date())
    payment_method = st.selectbox("طريقة الدفع", 
        ["نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية"])
    
with col3:
    st.markdown("**معلومات الفرع:**")
    branch_name = st.text_input("الفرع", value="فرع مدينة نصر", disabled=True)
    cashier_name = st.text_input("الكاشير", value="admin", disabled=True)

st.markdown('</div>', unsafe_allow_html=True)

# الخدمات والمنتجات
services = {
    "قص شعر بروفيشنال": 260,
    "دقن ستايلنج": 195,
    "صبغة شعر كاملة": 390,
    "استشوار برو": 260,
    "مكواة كيرلي": 650,
    "بشرة لايت": 650,
    "هيدرو فيشال": 2600,
    "باديكير": 650,
    "كولاجين للشعر": 1950,
    "بوتوكس للشعر": 2340
}

products = {
    "شامبو لوريال": 156,
    "كريم فرد الشعر": 325,
    "صبغة شعر احترافية": 455,
    "كريم تنظيف البشرة": 130,
    "ماسك الوجه": 104,
    "مكواة شعر احترافية": 1560,
    "مجفف شعر": 1170,
    "أحمر شفاه": 195,
    "كريم أساس": 260
}

# قسم إضافة العناصر
st.markdown('<div class="invoice-card">', unsafe_allow_html=True)
st.markdown("### 🛒 إضافة عناصر للفاتورة")

# تبويبات للخدمات والمنتجات
tab1, tab2 = st.tabs(["💆‍♀️ الخدمات", "🛍️ المنتجات"])

with tab1:
    st.markdown("#### الخدمات المتاحة")
    
    # عرض الخدمات في أعمدة
    services_list = list(services.items())
    for i in range(0, len(services_list), 3):
        cols = st.columns(3)
        for j, (service_name, price) in enumerate(services_list[i:i+3]):
            if j < len(cols):
                with cols[j]:
                    st.markdown(f"""
                    <div class="item-card">
                        <h4>💆‍♀️ {service_name}</h4>
                        <p style="font-size: 1.2rem; color: #3b82f6; font-weight: bold;">{price} ج.م</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    quantity = st.number_input("الكمية", min_value=1, value=1, key=f"service_qty_{i}_{j}")
                    employee = st.selectbox("الموظف", 
                        ["عمر محمد", "عمر فلانتينو", "أسامة", "أحمد عمر", "ياسمين"], 
                        key=f"service_emp_{i}_{j}")
                    
                    if st.button(f"➕ إضافة {service_name}", key=f"add_service_{i}_{j}", use_container_width=True):
                        new_item = {
                            'type': 'service',
                            'name': service_name,
                            'price': price,
                            'quantity': quantity,
                            'total': price * quantity,
                            'employee': employee
                        }
                        st.session_state.invoice_items.append(new_item)
                        st.success(f"✅ تم إضافة {service_name} للفاتورة!")
                        st.rerun()

with tab2:
    st.markdown("#### المنتجات المتاحة")
    
    # عرض المنتجات في أعمدة
    products_list = list(products.items())
    for i in range(0, len(products_list), 3):
        cols = st.columns(3)
        for j, (product_name, price) in enumerate(products_list[i:i+3]):
            if j < len(cols):
                with cols[j]:
                    st.markdown(f"""
                    <div class="item-card">
                        <h4>🛍️ {product_name}</h4>
                        <p style="font-size: 1.2rem; color: #f59e0b; font-weight: bold;">{price} ج.م</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    quantity = st.number_input("الكمية", min_value=1, value=1, key=f"product_qty_{i}_{j}")
                    
                    if st.button(f"➕ إضافة {product_name}", key=f"add_product_{i}_{j}", use_container_width=True):
                        new_item = {
                            'type': 'product',
                            'name': product_name,
                            'price': price,
                            'quantity': quantity,
                            'total': price * quantity,
                            'employee': 'غير محدد'
                        }
                        st.session_state.invoice_items.append(new_item)
                        st.success(f"✅ تم إضافة {product_name} للفاتورة!")
                        st.rerun()

st.markdown('</div>', unsafe_allow_html=True)

# عرض العناصر المضافة
if st.session_state.invoice_items:
    st.markdown('<div class="invoice-card">', unsafe_allow_html=True)
    st.markdown("### 📋 العناصر المضافة للفاتورة")
    
    # جدول العناصر
    for i, item in enumerate(st.session_state.invoice_items):
        col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])
        
        with col1:
            icon = "💆‍♀️" if item['type'] == 'service' else "🛍️"
            st.write(f"{icon} {item['name']}")
        with col2:
            st.write(f"{item['quantity']}")
        with col3:
            st.write(f"{item['price']} ج.م")
        with col4:
            st.write(f"{item['total']} ج.م")
        with col5:
            if st.button("🗑️", key=f"delete_{i}", help="حذف العنصر"):
                st.session_state.invoice_items.pop(i)
                st.rerun()
    
    # الإجمالي
    subtotal = sum([item['total'] for item in st.session_state.invoice_items])
    
    st.divider()
    
    # حسابات الفاتورة
    col1, col2 = st.columns(2)
    
    with col1:
        discount_percent = st.number_input("نسبة الخصم (%)", min_value=0.0, max_value=100.0, value=0.0)
        tax_percent = st.number_input("نسبة الضريبة (%)", min_value=0.0, max_value=100.0, value=14.0)
    
    with col2:
        discount_amount = subtotal * (discount_percent / 100)
        tax_amount = (subtotal - discount_amount) * (tax_percent / 100)
        total_amount = subtotal - discount_amount + tax_amount
        
        st.markdown(f"""
        <div class="total-card">
            <div>المجموع الفرعي: {subtotal:.2f} ج.م</div>
            <div>الخصم: -{discount_amount:.2f} ج.م</div>
            <div>الضريبة: +{tax_amount:.2f} ج.م</div>
            <hr style="margin: 1rem 0;">
            <div style="font-size: 2rem;">الإجمالي: {total_amount:.2f} ج.م</div>
        </div>
        """, unsafe_allow_html=True)
    
    st.divider()
    
    # أزرار الحفظ
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("✅ إنشاء الفاتورة", use_container_width=True, type="primary"):
            st.success("🎉 تم إنشاء الفاتورة بنجاح!")
            st.balloons()
    
    with col2:
        if st.button("💾 حفظ كمسودة", use_container_width=True):
            st.info("💾 تم حفظ الفاتورة كمسودة")
    
    with col3:
        if st.button("🗑️ مسح الكل", use_container_width=True):
            st.session_state.invoice_items = []
            st.rerun()
    
    st.markdown('</div>', unsafe_allow_html=True)

else:
    st.info("📝 لم يتم إضافة أي عناصر للفاتورة بعد")
