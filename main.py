"""
التطبيق الرئيسي
SalonProManager Main Application
"""

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
import uvicorn

from config import settings
from database import get_db
from api.routes import auth, branches, customers, zones

# إنشاء التطبيق
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="نظام إدارة صالونات متكامل ومتقدم",
    docs_url="/docs",
    redoc_url="/redoc"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # في الإنتاج، حدد النطاقات المسموحة
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد الأمان
security = HTTPBearer()

# تسجيل المسارات
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(branches.router, prefix="/api/branches", tags=["Branches"])
app.include_router(customers.router, prefix="/api/customers", tags=["Customers"])
app.include_router(zones.router, prefix="/api/zones", tags=["Zones"])

@app.on_event("startup")
async def startup_event():
    """أحداث بدء التطبيق"""
    print("🚀 تم بدء تشغيل SalonProManager API")

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "مرحباً بك في SalonProManager",
        "version": settings.APP_VERSION,
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """فحص صحة التطبيق"""
    return {"status": "healthy", "app": settings.APP_NAME}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
