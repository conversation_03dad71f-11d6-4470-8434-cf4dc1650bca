"""
جميع النماذج في ملف واحد لتجنب مشاكل الاستيراد الدائري
All Models in One File
"""

from sqlalchemy import Column, Integer, String, Text, Date, Time, DateTime, Boolean, Numeric, ForeignKey, Enum
from sqlalchemy.orm import relationship
from database import Base
from datetime import datetime
import enum

class TimestampMixin:
    """خليط الطوابع الزمنية"""
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# الأدوار
class Role(Base):
    """نموذج الأدوار"""
    __tablename__ = "roles"
    
    role_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(String(255))
    
    # العلاقات
    users = relationship("User", back_populates="role")

# الفروع
class Branch(Base, TimestampMixin):
    """نموذج الفروع"""
    __tablename__ = "branches"
    
    branch_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    address = Column(String(255))
    phone = Column(String(20))
    working_hours = Column(String(255))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    user_branches = relationship("UserBranch", back_populates="branch")
    employee_branches = relationship("EmployeeBranch", back_populates="branch")
    appointments = relationship("Appointment", back_populates="branch")
    invoices = relationship("Invoice", back_populates="branch")

# المستخدمين
class User(Base, TimestampMixin):
    """نموذج المستخدمين"""
    __tablename__ = "users"
    
    user_id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), nullable=False, unique=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    role_id = Column(Integer, ForeignKey("roles.role_id"))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    role = relationship("Role", back_populates="users")
    user_branches = relationship("UserBranch", back_populates="user")

# ربط المستخدمين بالفروع
class UserBranch(Base):
    """نموذج ربط المستخدمين بالفروع"""
    __tablename__ = "user_branches"
    
    user_branch_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    
    # العلاقات
    user = relationship("User", back_populates="user_branches")
    branch = relationship("Branch", back_populates="user_branches")

# المناطق
class Zone(Base):
    """نموذج المناطق"""
    __tablename__ = "zones"
    
    zone_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(String(255))
    
    # العلاقات
    customers = relationship("Customer", back_populates="zone")

# العملاء
class Customer(Base, TimestampMixin):
    """نموذج العملاء"""
    __tablename__ = "customers"
    
    customer_id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    phone = Column(String(20), nullable=False)
    email = Column(String(100))
    birth_date = Column(Date)
    zone_id = Column(Integer, ForeignKey("zones.zone_id"))
    notes = Column(Text)
    
    # العلاقات
    zone = relationship("Zone", back_populates="customers")
    appointments = relationship("Appointment", back_populates="customer")
    invoices = relationship("Invoice", back_populates="customer")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"

# أنواع العمولة
class CommissionType(enum.Enum):
    """أنواع العمولة"""
    PERCENTAGE = "percentage"
    FIXED = "fixed"
    SERVICE_BASED = "service_based"

# الموظفين
class Employee(Base, TimestampMixin):
    """نموذج الموظفين"""
    __tablename__ = "employees"
    
    employee_id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    phone = Column(String(20))
    email = Column(String(100))
    position = Column(String(50))
    commission_type = Column(Enum(CommissionType))
    commission_value = Column(Numeric(10, 2))
    hire_date = Column(Date)
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    employee_branches = relationship("EmployeeBranch", back_populates="employee")
    employee_services = relationship("EmployeeService", back_populates="employee")
    appointment_services = relationship("AppointmentService", back_populates="employee")
    commissions = relationship("EmployeeCommission", back_populates="employee")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"

# ربط الموظفين بالفروع
class EmployeeBranch(Base):
    """نموذج ربط الموظفين بالفروع"""
    __tablename__ = "employee_branches"
    
    employee_branch_id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    
    # العلاقات
    employee = relationship("Employee", back_populates="employee_branches")
    branch = relationship("Branch", back_populates="employee_branches")

# الخدمات
class Service(Base):
    """نموذج الخدمات"""
    __tablename__ = "services"
    
    service_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    price = Column(Numeric(10, 2), nullable=False)
    duration = Column(Integer, nullable=False)  # بالدقائق
    commission_type = Column(Enum(CommissionType))
    commission_value = Column(Numeric(10, 2))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    employee_services = relationship("EmployeeService", back_populates="service")
    appointment_services = relationship("AppointmentService", back_populates="service")
    invoice_items = relationship("InvoiceItem", back_populates="service")

# ربط الموظفين بالخدمات
class EmployeeService(Base):
    """نموذج ربط الموظفين بالخدمات"""
    __tablename__ = "employee_services"
    
    employee_service_id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    service_id = Column(Integer, ForeignKey("services.service_id"))
    
    # العلاقات
    employee = relationship("Employee", back_populates="employee_services")
    service = relationship("Service", back_populates="employee_services")

# حالات المواعيد
class AppointmentStatus(enum.Enum):
    """حالات المواعيد"""
    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"

# المواعيد
class Appointment(Base, TimestampMixin):
    """نموذج المواعيد"""
    __tablename__ = "appointments"
    
    appointment_id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.customer_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    appointment_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.SCHEDULED)
    notes = Column(Text)
    created_by = Column(Integer, ForeignKey("users.user_id"))
    
    # العلاقات
    customer = relationship("Customer", back_populates="appointments")
    branch = relationship("Branch", back_populates="appointments")
    creator = relationship("User")
    appointment_services = relationship("AppointmentService", back_populates="appointment")
    reminders = relationship("AppointmentReminder", back_populates="appointment")

# خدمات المواعيد
class AppointmentService(Base):
    """نموذج خدمات المواعيد"""
    __tablename__ = "appointment_services"
    
    appointment_service_id = Column(Integer, primary_key=True, index=True)
    appointment_id = Column(Integer, ForeignKey("appointments.appointment_id"))
    service_id = Column(Integer, ForeignKey("services.service_id"))
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    
    # العلاقات
    appointment = relationship("Appointment", back_populates="appointment_services")
    service = relationship("Service", back_populates="appointment_services")
    employee = relationship("Employee", back_populates="appointment_services")

# أنواع التذكيرات
class ReminderType(enum.Enum):
    """أنواع التذكيرات"""
    WHATSAPP = "whatsapp"
    SMS = "sms"
    EMAIL = "email"

# حالات التذكيرات
class ReminderStatus(enum.Enum):
    """حالات التذكيرات"""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"

# تذكيرات المواعيد
class AppointmentReminder(Base):
    """نموذج تذكيرات المواعيد"""
    __tablename__ = "appointment_reminders"
    
    reminder_id = Column(Integer, primary_key=True, index=True)
    appointment_id = Column(Integer, ForeignKey("appointments.appointment_id"))
    reminder_type = Column(Enum(ReminderType))
    reminder_status = Column(Enum(ReminderStatus), default=ReminderStatus.PENDING)
    scheduled_time = Column(DateTime)
    sent_time = Column(DateTime)
    message = Column(Text)
    
    # العلاقات
    appointment = relationship("Appointment", back_populates="reminders")

# الفواتير (مبسطة للبداية)
class Invoice(Base, TimestampMixin):
    """نموذج الفواتير"""
    __tablename__ = "invoices"
    
    invoice_id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.customer_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    invoice_date = Column(DateTime, default=datetime.utcnow)
    total_amount = Column(Numeric(10, 2), nullable=False)
    
    # العلاقات
    customer = relationship("Customer", back_populates="invoices")
    branch = relationship("Branch", back_populates="invoices")
    invoice_items = relationship("InvoiceItem", back_populates="invoice")

# بنود الفواتير
class InvoiceItem(Base):
    """نموذج بنود الفواتير"""
    __tablename__ = "invoice_items"
    
    invoice_item_id = Column(Integer, primary_key=True, index=True)
    invoice_id = Column(Integer, ForeignKey("invoices.invoice_id"))
    service_id = Column(Integer, ForeignKey("services.service_id"))
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # العلاقات
    invoice = relationship("Invoice", back_populates="invoice_items")
    service = relationship("Service", back_populates="invoice_items")
    employee = relationship("Employee")

# عمولات الموظفين
class EmployeeCommission(Base):
    """نموذج عمولات الموظفين"""
    __tablename__ = "employee_commissions"
    
    commission_id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    invoice_item_id = Column(Integer, ForeignKey("invoice_items.invoice_item_id"))
    commission_amount = Column(Numeric(10, 2), nullable=False)
    commission_date = Column(DateTime, default=datetime.utcnow)
    is_paid = Column(Boolean, default=False)
    payment_date = Column(DateTime)
    notes = Column(Text)
    
    # العلاقات
    employee = relationship("Employee", back_populates="commissions")
    invoice_item = relationship("InvoiceItem")
