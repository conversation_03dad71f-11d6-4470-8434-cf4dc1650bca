"""
نماذج المواعيد
Appointment Models
"""

from sqlalchemy import Column, Integer, String, Date, Time, Text, ForeignKey, Enum, DateTime
from sqlalchemy.orm import relationship
from database import Base
from models.base import TimestampMixin
import enum

class AppointmentStatus(enum.Enum):
    """حالات المواعيد"""
    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"

class ReminderType(enum.Enum):
    """أنواع التذكيرات"""
    WHATSAPP = "whatsapp"
    SMS = "sms"
    EMAIL = "email"

class ReminderStatus(enum.Enum):
    """حالات التذكيرات"""
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"

class Appointment(Base, TimestampMixin):
    """نموذج المواعيد"""
    __tablename__ = "appointments"
    
    appointment_id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.customer_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    appointment_date = Column(Date, nullable=False)
    start_time = Column(Time, nullable=False)
    end_time = Column(Time, nullable=False)
    status = Column(Enum(AppointmentStatus), default=AppointmentStatus.SCHEDULED)
    notes = Column(Text)
    created_by = Column(Integer, ForeignKey("users.user_id"))
    
    # العلاقات
    customer = relationship("Customer", back_populates="appointments")
    branch = relationship("Branch", back_populates="appointments")
    creator = relationship("User")
    appointment_services = relationship("AppointmentService", back_populates="appointment")
    reminders = relationship("AppointmentReminder", back_populates="appointment")

class AppointmentService(Base):
    """نموذج خدمات المواعيد"""
    __tablename__ = "appointment_services"
    
    appointment_service_id = Column(Integer, primary_key=True, index=True)
    appointment_id = Column(Integer, ForeignKey("appointments.appointment_id"))
    service_id = Column(Integer, ForeignKey("services.service_id"))
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    
    # العلاقات
    appointment = relationship("Appointment", back_populates="appointment_services")
    service = relationship("Service", back_populates="appointment_services")
    employee = relationship("Employee", back_populates="appointments")

class AppointmentReminder(Base):
    """نموذج تذكيرات المواعيد"""
    __tablename__ = "appointment_reminders"
    
    reminder_id = Column(Integer, primary_key=True, index=True)
    appointment_id = Column(Integer, ForeignKey("appointments.appointment_id"))
    reminder_type = Column(Enum(ReminderType))
    reminder_status = Column(Enum(ReminderStatus), default=ReminderStatus.PENDING)
    scheduled_time = Column(DateTime)
    sent_time = Column(DateTime)
    message = Column(Text)
    
    # العلاقات
    appointment = relationship("Appointment", back_populates="reminders")
