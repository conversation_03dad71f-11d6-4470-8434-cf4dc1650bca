"""
النماذج الأساسية
Base Models for SalonProManager
"""

from sqlalchemy import Column, Integer, DateTime, Boolean, String, Text, Numeric, Date, Time, ForeignKey
from database import Base
from sqlalchemy.orm import relationship
from datetime import datetime

class TimestampMixin:
    """خليط الطوابع الزمنية"""
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Branch(Base, TimestampMixin):
    """نموذج الفروع"""
    __tablename__ = "branches"
    
    branch_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    address = Column(String(255))
    phone = Column(String(20))
    working_hours = Column(String(255))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    user_branches = relationship("UserBranch", back_populates="branch")
    employee_branches = relationship("EmployeeBranch", back_populates="branch")
    appointments = relationship("Appointment", back_populates="branch")
    invoices = relationship("Invoice", back_populates="branch")

class Role(Base):
    """نموذج الأدوار"""
    __tablename__ = "roles"
    
    role_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(String(255))
    
    # العلاقات
    users = relationship("User", back_populates="role")

class User(Base, TimestampMixin):
    """نموذج المستخدمين"""
    __tablename__ = "users"
    
    user_id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), nullable=False, unique=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    role_id = Column(Integer, ForeignKey("roles.role_id"))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    role = relationship("Role", back_populates="users")
    user_branches = relationship("UserBranch", back_populates="user")

class UserBranch(Base):
    """نموذج ربط المستخدمين بالفروع"""
    __tablename__ = "user_branches"
    
    user_branch_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    
    # العلاقات
    user = relationship("User", back_populates="user_branches")
    branch = relationship("Branch", back_populates="user_branches")

class Zone(Base):
    """نموذج المناطق"""
    __tablename__ = "zones"
    
    zone_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(String(255))
    
    # العلاقات
    customers = relationship("Customer", back_populates="zone")
