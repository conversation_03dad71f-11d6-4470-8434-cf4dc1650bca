"""
نماذج العملاء
Customer Models
"""

from sqlalchemy import Column, Integer, String, Date, Text, ForeignKey
from sqlalchemy.orm import relationship
from database import Base
from models.base import TimestampMixin

class Customer(Base, TimestampMixin):
    """نموذج العملاء"""
    __tablename__ = "customers"
    
    customer_id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    phone = Column(String(20), nullable=False)
    email = Column(String(100))
    birth_date = Column(Date)
    zone_id = Column(Integer, ForeignKey("zones.zone_id"))
    notes = Column(Text)
    
    # العلاقات
    zone = relationship("Zone", back_populates="customers")
    appointments = relationship("Appointment", back_populates="customer")
    invoices = relationship("Invoice", back_populates="customer")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"
