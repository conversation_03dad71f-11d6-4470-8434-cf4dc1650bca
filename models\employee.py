"""
نماذج الموظفين
Employee Models
"""

from sqlalchemy import Column, Integer, String, Date, Boolean, Numeric, ForeignKey, Enum
from sqlalchemy.orm import relationship
from database import Base
from models.base import TimestampMixin
import enum

class CommissionType(enum.Enum):
    """أنواع العمولة"""
    PERCENTAGE = "percentage"
    FIXED = "fixed"
    SERVICE_BASED = "service_based"

class Employee(Base, TimestampMixin):
    """نموذج الموظفين"""
    __tablename__ = "employees"
    
    employee_id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    phone = Column(String(20))
    email = Column(String(100))
    position = Column(String(50))
    commission_type = Column(Enum(CommissionType))
    commission_value = Column(Numeric(10, 2))
    hire_date = Column(Date)
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    employee_branches = relationship("EmployeeBranch", back_populates="employee")
    employee_services = relationship("EmployeeService", back_populates="employee")
    appointments = relationship("AppointmentService", back_populates="employee")
    commissions = relationship("EmployeeCommission", back_populates="employee")
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"

class EmployeeBranch(Base):
    """نموذج ربط الموظفين بالفروع"""
    __tablename__ = "employee_branches"
    
    employee_branch_id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    branch_id = Column(Integer, ForeignKey("branches.branch_id"))
    
    # العلاقات
    employee = relationship("Employee", back_populates="employee_branches")
    branch = relationship("Branch", back_populates="employee_branches")

class EmployeeService(Base):
    """نموذج ربط الموظفين بالخدمات"""
    __tablename__ = "employee_services"
    
    employee_service_id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.employee_id"))
    service_id = Column(Integer, ForeignKey("services.service_id"))
    
    # العلاقات
    employee = relationship("Employee", back_populates="employee_services")
    service = relationship("Service", back_populates="employee_services")
