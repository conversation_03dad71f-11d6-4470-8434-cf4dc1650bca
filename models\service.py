"""
نماذج الخدمات
Service Models
"""

from sqlalchemy import Column, Integer, String, Text, Numeric, Boolean, Enum
from sqlalchemy.orm import relationship
from database import Base
from models.employee import CommissionType

class Service(Base):
    """نموذج الخدمات"""
    __tablename__ = "services"
    
    service_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    price = Column(Numeric(10, 2), nullable=False)
    duration = Column(Integer, nullable=False)  # بالدقائق
    commission_type = Column(Enum(CommissionType))
    commission_value = Column(Numeric(10, 2))
    is_active = Column(Boolean, default=True)
    
    # العلاقات
    employee_services = relationship("EmployeeService", back_populates="service")
    appointment_services = relationship("AppointmentService", back_populates="service")
    invoice_items = relationship("InvoiceItem", back_populates="service")
