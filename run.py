"""
ملف التشغيل السريع
Quick Start Script
"""

import subprocess
import sys
import time
import threading
from pathlib import Path

def run_api():
    """تشغيل API"""
    print("🚀 بدء تشغيل API...")
    subprocess.run([sys.executable, "main.py"])

def run_streamlit():
    """تشغيل واجهة المستخدم"""
    print("🎨 بدء تشغيل واجهة المستخدم...")
    time.sleep(3)  # انتظار حتى يبدأ API
    subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_app.py"])

def main():
    """التشغيل الرئيسي"""
    print("=" * 50)
    print("🏢 SalonProManager - نظام إدارة صالونات متكامل")
    print("=" * 50)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = [
        "main.py",
        "streamlit_app.py",
        "config.py",
        "database.py"
    ]
    
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ الملف المطلوب غير موجود: {file}")
            return
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # إعداد البيانات الأولية
    print("📊 إعداد البيانات الأولية...")
    try:
        subprocess.run([sys.executable, "init_data.py"], check=True)
        print("✅ تم إعداد البيانات الأولية بنجاح")
    except subprocess.CalledProcessError:
        print("⚠️ تحذير: قد تكون البيانات الأولية موجودة بالفعل")
    
    # تشغيل التطبيقات
    print("\n🚀 بدء تشغيل التطبيق...")
    print("📍 API سيعمل على: http://localhost:8000")
    print("📍 واجهة المستخدم ستعمل على: http://localhost:8501")
    print("\n⏳ يرجى الانتظار...")
    
    # تشغيل API في thread منفصل
    api_thread = threading.Thread(target=run_api)
    api_thread.daemon = True
    api_thread.start()
    
    # تشغيل Streamlit
    run_streamlit()

if __name__ == "__main__":
    main()
