import streamlit as st
from datetime import datetime
import pandas as pd

# إعداد الصفحة
st.set_page_config(
    page_title="SalonProManager - نظام إدارة الصالون",
    page_icon="💄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# تهيئة session state
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'user' not in st.session_state:
    st.session_state.user = None
if 'current_page' not in st.session_state:
    st.session_state.current_page = 'login'
if 'current_branch_id' not in st.session_state:
    st.session_state.current_branch_id = 1
if 'current_branch_name' not in st.session_state:
    st.session_state.current_branch_name = 'فرع مدينة نصر'
if 'invoice_items' not in st.session_state:
    st.session_state.invoice_items = []
if 'invoices' not in st.session_state:
    st.session_state.invoices = []
if 'customers' not in st.session_state:
    st.session_state.customers = []

# بيانات النظام
BRANCHES = {
    1: {"name": "فرع مدينة نصر", "price_tier": "premium"},
    2: {"name": "فرع جسر السويس", "price_tier": "standard"}, 
    3: {"name": "فرع الزهراء", "price_tier": "economy"}
}

# نظام الأسعار المتعدد للخدمات
SERVICES_PRICES = {
    "قص شعر بروفيشنال": {"premium": 260, "standard": 200, "economy": 160},
    "دقن ستايلنج": {"premium": 195, "standard": 150, "economy": 120},
    "صبغة شعر كاملة": {"premium": 390, "standard": 300, "economy": 240},
    "استشوار برو": {"premium": 260, "standard": 200, "economy": 160},
    "مكواة كيرلي بروفيشنال": {"premium": 650, "standard": 500, "economy": 400},
    "بشرة لايت": {"premium": 650, "standard": 500, "economy": 400},
    "هيدرو فيشال": {"premium": 2600, "standard": 2000, "economy": 1600},
    "أكسچنيو الفاخرة": {"premium": 3900, "standard": 3000, "economy": 2400},
    "باديكير يد وقدم": {"premium": 650, "standard": 500, "economy": 400},
    "باديكير عناية كاملة + مساج": {"premium": 910, "standard": 700, "economy": 560},
    "كولاجين للشعر": {"premium": 1950, "standard": 1500, "economy": 1200},
    "بوتوكس للشعر": {"premium": 2340, "standard": 1800, "economy": 1440},
    "كيراتين للشعر": {"premium": 2600, "standard": 2000, "economy": 1600}
}

# نظام الأسعار المتعدد للمنتجات
PRODUCTS_PRICES = {
    "شامبو لوريال": {"premium": 156, "standard": 120, "economy": 96},
    "كريم فرد الشعر": {"premium": 325, "standard": 250, "economy": 200},
    "صبغة شعر احترافية": {"premium": 455, "standard": 350, "economy": 280},
    "كريم تنظيف البشرة": {"premium": 130, "standard": 100, "economy": 80},
    "ماسك الوجه": {"premium": 104, "standard": 80, "economy": 64},
    "مكواة شعر احترافية": {"premium": 1560, "standard": 1200, "economy": 960},
    "مجفف شعر": {"premium": 1170, "standard": 900, "economy": 720},
    "أحمر شفاه": {"premium": 195, "standard": 150, "economy": 120},
    "كريم أساس": {"premium": 260, "standard": 200, "economy": 160}
}

EMPLOYEES = ["عمر محمد", "عمر فلانتينو", "أسامة", "أحمد عمر", "ياسمين"]

# دالة للحصول على السعر حسب الفرع
def get_price(item_name, item_type, branch_id):
    tier = BRANCHES[branch_id]["price_tier"]
    if item_type == "service":
        return SERVICES_PRICES.get(item_name, {}).get(tier, 0)
    else:
        return PRODUCTS_PRICES.get(item_name, {}).get(tier, 0)

# CSS للتصميم
st.markdown("""
<style>
.main-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 1rem;
}
.cashier-container {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin: 1rem 0;
}
.search-result {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
    transition: all 0.2s ease;
}
.search-result:hover {
    border-color: #3b82f6;
    background: #f0f9ff;
}
.invoice-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.total-section {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
}
</style>
""", unsafe_allow_html=True)

# دالة تسجيل الدخول
def login_page():
    st.markdown("""
    <div class="main-header">
        <h1>🔐 تسجيل الدخول</h1>
        <h3>SalonProManager</h3>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        with st.form("login_form"):
            username = st.text_input("اسم المستخدم", placeholder="أدخل اسم المستخدم")
            password = st.text_input("كلمة المرور", type="password", placeholder="أدخل كلمة المرور")
            branch_id = st.selectbox("اختر الفرع", 
                options=list(BRANCHES.keys()),
                format_func=lambda x: BRANCHES[x]["name"])
            
            login_btn = st.form_submit_button("🚀 دخول", use_container_width=True)
            
            if login_btn:
                # قائمة المستخدمين المسموح لهم
                valid_users = {
                    "admin": {"password": "123", "full_name": "المدير العام", "role": "admin"},
                    "manager": {"password": "123", "full_name": "مدير الفرع", "role": "manager"},
                    "cashier": {"password": "123", "full_name": "الكاشير", "role": "cashier"},
                    "user": {"password": "123", "full_name": "موظف", "role": "employee"}
                }

                if username in valid_users and password == valid_users[username]["password"]:
                    st.session_state.logged_in = True
                    st.session_state.user = {
                        "username": username,
                        "full_name": valid_users[username]["full_name"],
                        "role": valid_users[username]["role"]
                    }
                    st.session_state.current_branch_id = branch_id
                    st.session_state.current_branch_name = BRANCHES[branch_id]["name"]
                    st.session_state.current_page = "cashier"
                    st.success(f"✅ مرحباً {valid_users[username]['full_name']}")
                    st.rerun()
                else:
                    st.error("❌ اسم المستخدم أو كلمة المرور غير صحيحة")
                    st.info("💡 المستخدمين المتاحين: admin, manager, cashier, user - كلمة المرور: 123")

# دالة الشريط الجانبي
def sidebar():
    with st.sidebar:
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
                    color: white; padding: 1rem; border-radius: 12px; text-align: center; margin-bottom: 1rem;">
            <h3>👤 {st.session_state.user['full_name']}</h3>
            <p>🏢 {st.session_state.current_branch_name}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # قائمة التنقل
        pages = {
            "cashier": "🧾 نظام الكاشير",
            "dashboard": "📊 لوحة التحكم", 
            "invoices": "📋 إدارة الفواتير",
            "customers": "👥 إدارة العملاء",
            "reports": "📈 التقارير",
            "settings": "⚙️ الإعدادات"
        }
        
        for page_key, page_name in pages.items():
            if st.button(page_name, key=f"nav_{page_key}", use_container_width=True):
                st.session_state.current_page = page_key
                st.rerun()
        
        st.divider()
        
        if st.button("🚪 تسجيل الخروج", use_container_width=True):
            st.session_state.logged_in = False
            st.session_state.user = None
            st.session_state.current_page = "login"
            st.rerun()

# دالة نظام الكاشير
def cashier_page():
    st.markdown("""
    <div class="main-header">
        <h1>🧾 نظام الكاشير</h1>
        <p>نظام فواتير احترافي مثل برامج الماركت</p>
    </div>
    """, unsafe_allow_html=True)
    
    # معلومات الفرع والكاشير
    col1, col2, col3 = st.columns(3)
    
    with col1:
        current_branch_name = st.session_state.current_branch_name
        current_branch_id = st.session_state.current_branch_id
        tier_names = {"premium": "🌟 بريميوم", "standard": "⭐ عادية", "economy": "💰 اقتصادية"}
        tier_name = tier_names[BRANCHES[current_branch_id]["price_tier"]]
        
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
                    color: white; padding: 1rem; border-radius: 12px; text-align: center;">
            <h4>🏢 {current_branch_name}</h4>
            <p>الفئة السعرية: {tier_name}</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        cashier_name = st.session_state.user.get('username', 'غير محدد')
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); 
                    color: white; padding: 1rem; border-radius: 12px; text-align: center;">
            <h4>👤 الكاشير: {cashier_name}</h4>
            <p>⏰ {datetime.now().strftime('%H:%M - %Y/%m/%d')}</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        next_invoice_id = len(st.session_state.invoices) + 1
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); 
                    color: white; padding: 1rem; border-radius: 12px; text-align: center;">
            <h4>📄 رقم الفاتورة</h4>
            <p>INV-{next_invoice_id:04d}</p>
        </div>
        """, unsafe_allow_html=True)

    st.divider()
    
    # نظام البحث - مثل برنامج الماركت
    st.markdown("### 🔍 البحث وإضافة الأصناف")
    
    search_col1, search_col2 = st.columns([3, 1])
    
    with search_col1:
        search_term = st.text_input(
            "🔍 ابحث عن خدمة أو منتج...", 
            placeholder="اكتب اسم الخدمة أو المنتج للبحث",
            key="main_search"
        )
    
    with search_col2:
        payment_method = st.selectbox("طريقة الدفع", 
            ["نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية"])

    # الحصول على الأسعار حسب الفرع الحالي
    current_tier = BRANCHES[st.session_state.current_branch_id]["price_tier"]

    # دمج جميع الأصناف (خدمات + منتجات)
    all_items = {}
    
    # إضافة الخدمات
    for service_name, prices in SERVICES_PRICES.items():
        all_items[service_name] = {
            'type': 'service',
            'price': prices[current_tier],
            'icon': '💆‍♀️'
        }
        
    # إضافة المنتجات
    for product_name, prices in PRODUCTS_PRICES.items():
        all_items[product_name] = {
            'type': 'product', 
            'price': prices[current_tier],
            'icon': '🛍️'
        }

    # عرض نتائج البحث
    if search_term:
        filtered_items = {k: v for k, v in all_items.items() 
                        if search_term.lower() in k.lower()}
        
        if filtered_items:
            st.markdown("#### 🎯 نتائج البحث:")
            
            # عرض النتائج في بطاقات قابلة للنقر
            for item_name, item_data in list(filtered_items.items())[:8]:  # أول 8 نتائج
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                
                with col1:
                    st.markdown(f"""
                    <div class="search-result">
                        <span style="font-size: 1.1rem;">{item_data['icon']} {item_name}</span>
                    </div>
                    """, unsafe_allow_html=True)
                
                with col2:
                    st.markdown(f"""
                    <div style="background: #f0f9ff; border-radius: 6px; padding: 0.5rem; text-align: center;">
                        <strong style="color: #1e40af;">{item_data['price']} ج.م</strong>
                    </div>
                    """, unsafe_allow_html=True)
                
                with col3:
                    quantity = st.number_input("الكمية", min_value=1, value=1, 
                                             key=f"qty_{item_name}", label_visibility="collapsed")
                
                with col4:
                    if st.button(f"➕ إضافة", key=f"add_{item_name}", use_container_width=True):
                        # إضافة العنصر للفاتورة
                        new_item = {
                            'name': item_name,
                            'type': item_data['type'],
                            'price': item_data['price'],
                            'quantity': quantity,
                            'total': item_data['price'] * quantity,
                            'employee': 'غير محدد'
                        }
                        
                        st.session_state.invoice_items.append(new_item)
                        st.success(f"✅ تم إضافة {item_name} للفاتورة!")
                        st.rerun()
        else:
            st.warning("❌ لم يتم العثور على نتائج للبحث")

    st.divider()
    
    # عرض الفاتورة الحالية
    if st.session_state.invoice_items:
        st.markdown("### 📋 الفاتورة الحالية")
        
        # جدول العناصر
        st.markdown('<div class="invoice-table">', unsafe_allow_html=True)
        
        # رأس الجدول
        col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])
        with col1:
            st.markdown("**الصنف**")
        with col2:
            st.markdown("**الكمية**")
        with col3:
            st.markdown("**السعر**")
        with col4:
            st.markdown("**الإجمالي**")
        with col5:
            st.markdown("**حذف**")
        
        st.divider()
        
        # عرض العناصر
        for i, item in enumerate(st.session_state.invoice_items):
            col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])
            
            with col1:
                icon = "💆‍♀️" if item['type'] == 'service' else "🛍️"
                st.write(f"{icon} {item['name']}")
            with col2:
                st.write(f"{item['quantity']}")
            with col3:
                st.write(f"{item['price']} ج.م")
            with col4:
                st.write(f"{item['total']} ج.م")
            with col5:
                if st.button("🗑️", key=f"delete_{i}", help="حذف العنصر"):
                    st.session_state.invoice_items.pop(i)
                    st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # حسابات الفاتورة
        subtotal = sum([item['total'] for item in st.session_state.invoice_items])
        
        st.divider()
        
        col1, col2 = st.columns(2)
        
        with col1:
            discount_percent = st.number_input("نسبة الخصم (%)", min_value=0.0, max_value=100.0, value=0.0)
            tax_percent = st.number_input("نسبة الضريبة (%)", min_value=0.0, max_value=100.0, value=0.0)
        
        with col2:
            discount_amount = subtotal * (discount_percent / 100)
            tax_amount = (subtotal - discount_amount) * (tax_percent / 100)
            total_amount = subtotal - discount_amount + tax_amount
            
            st.markdown(f"""
            <div class="total-section">
                <div>المجموع الفرعي: {subtotal:.2f} ج.م</div>
                <div>الخصم: -{discount_amount:.2f} ج.م</div>
                <div>الضريبة: +{tax_amount:.2f} ج.م</div>
                <hr style="margin: 1rem 0;">
                <div style="font-size: 2rem;">الإجمالي: {total_amount:.2f} ج.م</div>
            </div>
            """, unsafe_allow_html=True)
        
        st.divider()
        
        # أزرار الحفظ
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("✅ إنشاء الفاتورة", use_container_width=True, type="primary"):
                # حفظ الفاتورة
                new_invoice = {
                    'id': len(st.session_state.invoices) + 1,
                    'items': st.session_state.invoice_items.copy(),
                    'subtotal': subtotal,
                    'discount': discount_amount,
                    'tax': tax_amount,
                    'total': total_amount,
                    'payment_method': payment_method,
                    'date': datetime.now().strftime('%Y-%m-%d'),
                    'time': datetime.now().strftime('%H:%M'),
                    'branch_id': st.session_state.current_branch_id,
                    'cashier': st.session_state.user['username']
                }
                
                st.session_state.invoices.append(new_invoice)
                st.session_state.invoice_items = []
                st.success("🎉 تم إنشاء الفاتورة بنجاح!")
                st.balloons()
                st.rerun()
        
        with col2:
            if st.button("💾 حفظ كمسودة", use_container_width=True):
                st.info("💾 تم حفظ الفاتورة كمسودة")
        
        with col3:
            if st.button("🗑️ مسح الكل", use_container_width=True):
                st.session_state.invoice_items = []
                st.rerun()
    
    else:
        st.info("📝 ابحث عن الخدمات والمنتجات لإضافتها للفاتورة")

# دالة لوحة التحكم
def dashboard_page():
    st.markdown("""
    <div class="main-header">
        <h1>📊 لوحة التحكم</h1>
        <p>نظرة عامة على أداء الصالون</p>
    </div>
    """, unsafe_allow_html=True)

    # إحصائيات سريعة
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_invoices = len(st.session_state.invoices)
        st.metric("📋 إجمالي الفواتير", total_invoices)

    with col2:
        total_revenue = sum([inv['total'] for inv in st.session_state.invoices])
        st.metric("💰 إجمالي المبيعات", f"{total_revenue:.2f} ج.م")

    with col3:
        today_invoices = [inv for inv in st.session_state.invoices
                         if inv['date'] == datetime.now().strftime('%Y-%m-%d')]
        st.metric("📅 فواتير اليوم", len(today_invoices))

    with col4:
        avg_invoice = total_revenue / total_invoices if total_invoices > 0 else 0
        st.metric("📈 متوسط الفاتورة", f"{avg_invoice:.2f} ج.م")

    st.divider()

    if st.session_state.invoices:
        st.markdown("### 📋 آخر الفواتير")

        # عرض آخر 5 فواتير
        recent_invoices = st.session_state.invoices[-5:]

        for invoice in reversed(recent_invoices):
            with st.expander(f"فاتورة رقم {invoice['id']} - {invoice['total']:.2f} ج.م"):
                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"📅 التاريخ: {invoice['date']}")
                    st.write(f"⏰ الوقت: {invoice['time']}")
                    st.write(f"💳 طريقة الدفع: {invoice['payment_method']}")
                with col2:
                    st.write(f"👤 الكاشير: {invoice['cashier']}")
                    st.write(f"🏢 الفرع: {BRANCHES[invoice['branch_id']]['name']}")
                    st.write(f"📦 عدد الأصناف: {len(invoice['items'])}")

# دالة إدارة الفواتير
def invoices_page():
    st.markdown("""
    <div class="main-header">
        <h1>📋 إدارة الفواتير</h1>
        <p>عرض وإدارة جميع الفواتير</p>
    </div>
    """, unsafe_allow_html=True)

    if st.session_state.invoices:
        # فلترة الفواتير
        col1, col2, col3 = st.columns(3)

        with col1:
            date_filter = st.date_input("فلترة حسب التاريخ", value=datetime.now().date())

        with col2:
            branch_filter = st.selectbox("فلترة حسب الفرع",
                options=["الكل"] + [BRANCHES[bid]["name"] for bid in BRANCHES.keys()])

        with col3:
            payment_filter = st.selectbox("فلترة حسب طريقة الدفع",
                options=["الكل", "نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية"])

        # تطبيق الفلاتر
        filtered_invoices = st.session_state.invoices.copy()

        if date_filter:
            filtered_invoices = [inv for inv in filtered_invoices
                               if inv['date'] == date_filter.strftime('%Y-%m-%d')]

        if branch_filter != "الكل":
            branch_id = next(bid for bid, data in BRANCHES.items() if data["name"] == branch_filter)
            filtered_invoices = [inv for inv in filtered_invoices if inv['branch_id'] == branch_id]

        if payment_filter != "الكل":
            filtered_invoices = [inv for inv in filtered_invoices if inv['payment_method'] == payment_filter]

        st.markdown(f"### 📊 النتائج: {len(filtered_invoices)} فاتورة")

        # عرض الفواتير
        for invoice in reversed(filtered_invoices):
            with st.expander(f"فاتورة رقم {invoice['id']} - {invoice['total']:.2f} ج.م - {invoice['date']}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("**معلومات الفاتورة:**")
                    st.write(f"📅 التاريخ: {invoice['date']}")
                    st.write(f"⏰ الوقت: {invoice['time']}")
                    st.write(f"💳 طريقة الدفع: {invoice['payment_method']}")
                    st.write(f"👤 الكاشير: {invoice['cashier']}")
                    st.write(f"🏢 الفرع: {BRANCHES[invoice['branch_id']]['name']}")

                with col2:
                    st.markdown("**تفاصيل المبالغ:**")
                    st.write(f"💰 المجموع الفرعي: {invoice['subtotal']:.2f} ج.م")
                    st.write(f"💸 الخصم: {invoice['discount']:.2f} ج.م")
                    st.write(f"📊 الضريبة: {invoice['tax']:.2f} ج.م")
                    st.write(f"🎯 **الإجمالي: {invoice['total']:.2f} ج.م**")

                st.markdown("**الأصناف:**")
                for item in invoice['items']:
                    icon = "💆‍♀️" if item['type'] == 'service' else "🛍️"
                    st.write(f"{icon} {item['name']} - الكمية: {item['quantity']} - السعر: {item['price']} ج.م - الإجمالي: {item['total']} ج.م")
    else:
        st.info("📝 لا توجد فواتير بعد")

# دالة إدارة العملاء
def customers_page():
    st.markdown("""
    <div class="main-header">
        <h1>👥 إدارة العملاء</h1>
        <p>إضافة وإدارة بيانات العملاء</p>
    </div>
    """, unsafe_allow_html=True)

    # إضافة عميل جديد
    with st.expander("➕ إضافة عميل جديد"):
        with st.form("add_customer"):
            col1, col2 = st.columns(2)

            with col1:
                customer_name = st.text_input("اسم العميل")
                customer_phone = st.text_input("رقم الهاتف")

            with col2:
                customer_email = st.text_input("البريد الإلكتروني")
                customer_address = st.text_area("العنوان")

            if st.form_submit_button("💾 حفظ العميل"):
                if customer_name and customer_phone:
                    new_customer = {
                        'id': len(st.session_state.customers) + 1,
                        'name': customer_name,
                        'phone': customer_phone,
                        'email': customer_email,
                        'address': customer_address,
                        'branch_id': st.session_state.current_branch_id,
                        'created_date': datetime.now().strftime('%Y-%m-%d')
                    }

                    st.session_state.customers.append(new_customer)
                    st.success("✅ تم إضافة العميل بنجاح!")
                    st.rerun()
                else:
                    st.error("❌ يرجى إدخال اسم العميل ورقم الهاتف على الأقل")

    # عرض العملاء
    if st.session_state.customers:
        st.markdown("### 📋 قائمة العملاء")

        # فلترة العملاء حسب الفرع الحالي
        branch_customers = [c for c in st.session_state.customers
                          if c['branch_id'] == st.session_state.current_branch_id]

        if branch_customers:
            for customer in branch_customers:
                with st.expander(f"👤 {customer['name']} - {customer['phone']}"):
                    col1, col2 = st.columns(2)

                    with col1:
                        st.write(f"📞 الهاتف: {customer['phone']}")
                        st.write(f"📧 الإيميل: {customer.get('email', 'غير محدد')}")

                    with col2:
                        st.write(f"📍 العنوان: {customer.get('address', 'غير محدد')}")
                        st.write(f"📅 تاريخ التسجيل: {customer['created_date']}")
        else:
            st.info("📝 لا يوجد عملاء مسجلين في هذا الفرع")
    else:
        st.info("📝 لا يوجد عملاء مسجلين")

# دالة التقارير
def reports_page():
    st.markdown("""
    <div class="main-header">
        <h1>📈 التقارير</h1>
        <p>تقارير مفصلة عن أداء الصالون</p>
    </div>
    """, unsafe_allow_html=True)

    if st.session_state.invoices:
        # تقرير المبيعات
        st.markdown("### 💰 تقرير المبيعات")

        # فلترة حسب التاريخ
        col1, col2 = st.columns(2)

        with col1:
            start_date = st.date_input("من تاريخ", value=datetime.now().date())

        with col2:
            end_date = st.date_input("إلى تاريخ", value=datetime.now().date())

        # تطبيق الفلتر
        filtered_invoices = [inv for inv in st.session_state.invoices
                           if start_date.strftime('%Y-%m-%d') <= inv['date'] <= end_date.strftime('%Y-%m-%d')]

        if filtered_invoices:
            total_sales = sum([inv['total'] for inv in filtered_invoices])
            total_discount = sum([inv['discount'] for inv in filtered_invoices])
            total_tax = sum([inv['tax'] for inv in filtered_invoices])

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("📋 عدد الفواتير", len(filtered_invoices))

            with col2:
                st.metric("💰 إجمالي المبيعات", f"{total_sales:.2f} ج.م")

            with col3:
                st.metric("💸 إجمالي الخصومات", f"{total_discount:.2f} ج.م")

            with col4:
                st.metric("📊 إجمالي الضرائب", f"{total_tax:.2f} ج.م")

            # تقرير الخدمات الأكثر مبيعاً
            st.markdown("### 🏆 الخدمات الأكثر مبيعاً")

            services_sales = {}
            for invoice in filtered_invoices:
                for item in invoice['items']:
                    if item['type'] == 'service':
                        if item['name'] not in services_sales:
                            services_sales[item['name']] = {'quantity': 0, 'revenue': 0}
                        services_sales[item['name']]['quantity'] += item['quantity']
                        services_sales[item['name']]['revenue'] += item['total']

            if services_sales:
                sorted_services = sorted(services_sales.items(),
                                       key=lambda x: x[1]['revenue'], reverse=True)

                for i, (service_name, data) in enumerate(sorted_services[:5]):
                    st.write(f"{i+1}. 💆‍♀️ {service_name} - الكمية: {data['quantity']} - الإيرادات: {data['revenue']:.2f} ج.م")
        else:
            st.info("📝 لا توجد فواتير في الفترة المحددة")
    else:
        st.info("📝 لا توجد بيانات للتقارير")

# دالة الإعدادات
def settings_page():
    st.markdown("""
    <div class="main-header">
        <h1>⚙️ الإعدادات</h1>
        <p>إعدادات النظام والصالون</p>
    </div>
    """, unsafe_allow_html=True)

    # إعدادات الفرع
    st.markdown("### 🏢 إعدادات الفرع")

    current_branch = BRANCHES[st.session_state.current_branch_id]

    col1, col2 = st.columns(2)

    with col1:
        st.info(f"**الفرع الحالي:** {current_branch['name']}")
        st.info(f"**الفئة السعرية:** {current_branch['price_tier']}")

    with col2:
        if st.button("🔄 تغيير الفرع"):
            st.session_state.current_page = "login"
            st.session_state.logged_in = False
            st.rerun()

    st.divider()

    # إعدادات الأسعار
    st.markdown("### 💰 عرض الأسعار")

    tier_names = {"premium": "🌟 بريميوم", "standard": "⭐ عادية", "economy": "💰 اقتصادية"}

    for tier, name in tier_names.items():
        with st.expander(f"أسعار الفئة {name}"):
            st.markdown("**الخدمات:**")
            for service, prices in SERVICES_PRICES.items():
                st.write(f"💆‍♀️ {service}: {prices[tier]} ج.م")

            st.markdown("**المنتجات:**")
            for product, prices in PRODUCTS_PRICES.items():
                st.write(f"🛍️ {product}: {prices[tier]} ج.م")

    st.divider()

    # إعدادات النظام
    st.markdown("### 🔧 إعدادات النظام")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🗑️ مسح جميع الفواتير", type="secondary"):
            if st.button("⚠️ تأكيد المسح", type="secondary"):
                st.session_state.invoices = []
                st.success("✅ تم مسح جميع الفواتير")
                st.rerun()

    with col2:
        if st.button("🗑️ مسح جميع العملاء", type="secondary"):
            if st.button("⚠️ تأكيد المسح", type="secondary"):
                st.session_state.customers = []
                st.success("✅ تم مسح جميع العملاء")
                st.rerun()

# التطبيق الرئيسي
def main():
    if not st.session_state.logged_in:
        login_page()
    else:
        sidebar()
        
        if st.session_state.current_page == "cashier":
            cashier_page()
        elif st.session_state.current_page == "dashboard":
            dashboard_page()
        elif st.session_state.current_page == "invoices":
            invoices_page()
        elif st.session_state.current_page == "customers":
            customers_page()
        elif st.session_state.current_page == "reports":
            reports_page()
        elif st.session_state.current_page == "settings":
            settings_page()

if __name__ == "__main__":
    main()
