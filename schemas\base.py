"""
المخططات الأساسية
Base Schemas for API
"""

from pydantic import BaseModel, ConfigDict
from datetime import datetime, date, time
from typing import Optional, List
from enum import Enum

class BaseSchema(BaseModel):
    """المخطط الأساسي"""
    model_config = ConfigDict(from_attributes=True)

class TimestampSchema(BaseSchema):
    """مخطط الطوابع الزمنية"""
    created_at: datetime
    updated_at: datetime

# مخططات الفروع
class BranchBase(BaseSchema):
    name: str
    address: Optional[str] = None
    phone: Optional[str] = None
    working_hours: Optional[str] = None
    is_active: bool = True

class BranchCreate(BranchBase):
    pass

class BranchUpdate(BranchBase):
    name: Optional[str] = None
    is_active: Optional[bool] = None

class Branch(BranchBase, TimestampSchema):
    branch_id: int

# مخططات الأدوار
class RoleBase(BaseSchema):
    name: str
    description: Optional[str] = None

class RoleCreate(RoleBase):
    pass

class Role(RoleBase):
    role_id: int

# مخططات المستخدمين
class UserBase(BaseSchema):
    username: str
    full_name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    role_id: int
    is_active: bool = True

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseSchema):
    full_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    role_id: Optional[int] = None
    is_active: Optional[bool] = None

class User(UserBase, TimestampSchema):
    user_id: int
    role: Optional[Role] = None

# مخططات المناطق
class ZoneBase(BaseSchema):
    name: str
    description: Optional[str] = None

class ZoneCreate(ZoneBase):
    pass

class Zone(ZoneBase):
    zone_id: int

# مخططات العملاء
class CustomerBase(BaseSchema):
    first_name: str
    last_name: str
    phone: str
    email: Optional[str] = None
    birth_date: Optional[date] = None
    zone_id: Optional[int] = None
    notes: Optional[str] = None

class CustomerCreate(CustomerBase):
    pass

class CustomerUpdate(BaseSchema):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    birth_date: Optional[date] = None
    zone_id: Optional[int] = None
    notes: Optional[str] = None

class Customer(CustomerBase, TimestampSchema):
    customer_id: int
    zone: Optional[Zone] = None
