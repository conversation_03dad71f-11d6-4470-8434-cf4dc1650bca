"""
خدمات WhatsApp
WhatsApp Integration Service
"""

from twilio.rest import Client
from config import settings
import logging

logger = logging.getLogger(__name__)

class WhatsAppService:
    """خدمة WhatsApp"""
    
    def __init__(self):
        if settings.TWILIO_ACCOUNT_SID and settings.TWILIO_AUTH_TOKEN:
            self.client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            self.enabled = True
        else:
            self.client = None
            self.enabled = False
            logger.warning("WhatsApp service not configured")
    
    def send_message(self, to: str, message: str) -> bool:
        """إرسال رسالة WhatsApp"""
        if not self.enabled:
            logger.warning("WhatsApp service not enabled")
            return False
        
        try:
            # التأكد من تنسيق رقم الهاتف
            if not to.startswith("whatsapp:"):
                to = f"whatsapp:{to}"
            
            message = self.client.messages.create(
                body=message,
                from_=settings.WHATSAPP_FROM,
                to=to
            )
            
            logger.info(f"WhatsApp message sent successfully: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send WhatsApp message: {e}")
            return False
    
    def send_appointment_confirmation(self, customer_phone: str, appointment_details: dict) -> bool:
        """إرسال تأكيد الموعد"""
        message = f"""
🏢 تأكيد حجز موعد - SalonProManager

عزيزي/عزيزتي {appointment_details.get('customer_name', '')},

تم تأكيد حجز موعدك بنجاح:

📅 التاريخ: {appointment_details.get('date', '')}
🕐 الوقت: {appointment_details.get('time', '')}
✨ الخدمة: {appointment_details.get('service', '')}
👨‍💼 الخبير: {appointment_details.get('employee', '')}
🏢 الفرع: {appointment_details.get('branch', '')}

نتطلع لرؤيتك!

للاستفسار: {appointment_details.get('branch_phone', '')}
        """.strip()
        
        return self.send_message(customer_phone, message)
    
    def send_appointment_reminder(self, customer_phone: str, appointment_details: dict) -> bool:
        """إرسال تذكير الموعد"""
        message = f"""
⏰ تذكير بموعدك - SalonProManager

عزيزي/عزيزتي {appointment_details.get('customer_name', '')},

نذكرك بموعدك غداً:

📅 التاريخ: {appointment_details.get('date', '')}
🕐 الوقت: {appointment_details.get('time', '')}
✨ الخدمة: {appointment_details.get('service', '')}
👨‍💼 الخبير: {appointment_details.get('employee', '')}
🏢 الفرع: {appointment_details.get('branch', '')}

نتطلع لرؤيتك!

للاستفسار أو التأجيل: {appointment_details.get('branch_phone', '')}
        """.strip()
        
        return self.send_message(customer_phone, message)
    
    def send_invoice(self, customer_phone: str, invoice_details: dict) -> bool:
        """إرسال الفاتورة"""
        message = f"""
🧾 فاتورة - SalonProManager

عزيزي/عزيزتي {invoice_details.get('customer_name', '')},

شكراً لزيارتك! إليك تفاصيل فاتورتك:

📄 رقم الفاتورة: {invoice_details.get('invoice_number', '')}
📅 التاريخ: {invoice_details.get('date', '')}
💰 المبلغ الإجمالي: {invoice_details.get('total_amount', '')} ر.س

نشكرك على ثقتك بنا!

🏢 {invoice_details.get('branch', '')}
📞 {invoice_details.get('branch_phone', '')}
        """.strip()
        
        return self.send_message(customer_phone, message)

# إنشاء مثيل واحد للاستخدام
whatsapp_service = WhatsAppService()
