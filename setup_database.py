#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إنشاء قاعدة البيانات من الصفر
"""

import pyodbc
import sys

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def recreate_database():
    """إنشاء قاعدة البيانات من الصفر"""
    try:
        print("🔄 جاري الاتصال بقاعدة البيانات...")
        conn = get_db_connection()
        if conn is None:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False

        cursor = conn.cursor()
        
        print("🔄 جاري حذف الجداول القديمة...")

        # حذف الجداول الموجودة بالترتيب الصحيح
        drop_tables = [
            "DROP TABLE IF EXISTS invoice_items",
            "DROP TABLE IF EXISTS invoices", 
            "DROP TABLE IF EXISTS appointments",
            "DROP TABLE IF EXISTS cash_transactions",
            "DROP TABLE IF EXISTS categories",
            "DROP TABLE IF EXISTS items",
            "DROP TABLE IF EXISTS services",
            "DROP TABLE IF EXISTS products",
            "DROP TABLE IF EXISTS customers",
            "DROP TABLE IF EXISTS employees",
            "DROP TABLE IF EXISTS users",
            "DROP TABLE IF EXISTS branches"
        ]

        for drop_sql in drop_tables:
            try:
                cursor.execute(drop_sql)
                conn.commit()
                print(f"✅ تم حذف الجدول: {drop_sql.split()[-1]}")
            except Exception as e:
                print(f"⚠️ تجاهل خطأ حذف الجدول: {str(e)}")

        print("✅ تم حذف الجداول القديمة")
        print("🔄 جاري إنشاء الجداول الجديدة...")

        # جدول الفروع
        branches_table = """
        CREATE TABLE branches (
            branch_id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            address NVARCHAR(200),
            phone NVARCHAR(20),
            manager_name NVARCHAR(100),
            is_active BIT DEFAULT 1,
            created_at DATETIME2 DEFAULT GETDATE()
        )
        """

        # جدول العملاء المحدث
        customers_table = """
        CREATE TABLE customers (
            customer_id INT IDENTITY(1,1) PRIMARY KEY,
            arabic_name NVARCHAR(100) NOT NULL,
            english_name NVARCHAR(100),
            phone NVARCHAR(20),
            email NVARCHAR(100),
            birth_date DATE,
            address NVARCHAR(200),
            reference_source NVARCHAR(50),
            branch_id INT DEFAULT 1,
            notes NVARCHAR(500),
            is_deleted BIT DEFAULT 0,
            created_at DATETIME2 DEFAULT GETDATE(),
            updated_at DATETIME2 DEFAULT GETDATE(),
            deleted_at DATETIME2,
            deleted_by NVARCHAR(50),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
        )
        """

        # جدول الموظفين
        employees_table = """
        CREATE TABLE employees (
            employee_id INT IDENTITY(1,1) PRIMARY KEY,
            arabic_name NVARCHAR(100) NOT NULL,
            english_name NVARCHAR(100),
            phone NVARCHAR(20),
            email NVARCHAR(100),
            position NVARCHAR(50),
            salary DECIMAL(10,2),
            commission_rate DECIMAL(5,2) DEFAULT 10.0,
            branch_id INT DEFAULT 1,
            hire_date DATE DEFAULT CAST(GETDATE() AS DATE),
            is_active BIT DEFAULT 1,
            created_at DATETIME2 DEFAULT GETDATE(),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
        )
        """

        # جدول الفواتير المحدث
        invoices_table = """
        CREATE TABLE invoices (
            invoice_id INT IDENTITY(1,1) PRIMARY KEY,
            customer_id INT NOT NULL,
            branch_id INT DEFAULT 1,
            employee_id INT DEFAULT 1,
            invoice_date DATE DEFAULT CAST(GETDATE() AS DATE),
            subtotal DECIMAL(10,2) NOT NULL,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            discount_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_method NVARCHAR(50) DEFAULT 'نقدي',
            payment_status NVARCHAR(20) DEFAULT 'مدفوعة',
            notes NVARCHAR(500),
            is_deleted BIT DEFAULT 0,
            created_at DATETIME2 DEFAULT GETDATE(),
            updated_at DATETIME2 DEFAULT GETDATE(),
            deleted_at DATETIME2,
            deleted_by NVARCHAR(50),
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id),
            FOREIGN KEY (employee_id) REFERENCES employees(employee_id)
        )
        """

        # جدول عناصر الفواتير
        invoice_items_table = """
        CREATE TABLE invoice_items (
            item_id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_id INT NOT NULL,
            item_type NVARCHAR(20) NOT NULL,
            item_code NVARCHAR(20) NOT NULL,
            item_name NVARCHAR(100) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            employee_name NVARCHAR(100),
            commission_amount DECIMAL(10,2) DEFAULT 0,
            created_at DATETIME2 DEFAULT GETDATE(),
            FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [
            ("branches", branches_table),
            ("customers", customers_table),
            ("employees", employees_table),
            ("invoices", invoices_table),
            ("invoice_items", invoice_items_table)
        ]

        for table_name, table_sql in tables:
            try:
                cursor.execute(table_sql)
                conn.commit()
                print(f"✅ تم إنشاء جدول {table_name}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء جدول {table_name}: {str(e)}")
                return False

        # إدراج بيانات افتراضية
        print("🔄 جاري إدراج البيانات الافتراضية...")
        
        # إدراج فروع افتراضية
        cursor.execute("""
            INSERT INTO branches (name, address, phone, manager_name) VALUES
            ('الفرع الرئيسي', 'القاهرة، مصر', '01090829393', 'أحمد محمد'),
            ('فرع المعادي', 'المعادي، القاهرة', '01090829394', 'سارة أحمد'),
            ('فرع مدينة نصر', 'مدينة نصر، القاهرة', '01090829395', 'محمد علي')
        """)
        print("✅ تم إدراج الفروع الافتراضية")
        
        # إدراج موظفين افتراضيين
        cursor.execute("""
            INSERT INTO employees (arabic_name, english_name, phone, position, branch_id) VALUES
            ('أحمد محمد', 'Ahmed Mohamed', '01090829393', 'مدير', 1),
            ('سارة أحمد', 'Sara Ahmed', '01090829394', 'مصففة شعر', 1),
            ('محمد علي', 'Mohamed Ali', '01090829395', 'حلاق', 1)
        """)
        print("✅ تم إدراج الموظفين الافتراضيين")
        
        # إدراج عملاء افتراضيين
        cursor.execute("""
            INSERT INTO customers (arabic_name, english_name, phone, reference_source, branch_id) VALUES
            ('عميل تجريبي', 'Test Customer', '01000000000', 'إعلان فيسبوك', 1),
            ('زياد أحمد', 'Ziad Ahmed', '01111111111', 'صديق', 1),
            ('نور محمد', 'Nour Mohamed', '01222222222', 'إنستجرام', 1)
        """)
        print("✅ تم إدراج العملاء الافتراضيين")

        conn.commit()
        conn.close()
        
        print("🎉 تم إنشاء قاعدة البيانات بنجاح!")
        print("📊 ملخص ما تم إنشاؤه:")
        print("   • 5 جداول رئيسية")
        print("   • 3 فروع افتراضية")
        print("   • 3 موظفين افتراضيين")
        print("   • 3 عملاء افتراضيين")
        print("   • إصلاح مشاكل التواريخ")
        print("   • أسماء عربية وإنجليزية")
        print("   • حقل 'عرفتنا عن طريق'")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إنشاء قاعدة البيانات من الصفر...")
    print("=" * 50)
    
    success = recreate_database()
    
    print("=" * 50)
    if success:
        print("✅ تمت العملية بنجاح!")
        print("يمكنك الآن استخدام النظام بقاعدة البيانات الجديدة.")
    else:
        print("❌ فشلت العملية!")
        print("يرجى التحقق من إعدادات قاعدة البيانات.")
    
    input("\nاضغط Enter للخروج...")
