"""
مسار مصادقة مبسط
Simple Authentication Route
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import pyodbc
from passlib.context import CryptContext
from jose import jwt
from datetime import datetime, timedelta

app = FastAPI()

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# إعدادات JWT
SECRET_KEY = "salon-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

class LoginRequest(BaseModel):
    username: str
    password: str

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
    return pyodbc.connect(connection_string)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """التحقق من كلمة المرور"""
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict):
    """إنشاء رمز الوصول"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

@app.get("/")
async def root():
    return {"message": "Simple Auth API"}

class BranchCreate(BaseModel):
    name: str
    address: str = ""
    phone: str = ""
    working_hours: str = ""

class BranchUpdate(BaseModel):
    name: str = None
    address: str = None
    phone: str = None
    working_hours: str = None
    is_active: bool = None

@app.post("/login")
async def login(login_data: LoginRequest):
    """تسجيل الدخول المبسط"""
    try:
        # الاتصال بقاعدة البيانات
        conn = get_db_connection()
        cursor = conn.cursor()

        # البحث عن المستخدم
        cursor.execute("SELECT user_id, username, password_hash, full_name, role_id FROM users WHERE username = ?", login_data.username)
        user_row = cursor.fetchone()

        if not user_row:
            raise HTTPException(status_code=401, detail="اسم المستخدم غير موجود")

        user_id, username, password_hash, full_name, role_id = user_row

        # التحقق من كلمة المرور
        if not verify_password(login_data.password, password_hash):
            raise HTTPException(status_code=401, detail="كلمة المرور غير صحيحة")

        # إنشاء رمز الوصول
        access_token = create_access_token(data={"sub": username})

        cursor.close()
        conn.close()

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "user_id": user_id,
                "username": username,
                "full_name": full_name,
                "role_id": role_id
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ داخلي: {str(e)}")

# مسارات إدارة الفروع
@app.get("/branches")
async def get_branches():
    """الحصول على قائمة الفروع"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT branch_id, name, address, phone, working_hours, is_active, created_at
            FROM branches
            ORDER BY created_at DESC
        """)

        branches = []
        for row in cursor.fetchall():
            branches.append({
                "branch_id": row[0],
                "name": row[1],
                "address": row[2] or "",
                "phone": row[3] or "",
                "working_hours": row[4] or "",
                "is_active": bool(row[5]),
                "created_at": row[6].isoformat() if row[6] else None
            })

        cursor.close()
        conn.close()

        return branches

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في جلب الفروع: {str(e)}")

@app.post("/branches")
async def create_branch(branch: BranchCreate):
    """إنشاء فرع جديد"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO branches (name, address, phone, working_hours, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, 1, GETDATE(), GETDATE())
        """, branch.name, branch.address, branch.phone, branch.working_hours)

        conn.commit()

        # الحصول على ID الفرع الجديد
        cursor.execute("SELECT @@IDENTITY")
        branch_id = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        return {
            "branch_id": branch_id,
            "name": branch.name,
            "address": branch.address,
            "phone": branch.phone,
            "working_hours": branch.working_hours,
            "is_active": True,
            "message": "تم إنشاء الفرع بنجاح"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء الفرع: {str(e)}")

@app.put("/branches/{branch_id}")
async def update_branch(branch_id: int, branch: BranchUpdate):
    """تحديث فرع"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الفرع
        cursor.execute("SELECT branch_id FROM branches WHERE branch_id = ?", branch_id)
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="الفرع غير موجود")

        # بناء استعلام التحديث
        update_fields = []
        params = []

        if branch.name is not None:
            update_fields.append("name = ?")
            params.append(branch.name)
        if branch.address is not None:
            update_fields.append("address = ?")
            params.append(branch.address)
        if branch.phone is not None:
            update_fields.append("phone = ?")
            params.append(branch.phone)
        if branch.working_hours is not None:
            update_fields.append("working_hours = ?")
            params.append(branch.working_hours)
        if branch.is_active is not None:
            update_fields.append("is_active = ?")
            params.append(branch.is_active)

        if update_fields:
            update_fields.append("updated_at = GETDATE()")
            params.append(branch_id)

            query = f"UPDATE branches SET {', '.join(update_fields)} WHERE branch_id = ?"
            cursor.execute(query, params)
            conn.commit()

        cursor.close()
        conn.close()

        return {"message": "تم تحديث الفرع بنجاح"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحديث الفرع: {str(e)}")

@app.delete("/branches/{branch_id}")
async def delete_branch(branch_id: int):
    """حذف فرع"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الفرع
        cursor.execute("SELECT branch_id FROM branches WHERE branch_id = ?", branch_id)
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="الفرع غير موجود")

        # حذف الفرع
        cursor.execute("DELETE FROM branches WHERE branch_id = ?", branch_id)
        conn.commit()

        cursor.close()
        conn.close()

        return {"message": "تم حذف الفرع بنجاح"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في حذف الفرع: {str(e)}")

# نماذج العملاء
class CustomerCreate(BaseModel):
    first_name: str
    last_name: str = ""
    phone: str
    email: str = ""
    birth_date: str = ""
    zone_id: int = 1
    notes: str = ""

class CustomerUpdate(BaseModel):
    first_name: str = None
    last_name: str = None
    phone: str = None
    email: str = None
    birth_date: str = None
    zone_id: int = None
    notes: str = None

# مسارات إدارة العملاء
@app.get("/customers")
async def get_customers():
    """الحصول على قائمة العملاء"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT customer_id, first_name, last_name, phone, email, birth_date, zone_id, notes, created_at
            FROM customers
            ORDER BY created_at DESC
        """)

        customers = []
        for row in cursor.fetchall():
            customers.append({
                "customer_id": row[0],
                "first_name": row[1] or "",
                "last_name": row[2] or "",
                "full_name": f"{row[1] or ''} {row[2] or ''}".strip(),
                "phone": row[3] or "",
                "email": row[4] or "",
                "birth_date": row[5].isoformat() if row[5] else "",
                "zone_id": row[6] or 1,
                "notes": row[7] or "",
                "created_at": row[8].isoformat() if row[8] else None
            })

        cursor.close()
        conn.close()

        return customers

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في جلب العملاء: {str(e)}")

@app.post("/customers")
async def create_customer(customer: CustomerCreate):
    """إنشاء عميل جديد"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # تحويل تاريخ الميلاد إذا كان موجود
        birth_date_param = None
        if customer.birth_date:
            try:
                birth_date_param = customer.birth_date
            except:
                birth_date_param = None

        cursor.execute("""
            INSERT INTO customers (first_name, last_name, phone, email, birth_date, zone_id, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, GETDATE(), GETDATE())
        """, customer.first_name, customer.last_name, customer.phone, customer.email, birth_date_param, customer.zone_id, customer.notes)

        conn.commit()

        # الحصول على ID العميل الجديد
        cursor.execute("SELECT @@IDENTITY")
        customer_id = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        return {
            "customer_id": customer_id,
            "first_name": customer.first_name,
            "last_name": customer.last_name,
            "phone": customer.phone,
            "email": customer.email,
            "message": "تم إنشاء العميل بنجاح"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في إنشاء العميل: {str(e)}")

@app.put("/customers/{customer_id}")
async def update_customer(customer_id: int, customer: CustomerUpdate):
    """تحديث عميل"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود العميل
        cursor.execute("SELECT customer_id FROM customers WHERE customer_id = ?", customer_id)
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="العميل غير موجود")

        # بناء استعلام التحديث
        update_fields = []
        params = []

        if customer.first_name is not None:
            update_fields.append("first_name = ?")
            params.append(customer.first_name)
        if customer.last_name is not None:
            update_fields.append("last_name = ?")
            params.append(customer.last_name)
        if customer.phone is not None:
            update_fields.append("phone = ?")
            params.append(customer.phone)
        if customer.email is not None:
            update_fields.append("email = ?")
            params.append(customer.email)
        if customer.birth_date is not None:
            update_fields.append("birth_date = ?")
            params.append(customer.birth_date if customer.birth_date else None)
        if customer.zone_id is not None:
            update_fields.append("zone_id = ?")
            params.append(customer.zone_id)
        if customer.notes is not None:
            update_fields.append("notes = ?")
            params.append(customer.notes)

        if update_fields:
            update_fields.append("updated_at = GETDATE()")
            params.append(customer_id)

            query = f"UPDATE customers SET {', '.join(update_fields)} WHERE customer_id = ?"
            cursor.execute(query, params)
            conn.commit()

        cursor.close()
        conn.close()

        return {"message": "تم تحديث العميل بنجاح"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في تحديث العميل: {str(e)}")

@app.delete("/customers/{customer_id}")
async def delete_customer(customer_id: int):
    """حذف عميل"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود العميل
        cursor.execute("SELECT customer_id FROM customers WHERE customer_id = ?", customer_id)
        if not cursor.fetchone():
            raise HTTPException(status_code=404, detail="العميل غير موجود")

        # حذف العميل
        cursor.execute("DELETE FROM customers WHERE customer_id = ?", customer_id)
        conn.commit()

        cursor.close()
        conn.close()

        return {"message": "تم حذف العميل بنجاح"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"خطأ في حذف العميل: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
