"""
نظام الحذف المنطقي (Soft Delete)
Soft Delete System for SalonProManager
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
    return pyodbc.connect(connection_string)

def add_soft_delete_columns():
    """إضافة أعمدة الحذف المنطقي لجميع الجداول"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    tables = [
        'branches', 'customers', 'employees', 'services', 
        'products', 'appointments', 'invoices', 'invoice_items'
    ]
    
    for table in tables:
        try:
            # إضافة عمود is_deleted
            cursor.execute(f"""
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = '{table}' AND COLUMN_NAME = 'is_deleted')
                BEGIN
                    ALTER TABLE {table} ADD is_deleted BIT DEFAULT 0
                END
            """)
            
            # إضافة عمود deleted_at
            cursor.execute(f"""
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = '{table}' AND COLUMN_NAME = 'deleted_at')
                BEGIN
                    ALTER TABLE {table} ADD deleted_at DATETIME NULL
                END
            """)
            
            # إضافة عمود deleted_by
            cursor.execute(f"""
                IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                              WHERE TABLE_NAME = '{table}' AND COLUMN_NAME = 'deleted_by')
                BEGIN
                    ALTER TABLE {table} ADD deleted_by NVARCHAR(50) NULL
                END
            """)
            
            print(f"✅ تم تحديث جدول {table}")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث جدول {table}: {e}")
    
    conn.commit()
    cursor.close()
    conn.close()
    print("🎉 تم إضافة نظام الحذف المنطقي لجميع الجداول")

def soft_delete_record(table_name, record_id, id_column, deleted_by_user):
    """حذف منطقي لسجل معين"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute(f"""
            UPDATE {table_name} 
            SET is_deleted = 1, 
                deleted_at = GETDATE(), 
                deleted_by = ?
            WHERE {id_column} = ? AND (is_deleted = 0 OR is_deleted IS NULL)
        """, deleted_by_user, record_id)
        
        rows_affected = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        return rows_affected > 0
        
    except Exception as e:
        print(f"❌ خطأ في الحذف المنطقي: {e}")
        cursor.close()
        conn.close()
        return False

def restore_record(table_name, record_id, id_column):
    """استعادة سجل محذوف منطقياً"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute(f"""
            UPDATE {table_name} 
            SET is_deleted = 0, 
                deleted_at = NULL, 
                deleted_by = NULL
            WHERE {id_column} = ? AND is_deleted = 1
        """, record_id)
        
        rows_affected = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        return rows_affected > 0
        
    except Exception as e:
        print(f"❌ خطأ في استعادة السجل: {e}")
        cursor.close()
        conn.close()
        return False

def get_active_records(table_name, branch_id=None):
    """الحصول على السجلات النشطة فقط"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        if branch_id and table_name in ['customers', 'employees', 'appointments', 'invoices']:
            cursor.execute(f"""
                SELECT * FROM {table_name} 
                WHERE (is_deleted = 0 OR is_deleted IS NULL) 
                AND branch_id = ?
                ORDER BY created_at DESC
            """, branch_id)
        else:
            cursor.execute(f"""
                SELECT * FROM {table_name} 
                WHERE (is_deleted = 0 OR is_deleted IS NULL)
                ORDER BY created_at DESC
            """)
        
        columns = [column[0] for column in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            results.append(record)
        
        cursor.close()
        conn.close()
        return results
        
    except Exception as e:
        print(f"❌ خطأ في جلب السجلات: {e}")
        cursor.close()
        conn.close()
        return []

def get_deleted_records(table_name, branch_id=None):
    """الحصول على السجلات المحذوفة منطقياً"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        if branch_id and table_name in ['customers', 'employees', 'appointments', 'invoices']:
            cursor.execute(f"""
                SELECT * FROM {table_name} 
                WHERE is_deleted = 1 
                AND branch_id = ?
                ORDER BY deleted_at DESC
            """, branch_id)
        else:
            cursor.execute(f"""
                SELECT * FROM {table_name} 
                WHERE is_deleted = 1
                ORDER BY deleted_at DESC
            """)
        
        columns = [column[0] for column in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            record = dict(zip(columns, row))
            results.append(record)
        
        cursor.close()
        conn.close()
        return results
        
    except Exception as e:
        print(f"❌ خطأ في جلب السجلات المحذوفة: {e}")
        cursor.close()
        conn.close()
        return []

def permanent_delete_record(table_name, record_id, id_column):
    """حذف نهائي للسجل من قاعدة البيانات"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute(f"""
            DELETE FROM {table_name} 
            WHERE {id_column} = ? AND is_deleted = 1
        """, record_id)
        
        rows_affected = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        return rows_affected > 0
        
    except Exception as e:
        print(f"❌ خطأ في الحذف النهائي: {e}")
        cursor.close()
        conn.close()
        return False

def cleanup_old_deleted_records(table_name, days_old=30):
    """تنظيف السجلات المحذوفة القديمة"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute(f"""
            DELETE FROM {table_name} 
            WHERE is_deleted = 1 
            AND deleted_at < DATEADD(day, -{days_old}, GETDATE())
        """)
        
        rows_affected = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"🧹 تم حذف {rows_affected} سجل قديم من جدول {table_name}")
        return rows_affected
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف السجلات القديمة: {e}")
        cursor.close()
        conn.close()
        return 0

def get_branch_statistics(branch_id):
    """إحصائيات الفرع"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    stats = {}
    
    try:
        # عدد العملاء النشطين
        cursor.execute("""
            SELECT COUNT(*) FROM customers 
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
        """)
        stats['active_customers'] = cursor.fetchone()[0]
        
        # عدد الموظفين النشطين
        cursor.execute("""
            SELECT COUNT(*) FROM employees 
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
        """)
        stats['active_employees'] = cursor.fetchone()[0]
        
        # عدد المواعيد اليوم
        cursor.execute("""
            SELECT COUNT(*) FROM appointments 
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
            AND appointment_date = CAST(GETDATE() AS DATE)
        """)
        stats['today_appointments'] = cursor.fetchone()[0]
        
        # إجمالي المبيعات اليوم
        cursor.execute("""
            SELECT ISNULL(SUM(total_amount), 0) FROM invoices 
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
            AND invoice_date = CAST(GETDATE() AS DATE)
        """)
        stats['today_sales'] = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        return stats
        
    except Exception as e:
        print(f"❌ خطأ في جلب إحصائيات الفرع: {e}")
        cursor.close()
        conn.close()
        return {
            'active_customers': 0,
            'active_employees': 0,
            'today_appointments': 0,
            'today_sales': 0
        }

if __name__ == "__main__":
    print("🔧 إعداد نظام الحذف المنطقي...")
    add_soft_delete_columns()
    print("✅ تم إعداد النظام بنجاح!")
