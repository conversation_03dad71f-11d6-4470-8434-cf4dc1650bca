"""
واجهة المستخدم الرئيسية
SalonProManager Streamlit Interface - Professional Salon Management System
"""

import streamlit as st
import requests
import pandas as pd
from datetime import datetime, date
import json
import base64
from io import BytesIO
import pyodbc
import hashlib

# استيراد plotly مع معالجة الخطأ
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    st.warning("مكتبة plotly غير مثبتة. سيتم عرض الرسوم البيانية الأساسية.")

# إعدادات اللغة والترجمة
LANGUAGES = {
    'ar': {
        'app_title': 'SalonProManager - نظام إدارة الصالونات',
        'login': 'تسجيل الدخول',
        'username': 'اسم المستخدم',
        'password': 'كلمة المرور',
        'dashboard': 'لوحة التحكم',
        'sales': 'المبيعات',
        'customers': 'العملاء',
        'appointments': 'المواعيد',
        'reports': 'التقارير',
        'settings': 'الإعدادات',
        'logout': 'تسجيل الخروج',
        'total_sales': 'إجمالي المبيعات',
        'total_invoices': 'إجمالي الفواتير',
        'today_sales': 'مبيعات اليوم',
        'cash_balance': 'الرصيد النقدي',
        'new_invoice': 'فاتورة جديدة',
        'view_invoices': 'عرض الفواتير',
        'statistics': 'الإحصائيات',
        'currency': 'ج.م',
        'branch': 'الفرع',
        'user': 'المستخدم',
        'date': 'التاريخ',
        'time': 'الوقت',
        'employee': 'موظف',
        'manager': 'مدير',
        'admin': 'مدير عام',
        'today_appointments': 'مواعيد اليوم',
        'daily_revenue': 'إيرادات اليوم',
        'support': 'للدعم',
        'whatsapp': 'واتساب',
        'click_here': 'اضغط هنا'
    },
    'en': {
        'app_title': 'SalonProManager - Professional Salon Management',
        'login': 'Login',
        'username': 'Username',
        'password': 'Password',
        'dashboard': 'Dashboard',
        'sales': 'Sales',
        'customers': 'Customers',
        'appointments': 'Appointments',
        'reports': 'Reports',
        'settings': 'Settings',
        'logout': 'Logout',
        'total_sales': 'Total Sales',
        'total_invoices': 'Total Invoices',
        'today_sales': 'Today Sales',
        'cash_balance': 'Cash Balance',
        'new_invoice': 'New Invoice',
        'view_invoices': 'View Invoices',
        'statistics': 'Statistics',
        'currency': 'EGP',
        'branch': 'Branch',
        'user': 'User',
        'date': 'Date',
        'time': 'Time'
    }
}

def get_text(key, lang='ar'):
    """الحصول على النص المترجم"""
    return LANGUAGES.get(lang, LANGUAGES['ar']).get(key, key)

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': 'alisamaraa.ddns.net,4100',
    'database': 'SalonProManager_DB',
    'username': 'sa',
    'password': '@a123admin4',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

def get_next_sequence_code(sequence_name, branch_id=None):
    """الحصول على الكود التسلسلي التالي"""
    try:
        conn = get_db_connection()
        if conn is None:
            return None

        cursor = conn.cursor()
        cursor.execute("""
            DECLARE @next_code NVARCHAR(50);
            EXEC GetNextSequence ?, ?, @next_code OUTPUT;
            SELECT @next_code;
        """, (sequence_name, branch_id))

        result = cursor.fetchone()
        conn.close()

        if result and result[0] != 'ERROR':
            return result[0]
        return None
    except Exception as e:
        return None

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات مع معالجة محسنة للأخطاء"""
    try:
        # محاولة الاتصال بعدة طرق مختلفة
        connection_strings = [
            # الطريقة الأولى - مع TrustServerCertificate
            f"DRIVER={DB_CONFIG['driver']};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']};TrustServerCertificate=yes;Encrypt=no;",

            # الطريقة الثانية - مع Encrypt=no فقط
            f"DRIVER={DB_CONFIG['driver']};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']};Encrypt=no;",

            # الطريقة الثالثة - بدون تشفير
            f"DRIVER={DB_CONFIG['driver']};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']};",

            # الطريقة الرابعة - مع ODBC Driver 18
            f"DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['username']};PWD={DB_CONFIG['password']};TrustServerCertificate=yes;Encrypt=no;",
        ]

        for i, conn_str in enumerate(connection_strings):
            try:
                conn = pyodbc.connect(conn_str, timeout=5)  # تقليل وقت الانتظار
                # اختبار الاتصال
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()

                return conn
            except Exception as e:
                if i == len(connection_strings) - 1:  # آخر محاولة
                    raise e
                continue

    except Exception as e:
        # لا نعرض رسائل الخطأ في الواجهة الرئيسية
        # فقط نسجل الخطأ ونعيد None
        return None

def execute_query(query, params=None, fetch=True):
    """تنفيذ استعلام SQL مع معالجة محسنة للأخطاء"""
    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            return None

        cursor = conn.cursor()

        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        if fetch:
            if query.strip().upper().startswith('SELECT'):
                columns = [column[0] for column in cursor.description]
                results = cursor.fetchall()
                data = []
                for row in results:
                    data.append(dict(zip(columns, row)))
                return data
            else:
                return cursor.fetchall()
        else:
            conn.commit()
            return cursor.rowcount

    except:
        # لا نعرض رسائل خطأ، فقط نعيد None
        return None
    finally:
        if conn:
            try:
                conn.close()
            except:
                pass

def init_database():
    """تهيئة قاعدة البيانات والتحقق من الجداول الموجودة"""

    # محاولة الاتصال بقاعدة البيانات بصمت
    conn = get_db_connection()
    if conn is None:
        # تشغيل النظام بالبيانات المحلية بدون عرض رسائل خطأ
        init_local_data()
        return False

    # التحقق من وجود الجداول الأساسية في قاعدة البيانات الموجودة
    check_tables_query = """
    SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME IN (
        'Branches', 'Customers', 'Employees', 'Services', 'Products',
        'Sales', 'Appointments', 'Users_T', 'dbo.Branches', 'dbo.Customers',
        'dbo.Employees', 'dbo.Services', 'dbo.Products', 'dbo.Sales',
        'dbo.Appointments', 'dbo.Users_T'
    )
    """

    try:
        existing_tables = execute_query(check_tables_query)

        if existing_tables and len(existing_tables) > 0:
            # إضافة الأعمدة المفقودة للجداول الموجودة إذا لزم الأمر
            add_missing_columns_to_existing_tables()
            return True
        else:
            create_new_tables()
            return True

    except Exception as e:
        # تشغيل النظام بالبيانات المحلية بدون عرض رسائل خطأ
        init_local_data()
        return False

def init_local_data():
    """تهيئة البيانات - الآن تعتمد على قاعدة البيانات فقط"""
    # تعيين حالة قاعدة البيانات
    st.session_state.db_initialized = False
    st.session_state.using_local_data = False

    # لا نحتاج لتهيئة بيانات محلية - كل شيء من قاعدة البيانات

def recreate_database_from_scratch():
    """إنشاء قاعدة البيانات من الصفر مع إصلاح مشاكل التواريخ"""
    try:
        conn = get_db_connection()
        if conn is None:
            st.error("❌ فشل الاتصال بقاعدة البيانات")
            return False

        cursor = conn.cursor()

        st.info("🔄 جاري حذف الجداول القديمة...")

        # حذف الجداول الموجودة بالترتيب الصحيح
        drop_tables = [
            "DROP TABLE IF EXISTS invoice_items",
            "DROP TABLE IF EXISTS invoices",
            "DROP TABLE IF EXISTS appointments",
            "DROP TABLE IF EXISTS cash_transactions",
            "DROP TABLE IF EXISTS categories",
            "DROP TABLE IF EXISTS items",
            "DROP TABLE IF EXISTS services",
            "DROP TABLE IF EXISTS products",
            "DROP TABLE IF EXISTS customers",
            "DROP TABLE IF EXISTS employees",
            "DROP TABLE IF EXISTS users",
            "DROP TABLE IF EXISTS branches"
        ]

        for drop_sql in drop_tables:
            try:
                cursor.execute(drop_sql)
                conn.commit()
            except Exception as e:
                pass  # تجاهل الأخطاء إذا كان الجدول غير موجود

        st.info("✅ تم حذف الجداول القديمة")
        st.info("🔄 جاري إنشاء الجداول الجديدة...")

        # جدول الفروع
        branches_table = """
        CREATE TABLE branches (
            branch_id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(100) NOT NULL,
            address NVARCHAR(200),
            phone NVARCHAR(20),
            manager_name NVARCHAR(100),
            is_active BIT DEFAULT 1,
            created_at DATETIME2 DEFAULT GETDATE()
        )
        """

        # جدول العملاء المحدث
        customers_table = """
        CREATE TABLE customers (
            customer_id INT IDENTITY(1,1) PRIMARY KEY,
            arabic_name NVARCHAR(100) NOT NULL,
            english_name NVARCHAR(100),
            phone NVARCHAR(20),
            email NVARCHAR(100),
            birth_date DATE,
            address NVARCHAR(200),
            reference_source NVARCHAR(50),
            branch_id INT DEFAULT 1,
            notes NVARCHAR(500),
            is_deleted BIT DEFAULT 0,
            created_at DATETIME2 DEFAULT GETDATE(),
            updated_at DATETIME2 DEFAULT GETDATE(),
            deleted_at DATETIME2,
            deleted_by NVARCHAR(50),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
        )
        """

        # جدول الموظفين
        employees_table = """
        CREATE TABLE employees (
            employee_id INT IDENTITY(1,1) PRIMARY KEY,
            arabic_name NVARCHAR(100) NOT NULL,
            english_name NVARCHAR(100),
            phone NVARCHAR(20),
            email NVARCHAR(100),
            position NVARCHAR(50),
            salary DECIMAL(10,2),
            commission_rate DECIMAL(5,2) DEFAULT 10.0,
            branch_id INT DEFAULT 1,
            hire_date DATE DEFAULT CAST(GETDATE() AS DATE),
            is_active BIT DEFAULT 1,
            created_at DATETIME2 DEFAULT GETDATE(),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
        )
        """

        # جدول الفواتير المحدث
        invoices_table = """
        CREATE TABLE invoices (
            invoice_id INT IDENTITY(1,1) PRIMARY KEY,
            customer_id INT NOT NULL,
            branch_id INT DEFAULT 1,
            employee_id INT DEFAULT 1,
            invoice_date DATE DEFAULT CAST(GETDATE() AS DATE),
            subtotal DECIMAL(10,2) NOT NULL,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            discount_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_method NVARCHAR(50) DEFAULT 'نقدي',
            payment_status NVARCHAR(20) DEFAULT 'مدفوعة',
            notes NVARCHAR(500),
            is_deleted BIT DEFAULT 0,
            created_at DATETIME2 DEFAULT GETDATE(),
            updated_at DATETIME2 DEFAULT GETDATE(),
            deleted_at DATETIME2,
            deleted_by NVARCHAR(50),
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
            FOREIGN KEY (branch_id) REFERENCES branches(branch_id),
            FOREIGN KEY (employee_id) REFERENCES employees(employee_id)
        )
        """

        # جدول عناصر الفواتير
        invoice_items_table = """
        CREATE TABLE invoice_items (
            item_id INT IDENTITY(1,1) PRIMARY KEY,
            invoice_id INT NOT NULL,
            item_type NVARCHAR(20) NOT NULL,
            item_code NVARCHAR(20) NOT NULL,
            item_name NVARCHAR(100) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            employee_name NVARCHAR(100),
            commission_amount DECIMAL(10,2) DEFAULT 0,
            created_at DATETIME2 DEFAULT GETDATE(),
            FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [
            ("branches", branches_table),
            ("customers", customers_table),
            ("employees", employees_table),
            ("invoices", invoices_table),
            ("invoice_items", invoice_items_table)
        ]

        for table_name, table_sql in tables:
            try:
                cursor.execute(table_sql)
                conn.commit()
                st.success(f"✅ تم إنشاء جدول {table_name}")
            except Exception as e:
                st.error(f"❌ خطأ في إنشاء جدول {table_name}: {str(e)}")
                return False

        # إدراج بيانات افتراضية
        st.info("🔄 جاري إدراج البيانات الافتراضية...")

        # إدراج فروع افتراضية
        cursor.execute("""
            INSERT INTO branches (name, address, phone, manager_name) VALUES
            ('الفرع الرئيسي', 'القاهرة، مصر', '01090829393', 'أحمد محمد'),
            ('فرع المعادي', 'المعادي، القاهرة', '01090829394', 'سارة أحمد'),
            ('فرع مدينة نصر', 'مدينة نصر، القاهرة', '01090829395', 'محمد علي')
        """)

        # إدراج موظفين افتراضيين
        cursor.execute("""
            INSERT INTO employees (arabic_name, english_name, phone, position, branch_id) VALUES
            ('أحمد محمد', 'Ahmed Mohamed', '01090829393', 'مدير', 1),
            ('سارة أحمد', 'Sara Ahmed', '01090829394', 'مصففة شعر', 1),
            ('محمد علي', 'Mohamed Ali', '01090829395', 'حلاق', 1)
        """)

        # إدراج عملاء افتراضيين
        cursor.execute("""
            INSERT INTO customers (arabic_name, english_name, phone, reference_source, branch_id) VALUES
            ('عميل تجريبي', 'Test Customer', '01000000000', 'إعلان فيسبوك', 1),
            ('زياد أحمد', 'Ziad Ahmed', '01111111111', 'صديق', 1),
            ('نور محمد', 'Nour Mohamed', '01222222222', 'إنستجرام', 1)
        """)

        conn.commit()
        conn.close()

        st.success("🎉 تم إنشاء قاعدة البيانات بنجاح!")
        st.balloons()
        return True

    except Exception as e:
        st.error(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
        return False

def add_missing_columns_to_existing_tables():
    """إضافة الأعمدة المفقودة للجداول الموجودة"""

    # قائمة الأعمدة المطلوبة لكل جدول
    required_columns = {
        'Customers': [
            ('branch_id', 'INT DEFAULT 1'),
            ('is_deleted', 'BIT DEFAULT 0'),
            ('created_at', 'DATETIME DEFAULT GETDATE()'),
            ('updated_at', 'DATETIME DEFAULT GETDATE()')
        ],
        'Sales': [
            ('branch_id', 'INT DEFAULT 1'),
            ('is_deleted', 'BIT DEFAULT 0')
        ],
        'Appointments': [
            ('branch_id', 'INT DEFAULT 1'),
            ('is_deleted', 'BIT DEFAULT 0')
        ],
        'Employees': [
            ('branch_id', 'INT DEFAULT 1'),
            ('is_deleted', 'BIT DEFAULT 0')
        ],
        'Services': [
            ('branch_id', 'INT DEFAULT 1'),
            ('is_deleted', 'BIT DEFAULT 0')
        ],
        'Products': [
            ('branch_id', 'INT DEFAULT 1'),
            ('is_deleted', 'BIT DEFAULT 0')
        ]
    }

    for table_name, columns in required_columns.items():
        for column_name, column_definition in columns:
            try:
                # التحقق من وجود العمود
                check_column_query = f"""
                SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = '{table_name}' AND COLUMN_NAME = '{column_name}'
                """

                result = execute_query(check_column_query)
                if result and result[0]['count'] == 0:
                    # إضافة العمود إذا لم يكن موجود
                    alter_query = f"ALTER TABLE {table_name} ADD {column_name} {column_definition}"
                    execute_query(alter_query, fetch=False)
                    st.success(f"✅ تم إضافة العمود {column_name} إلى جدول {table_name}")

            except Exception as e:
                st.warning(f"تحذير: لا يمكن إضافة العمود {column_name} إلى {table_name}: {str(e)}")

def create_new_tables():
    """إنشاء جداول جديدة إذا لم توجد"""

    # جدول الفروع
    branches_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
    CREATE TABLE Branches (
        branch_id INT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(100) NOT NULL,
        address NVARCHAR(255),
        phone NVARCHAR(20),
        working_hours NVARCHAR(100),
        is_active BIT DEFAULT 1,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE()
    )
    """

    # جدول المستخدمين
    users_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
    CREATE TABLE users (
        user_id INT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password_hash NVARCHAR(255) NOT NULL,
        full_name NVARCHAR(100) NOT NULL,
        email NVARCHAR(100),
        phone NVARCHAR(20),
        role NVARCHAR(20) DEFAULT 'employee',
        branch_id INT,
        is_active BIT DEFAULT 1,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
    )
    """

    # جدول العملاء
    customers_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='customers' AND xtype='U')
    CREATE TABLE customers (
        customer_id INT IDENTITY(1,1) PRIMARY KEY,
        first_name NVARCHAR(50) NOT NULL,
        last_name NVARCHAR(50),
        phone NVARCHAR(20) NOT NULL,
        email NVARCHAR(100),
        birth_date DATE,
        zone_id INT DEFAULT 1,
        notes NVARCHAR(500),
        branch_id INT DEFAULT 1,
        is_deleted BIT DEFAULT 0,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        deleted_at DATETIME,
        deleted_by NVARCHAR(50)
    )
    """

    # إضافة عمود branch_id إذا لم يكن موجوداً
    add_branch_id_to_customers = """
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_NAME = 'customers' AND COLUMN_NAME = 'branch_id')
    BEGIN
        ALTER TABLE customers ADD branch_id INT DEFAULT 1
    END
    """

    # جدول الخدمات مع الأكواد
    services_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='services' AND xtype='U')
    CREATE TABLE services (
        service_id INT IDENTITY(1,1) PRIMARY KEY,
        service_code NVARCHAR(10) UNIQUE NOT NULL,
        name NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        price_standard DECIMAL(10,2) NOT NULL,
        price_premium DECIMAL(10,2) NOT NULL,
        price_economy DECIMAL(10,2) NOT NULL,
        duration_minutes INT DEFAULT 60,
        commission_rate DECIMAL(5,2) DEFAULT 15.0,
        default_employee_id INT,
        default_employee_name NVARCHAR(100),
        branch_id INT NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
    )
    """

    # جدول المنتجات مع الأكواد
    products_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='products' AND xtype='U')
    CREATE TABLE products (
        product_id INT IDENTITY(1,1) PRIMARY KEY,
        product_code NVARCHAR(10) UNIQUE NOT NULL,
        name NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        cost_price DECIMAL(10,2) NOT NULL,
        price_standard DECIMAL(10,2) NOT NULL,
        price_premium DECIMAL(10,2) NOT NULL,
        price_economy DECIMAL(10,2) NOT NULL,
        stock_quantity INT DEFAULT 0,
        min_stock_level INT DEFAULT 5,
        unit NVARCHAR(20) DEFAULT 'قطعة',
        branch_id INT NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME DEFAULT GETDATE(),
        updated_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
    )
    """

    # جدول الفواتير المحدث
    invoices_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='invoices' AND xtype='U')
    CREATE TABLE invoices (
        invoice_id INT IDENTITY(1,1) PRIMARY KEY,
        customer_name NVARCHAR(100) NOT NULL,
        customer_phone NVARCHAR(20),
        branch_id INT DEFAULT 1,
        invoice_date DATE DEFAULT GETDATE(),
        invoice_time TIME DEFAULT GETDATE(),
        subtotal DECIMAL(10,2) NOT NULL,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_method NVARCHAR(50) DEFAULT 'نقدي',
        status NVARCHAR(20) DEFAULT 'مدفوعة',
        notes NVARCHAR(500),
        created_by NVARCHAR(50) NOT NULL,
        is_deleted BIT DEFAULT 0,
        created_at DATETIME DEFAULT GETDATE()
    )
    """

    # جدول عناصر الفاتورة المحدث
    invoice_items_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='invoice_items' AND xtype='U')
    CREATE TABLE invoice_items (
        item_id INT IDENTITY(1,1) PRIMARY KEY,
        invoice_id INT NOT NULL,
        item_type NVARCHAR(20) NOT NULL,
        item_code NVARCHAR(20) NOT NULL,
        item_name NVARCHAR(100) NOT NULL,
        quantity INT NOT NULL DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        employee_name NVARCHAR(100),
        commission_amount DECIMAL(10,2) DEFAULT 0,
        created_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (invoice_id) REFERENCES invoices(invoice_id)
    )
    """

    # جدول المعاملات النقدية
    cash_transactions_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='cash_transactions' AND xtype='U')
    CREATE TABLE cash_transactions (
        transaction_id INT IDENTITY(1,1) PRIMARY KEY,
        branch_id INT NOT NULL,
        transaction_type NVARCHAR(20) NOT NULL, -- 'income', 'expense', 'invoice_payment'
        amount DECIMAL(10,2) NOT NULL,
        description NVARCHAR(500) NOT NULL,
        reference_id INT, -- invoice_id if related to invoice
        created_by NVARCHAR(50) NOT NULL,
        created_at DATETIME DEFAULT GETDATE(),
        FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
    )
    """

    # جدول الفئات
    categories_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='categories' AND xtype='U')
    CREATE TABLE categories (
        category_id INT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(100) NOT NULL,
        type NVARCHAR(20) NOT NULL, -- 'خدمات' or 'منتجات'
        description NVARCHAR(500),
        color NVARCHAR(10) DEFAULT '#667eea',
        icon NVARCHAR(10) DEFAULT '📦',
        is_active BIT DEFAULT 1,
        branch_id INT DEFAULT 1,
        created_at DATETIME DEFAULT GETDATE(),
        is_deleted BIT DEFAULT 0,
        FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
    )
    """

    # جدول الأصناف المحدث
    items_table = """
    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='items' AND xtype='U')
    CREATE TABLE items (
        item_id INT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(100) NOT NULL,
        code NVARCHAR(20) NOT NULL UNIQUE,
        category_id INT,
        type NVARCHAR(20) NOT NULL, -- 'منتج' or 'خدمة'
        description NVARCHAR(500),
        price_normal DECIMAL(10,2) NOT NULL,
        price_premium DECIMAL(10,2),
        price_vip DECIMAL(10,2),
        stock_quantity INT DEFAULT 0,
        min_stock INT DEFAULT 5,
        commission_rate DECIMAL(5,2) DEFAULT 10,
        is_active BIT DEFAULT 1,
        branch_id INT DEFAULT 1,
        created_at DATETIME DEFAULT GETDATE(),
        is_deleted BIT DEFAULT 0,
        FOREIGN KEY (category_id) REFERENCES categories(category_id),
        FOREIGN KEY (branch_id) REFERENCES branches(branch_id)
    )
    """

    # تنفيذ إنشاء الجداول
    tables = [
        branches_table,
        users_table,
        customers_table,
        services_table,
        products_table,
        invoices_table,
        invoice_items_table,
        cash_transactions_table,
        categories_table,
        items_table
    ]

    for table_sql in tables:
        execute_query(table_sql, fetch=False)

    # إضافة الأعمدة المفقودة للجداول الموجودة
    alter_queries = [
        add_branch_id_to_customers,
        """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                       WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'branch_id')
        BEGIN
            ALTER TABLE invoices ADD branch_id INT DEFAULT 1
        END
        """,
        """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                       WHERE TABLE_NAME = 'cash_transactions' AND COLUMN_NAME = 'branch_id')
        BEGIN
            ALTER TABLE cash_transactions ADD branch_id INT DEFAULT 1
        END
        """
    ]

    for alter_sql in alter_queries:
        try:
            execute_query(alter_sql, fetch=False)
        except Exception as e:
            print(f"تحذير: {str(e)}")

    # إدراج بيانات افتراضية
    insert_default_data()

def insert_default_data():
    """إدراج البيانات الافتراضية"""

    # إدراج الفروع الافتراضية
    branches_data = [
        ("الفرع الرئيسي", "شارع التحرير - وسط البلد", "01090829393", "يومياً من 10 ص إلى 12 م"),
        ("فرع المعادي", "شارع 9 - المعادي", "01090829394", "يومياً من 10 ص إلى 12 م"),
        ("فرع مدينة نصر", "شارع عباس العقاد - مدينة نصر", "01090829395", "يومياً من 10 ص إلى 12 م"),
        ("فرع الزمالك", "المقر الرئيسي - القاهرة", "01090829393", "يومياً من 10 ص إلى 12 م")
    ]

    for branch in branches_data:
        check_query = "SELECT COUNT(*) as count FROM branches WHERE name = ?"
        result = execute_query(check_query, (branch[0],))
        if result and result[0]['count'] == 0:
            insert_query = """
            INSERT INTO branches (name, address, phone, working_hours)
            VALUES (?, ?, ?, ?)
            """
            execute_query(insert_query, branch, fetch=False)

    # إدراج المستخدم الافتراضي
    admin_password = hashlib.sha256("admin".encode()).hexdigest()
    check_admin = "SELECT COUNT(*) as count FROM users WHERE username = 'admin'"
    result = execute_query(check_admin)
    if result and result[0]['count'] == 0:
        insert_admin = """
        INSERT INTO users (username, password_hash, full_name, role, branch_id)
        VALUES ('admin', ?, 'مدير النظام', 'admin', 1)
        """
        execute_query(insert_admin, (admin_password,), fetch=False)

    # إدراج خدمات افتراضية مع الأكواد
    services_data = [
        ("S001", "قص شعر رجالي", "قص شعر احترافي للرجال", 50.00, 70.00, 40.00, 30, 15.0, "أحمد محمد", 1),
        ("S002", "قص شعر نسائي", "قص شعر احترافي للنساء", 80.00, 100.00, 60.00, 45, 20.0, "فاطمة علي", 1),
        ("S003", "صبغة شعر كاملة", "صبغة شعر بألوان متنوعة", 150.00, 200.00, 120.00, 90, 25.0, "سارة أحمد", 1),
        ("S004", "فرد شعر", "فرد شعر بالكيراتين", 200.00, 280.00, 160.00, 120, 30.0, "نور محمد", 1),
        ("S005", "تسريحة عروس", "تسريحة عروس فاخرة", 300.00, 400.00, 250.00, 180, 45.0, "ليلى حسن", 1),
        ("S006", "حلاقة ذقن", "حلاقة وتهذيب الذقن", 25.00, 35.00, 20.00, 20, 8.0, "أحمد محمد", 1),
        ("S007", "ماسك للوجه", "ماسك طبيعي للوجه", 100.00, 130.00, 80.00, 60, 15.0, "مريم سالم", 1),
        ("S008", "تنظيف بشرة", "تنظيف عميق للبشرة", 120.00, 160.00, 100.00, 75, 18.0, "هدى عبدالله", 1),
    ]

    for service in services_data:
        check_service = "SELECT COUNT(*) as count FROM services WHERE service_code = ?"
        result = execute_query(check_service, (service[0],))
        if result and result[0]['count'] == 0:
            insert_service = """
            INSERT INTO services (service_code, name, description, price_standard, price_premium, price_economy,
                                duration_minutes, commission_rate, default_employee_name, branch_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            execute_query(insert_service, service, fetch=False)

    # إدراج منتجات افتراضية مع الأكواد
    products_data = [
        ("P001", "شامبو للشعر الجاف", "شامبو مرطب للشعر الجاف", 30.00, 45.00, 60.00, 35.00, 50, 5, "زجاجة", 1),
        ("P002", "كريم للشعر", "كريم مغذي للشعر", 25.00, 35.00, 45.00, 25.00, 30, 5, "علبة", 1),
        ("P003", "زيت للشعر", "زيت طبيعي للشعر", 20.00, 25.00, 35.00, 20.00, 40, 5, "زجاجة", 1),
        ("P004", "صبغة شعر طبيعية", "صبغة شعر خالية من الأمونيا", 60.00, 80.00, 100.00, 50.00, 20, 3, "علبة", 1),
        ("P005", "ماسك للوجه", "ماسك طبيعي مرطب", 45.00, 60.00, 75.00, 35.00, 25, 5, "علبة", 1),
        ("P006", "كريم مرطب", "كريم مرطب للبشرة الجافة", 30.00, 40.00, 50.00, 25.00, 35, 5, "علبة", 1),
        ("P007", "سيروم للشعر", "سيروم مقوي للشعر", 70.00, 90.00, 110.00, 55.00, 15, 3, "زجاجة", 1),
        ("P008", "مقص شعر احترافي", "مقص شعر من الستانلس ستيل", 120.00, 150.00, 180.00, 100.00, 10, 2, "قطعة", 1),
    ]

    for product in products_data:
        check_product = "SELECT COUNT(*) as count FROM products WHERE product_code = ?"
        result = execute_query(check_product, (product[0],))
        if result and result[0]['count'] == 0:
            insert_product = """
            INSERT INTO products (product_code, name, description, cost_price, price_standard, price_premium,
                                price_economy, stock_quantity, min_stock_level, unit, branch_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            execute_query(insert_product, product, fetch=False)

# دوال جلب البيانات من قاعدة البيانات
def get_branches():
    """جلب جميع الفروع من قاعدة البيانات الموجودة"""
    try:
        # محاولة جلب من جدول Branches أولاً
        query = "SELECT * FROM Branches ORDER BY name"
        result = execute_query(query)
        if result:
            return result
    except:
        try:
            # محاولة جلب من جدول branches (حروف صغيرة)
            query = "SELECT * FROM branches WHERE is_active = 1 ORDER BY name"
            result = execute_query(query)
            if result:
                return result
        except:
            pass

    # إرجاع فروع افتراضية إذا لم توجد
    return [
        {"branch_id": 1, "name": "الفرع الرئيسي", "address": "القاهرة", "phone": "01090829393"},
        {"branch_id": 2, "name": "فرع المعادي", "address": "المعادي", "phone": "01090829394"},
        {"branch_id": 3, "name": "فرع مدينة نصر", "address": "مدينة نصر", "phone": "01090829395"}
    ]

def get_customers_by_branch(branch_id):
    """جلب العملاء حسب الفرع من قاعدة البيانات الموجودة"""
    try:
        # محاولة جلب من جدول Customers أولاً
        try:
            # التحقق من وجود عمود branch_id في جدول Customers
            check_column_query = """
            SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'branch_id'
            """
            column_exists = execute_query(check_column_query)

            if column_exists and column_exists[0]['count'] > 0:
                # إذا كان العمود موجود، استخدم الاستعلام مع branch_id
                query = """
                SELECT * FROM Customers
                WHERE branch_id = ? AND ISNULL(is_deleted, 0) = 0
                ORDER BY FirstName, LastName
                """
                result = execute_query(query, (branch_id,))
                if result:
                    return result
            else:
                # إذا لم يكن العمود موجود، جلب جميع العملاء
                query = """
                SELECT * FROM Customers
                ORDER BY FirstName, LastName
                """
                result = execute_query(query)
                if result:
                    return result
        except:
            pass

        # محاولة جلب من جدول customers (حروف صغيرة)
        try:
            check_column_query = """
            SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'customers' AND COLUMN_NAME = 'branch_id'
            """
            column_exists = execute_query(check_column_query)

            if column_exists and column_exists[0]['count'] > 0:
                query = """
                SELECT * FROM customers
                WHERE branch_id = ? AND ISNULL(is_deleted, 0) = 0
                ORDER BY first_name, last_name
                """
                result = execute_query(query, (branch_id,))
                if result:
                    return result
            else:
                query = """
                SELECT * FROM customers
                WHERE ISNULL(is_deleted, 0) = 0
                ORDER BY first_name, last_name
                """
                result = execute_query(query)
                if result:
                    return result
        except:
            pass

        return []

    except Exception as e:
        st.error(f"خطأ في جلب العملاء: {str(e)}")
        return []

def get_services_by_branch(branch_id):
    """جلب الخدمات حسب الفرع"""
    query = """
    SELECT * FROM services
    WHERE branch_id = ? AND is_active = 1
    ORDER BY name
    """
    return execute_query(query, (branch_id,)) or []

def get_products_by_branch(branch_id):
    """جلب المنتجات حسب الفرع"""
    query = """
    SELECT * FROM products
    WHERE branch_id = ? AND is_active = 1
    ORDER BY name
    """
    return execute_query(query, (branch_id,)) or []

def get_invoices_by_branch(branch_id):
    """جلب الفواتير حسب الفرع"""
    try:
        # التحقق من وجود عمود branch_id في جدول invoices
        check_column_query = """
        SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'branch_id'
        """
        column_exists = execute_query(check_column_query)

        if column_exists and column_exists[0]['count'] > 0:
            # إذا كان العمود موجود، استخدم الاستعلام مع branch_id
            query = """
            SELECT i.*, c.FirstName + ' ' + ISNULL(c.LastName, '') as customer_name
            FROM invoices i
            JOIN Customers c ON i.customer_id = c.CustomerId
            WHERE i.branch_id = ? AND ISNULL(i.is_deleted, 0) = 0
            ORDER BY i.created_at DESC
            """
            return execute_query(query, (branch_id,)) or []
        else:
            # إذا لم يكن العمود موجود، جلب جميع الفواتير
            query = """
            SELECT i.*, c.FirstName + ' ' + ISNULL(c.LastName, '') as customer_name
            FROM invoices i
            JOIN Customers c ON i.customer_id = c.CustomerId
            WHERE ISNULL(i.is_deleted, 0) = 0
            ORDER BY i.created_at DESC
            """
            return execute_query(query) or []
    except Exception as e:
        st.error(f"خطأ في جلب الفواتير: {str(e)}")
        return []

def get_cash_transactions_by_branch(branch_id):
    """جلب المعاملات النقدية حسب الفرع"""
    query = """
    SELECT * FROM cash_transactions
    WHERE branch_id = ?
    ORDER BY created_at DESC
    """
    return execute_query(query, (branch_id,)) or []

def get_categories_by_branch(branch_id):
    """جلب الفئات حسب الفرع"""
    try:
        query = """
        SELECT * FROM categories
        WHERE branch_id = ? AND is_deleted = 0
        ORDER BY name
        """
        return execute_query(query, (branch_id,)) or []
    except:
        return []

def get_items_by_branch(branch_id):
    """جلب الأصناف حسب الفرع"""
    try:
        query = """
        SELECT * FROM items
        WHERE branch_id = ? AND is_deleted = 0
        ORDER BY name
        """
        return execute_query(query, (branch_id,)) or []
    except:
        return []

def authenticate_user(username, password):
    """التحقق من صحة بيانات المستخدم"""
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    query = """
    SELECT u.*, b.name as branch_name
    FROM users u
    LEFT JOIN branches b ON u.branch_id = b.branch_id
    WHERE u.username = ? AND u.password_hash = ? AND u.is_active = 1
    """
    result = execute_query(query, (username, password_hash))
    return result[0] if result else None

def save_customer_to_db(customer_data):
    """حفظ عميل جديد في قاعدة البيانات مع كود تسلسلي"""
    try:
        # توليد كود العميل التسلسلي
        customer_code = get_next_sequence_code('customer_code')

        if not customer_code:
            # في حالة فشل التوليد، استخدم كود افتراضي
            customer_code = f"CUS{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # استخدام الهيكل الموجود لجدول العملاء مع إضافة الكود
        query = """
        INSERT INTO Customers (customer_code, FirstName, LastName, PhoneNumber, Email, DateOfBirth,
                             Notes, ReferredByZoneId, branch_id, CreatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
        """

        params = (
            customer_code,  # customer_code
            customer_data.get('arabic_name', customer_data.get('first_name', '')),  # FirstName
            customer_data.get('english_name', customer_data.get('last_name', '')),  # LastName
            customer_data.get('phone', ''),  # PhoneNumber
            customer_data.get('email', ''),  # Email
            customer_data.get('birth_date'),  # DateOfBirth
            customer_data.get('notes', ''),  # Notes
            customer_data.get('zone_id', 1),  # ReferredByZoneId
            customer_data.get('branch_id', 1)  # branch_id
        )

        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()

            # تنفيذ الإدراج والحصول على المعرف في استعلام واحد
            cursor.execute(query, params)
            conn.commit()

            # جلب آخر عميل تم إدراجه
            cursor.execute("""
                SELECT TOP 1 CustomerId
                FROM Customers
                WHERE FirstName = ? AND LastName = ? AND PhoneNumber = ?
                ORDER BY CreatedAt DESC
            """, (params[0], params[1], params[2]))

            result = cursor.fetchone()
            customer_id = result[0] if result else None

            conn.close()

            if customer_id:
                st.success("✅ تم حفظ العميل في قاعدة البيانات بنجاح!")
                return customer_id
            else:
                st.error("❌ فشل في الحصول على معرف العميل الجديد")
                return False
        else:
            st.error("❌ فشل الاتصال بقاعدة البيانات")
            return False

    except Exception as e:
        st.error(f"❌ خطأ في حفظ العميل: {str(e)}")
        return False

def save_invoice_to_db(invoice_data, items_data):
    """حفظ فاتورة جديدة في قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if not conn:
            return False

        cursor = conn.cursor()

        # حفظ الفاتورة الرئيسية - متوافق مع هيكل الجدول الحقيقي
        invoice_query = """
        INSERT INTO invoices (customer_id, branch_id, employee_id, invoice_date,
                            subtotal, tax_amount, discount_amount, total_amount,
                            payment_method, payment_status, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        invoice_params = (
            1,  # customer_id افتراضي
            invoice_data.get('branch_id', 1),
            1,  # employee_id افتراضي
            invoice_data.get('invoice_date', datetime.now().date()),
            invoice_data.get('subtotal', 0),
            invoice_data.get('tax_amount', 0),
            invoice_data.get('discount_amount', 0),
            invoice_data.get('total_amount', 0),
            invoice_data.get('payment_method', 'نقدي'),
            invoice_data.get('payment_status', 'مدفوعة'),
            invoice_data.get('notes', '')
        )

        # تنفيذ إدراج الفاتورة
        cursor.execute(invoice_query, invoice_params)

        # الحصول على ID الفاتورة الجديدة
        cursor.execute("SELECT SCOPE_IDENTITY()")
        result = cursor.fetchone()
        invoice_id = result[0] if result else None

        if invoice_id and items_data:
            # حفظ عناصر الفاتورة
            items_query = """
            INSERT INTO invoice_items (invoice_id, item_type, item_code, item_name, quantity,
                                     unit_price, total_price, employee_name, commission_amount)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            for item in items_data:
                item_params = (
                    invoice_id,
                    item.get('type', 'خدمة'),
                    item.get('code', ''),
                    item.get('name', ''),
                    item.get('quantity', 1),
                    item.get('price', 0),
                    item.get('total', 0),
                    item.get('employee', 'غير محدد'),
                    item.get('commission', 0)
                )
                cursor.execute(items_query, item_params)

        # تأكيد الحفظ
        conn.commit()
        conn.close()

        return invoice_id

    except Exception as e:
        st.error(f"خطأ في حفظ الفاتورة: {str(e)}")
        return False

def save_cash_transaction_to_db(transaction_data):
    """حفظ معاملة نقدية في قاعدة البيانات"""
    try:
        # التحقق من وجود عمود branch_id
        check_column_query = """
        SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'cash_transactions' AND COLUMN_NAME = 'branch_id'
        """
        column_exists = execute_query(check_column_query)

        if column_exists and column_exists[0]['count'] > 0:
            # إذا كان العمود موجود، استخدم الاستعلام مع branch_id
            query = """
            INSERT INTO cash_transactions (branch_id, transaction_type, amount, description, created_by)
            VALUES (?, ?, ?, ?, ?)
            """
            params = (
                transaction_data.get('branch_id', 1),
                transaction_data['type'],
                transaction_data['amount'],
                transaction_data['description'],
                transaction_data['user']
            )
        else:
            # إذا لم يكن العمود موجود، استخدم الاستعلام بدون branch_id
            query = """
            INSERT INTO cash_transactions (transaction_type, amount, description, created_by)
            VALUES (?, ?, ?, ?)
            """
            params = (
                transaction_data['type'],
                transaction_data['amount'],
                transaction_data['description'],
                transaction_data['user']
            )

        return execute_query(query, params, fetch=False)

    except Exception as e:
        st.error(f"خطأ في حفظ المعاملة النقدية: {str(e)}")
        return False

def get_cash_balance_from_db(branch_id):
    """حساب الرصيد النقدي من قاعدة البيانات"""
    try:
        # التحقق من وجود عمود branch_id في جدول cash_transactions
        check_column_query = """
        SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'cash_transactions' AND COLUMN_NAME = 'branch_id'
        """
        column_exists = execute_query(check_column_query)

        if column_exists and column_exists[0]['count'] > 0:
            # إذا كان العمود موجود، استخدم الاستعلام مع branch_id
            query = """
            SELECT
                SUM(CASE WHEN transaction_type IN ('income', 'invoice_payment') THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expense
            FROM cash_transactions
            WHERE branch_id = ?
            """
            result = execute_query(query, (branch_id,))
        else:
            # إذا لم يكن العمود موجود، جلب جميع المعاملات
            query = """
            SELECT
                SUM(CASE WHEN transaction_type IN ('income', 'invoice_payment') THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as total_expense
            FROM cash_transactions
            """
            result = execute_query(query)

        if result and result[0]:
            total_income = result[0]['total_income'] or 0
            total_expense = result[0]['total_expense'] or 0
            return total_income - total_expense
        return 0
    except Exception as e:
        st.error(f"خطأ في حساب الرصيد النقدي: {str(e)}")
        return 0

# إعداد الصفحة
st.set_page_config(
    page_title="SalonProManager - Professional Salon Management",
    page_icon="💄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# تهيئة نظام إدارة النقدية
def init_cash_management():
    """تهيئة نظام إدارة النقدية - يعتمد على قاعدة البيانات فقط"""
    # لا نحتاج لتهيئة بيانات محلية - كل شيء من قاعدة البيانات
    pass

def add_cash_transaction(amount, transaction_type, description, branch_id, user):
    """إضافة معاملة نقدية"""
    transaction = {
        'id': len(st.session_state.cash_transactions) + 1,
        'amount': amount,
        'type': transaction_type,  # 'income', 'expense', 'invoice_payment'
        'description': description,
        'branch_id': branch_id,
        'user': user,
        'date': datetime.now().strftime('%Y-%m-%d'),
        'time': datetime.now().strftime('%H:%M:%S'),
        'timestamp': datetime.now().isoformat()
    }

    # حفظ مباشرة في قاعدة البيانات أولاً
    if st.session_state.get('db_initialized', False):
        try:
            transaction_data = {
                'branch_id': branch_id,
                'type': transaction_type,
                'amount': amount,
                'description': description,
                'user': user
            }

            db_result = save_cash_transaction_to_db(transaction_data)

            if db_result:
                # حفظ في session state كنسخة احتياطية
                st.session_state.cash_transactions.append(transaction)

                # تحديث الرصيد
                if transaction_type in ['income', 'invoice_payment']:
                    st.session_state.cash_balance += amount
                elif transaction_type == 'expense':
                    st.session_state.cash_balance -= amount

                return True
            else:
                st.error("❌ فشل في حفظ المعاملة في قاعدة البيانات")
                return False

        except Exception as e:
            st.error(f"❌ خطأ في حفظ المعاملة: {str(e)}")
            # حفظ محلي كنسخة احتياطية
            st.session_state.cash_transactions.append(transaction)

            # تحديث الرصيد
            if transaction_type in ['income', 'invoice_payment']:
                st.session_state.cash_balance += amount
            elif transaction_type == 'expense':
                st.session_state.cash_balance -= amount

            return False
    else:
        st.error("❌ قاعدة البيانات غير متصلة - لا يمكن حفظ المعاملة")
        return False

def get_cash_balance_by_branch(branch_id):
    """حساب الرصيد النقدي لفرع معين من قاعدة البيانات أولاً"""

    # محاولة جلب الرصيد من قاعدة البيانات أولاً
    if st.session_state.get('db_initialized', False):
        try:
            db_balance = get_cash_balance_from_db(branch_id)
            return db_balance
        except Exception as e:
            st.warning(f"تعذر جلب الرصيد من قاعدة البيانات: {str(e)}")

    # الرجوع للحساب المحلي كنسخة احتياطية
    if 'cash_transactions' not in st.session_state:
        return 0

    branch_transactions = [t for t in st.session_state.cash_transactions
                          if t.get('branch_id') == branch_id]

    balance = 0.0
    for transaction in branch_transactions:
        if transaction['type'] in ['income', 'invoice_payment']:
            balance += transaction['amount']
        elif transaction['type'] == 'expense':
            balance -= transaction['amount']

    return balance

# CSS مخصص للتصميم الاحترافي
st.markdown("""
<style>
    /* تصميم الشريط الجانبي */
    .css-1d391kg {
        background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
        color: white;
    }

    /* تصميم الأزرار */
    .stButton > button {
        background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* تصميم البطاقات */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        border-left: 4px solid #3b82f6;
        margin: 0.5rem 0;
    }

    /* تصميم الجداول */
    .dataframe {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* تصميم العناوين */
    .main-header {
        background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    /* تصميم النماذج */
    .stForm {
        background: #f8fafc;
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    /* تصميم التبويبات */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }

    .stTabs [data-baseweb="tab"] {
        background: #f1f5f9;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        border: 1px solid #e2e8f0;
    }

    .stTabs [aria-selected="true"] {
        background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
    }

    /* تصميم الإشعارات */
    .stSuccess {
        background: #dcfce7;
        border: 1px solid #16a34a;
        border-radius: 8px;
        padding: 1rem;
    }

    .stError {
        background: #fef2f2;
        border: 1px solid #dc2626;
        border-radius: 8px;
        padding: 1rem;
    }

    .stInfo {
        background: #dbeafe;
        border: 1px solid #2563eb;
        border-radius: 8px;
        padding: 1rem;
    }

    /* تصميم الشريط العلوي */
    .top-bar {
        background: linear-gradient(90deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 0.5rem 2rem;
        margin: -1rem -1rem 2rem -1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* تصميم البحث */
    .search-container {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }

    /* تصميم الفواتير */
    .invoice-container {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }

    .invoice-header {
        background: linear-gradient(90deg, #059669 0%, #10b981 100%);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .invoice-item {
        background: #f8fafc;
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #3b82f6;
    }

    /* تصميم الإحصائيات */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        text-align: center;
        border-top: 4px solid #3b82f6;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #1e40af;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

# إعداد الجلسة
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'user' not in st.session_state:
    st.session_state.user = None
if 'token' not in st.session_state:
    st.session_state.token = None

def make_api_request(endpoint, method="GET", data=None, headers=None):
    """إجراء طلب مباشر من قاعدة البيانات بدلاً من API"""
    # استخدام قاعدة البيانات مباشرة بدلاً من API
    return get_local_data(endpoint)

def get_local_data(endpoint):
    """الحصول على البيانات من قاعدة البيانات مباشرة"""
    try:
        if endpoint == "/branches":
            return get_branches()
        elif endpoint == "/customers":
            return get_customers_by_branch(st.session_state.get('current_branch_id', 1))
        elif endpoint == "/services":
            return get_services_by_branch(st.session_state.get('current_branch_id', 1))
        elif endpoint == "/products":
            return get_products_by_branch(st.session_state.get('current_branch_id', 1))
        elif endpoint == "/invoices":
            return get_invoices_by_branch(st.session_state.get('current_branch_id', 1))
        elif endpoint == "/categories":
            return get_categories_by_branch(st.session_state.get('current_branch_id', 1))
        elif endpoint == "/items":
            return get_items_by_branch(st.session_state.get('current_branch_id', 1))
        else:
            return []
    except Exception as e:
        # في حالة فشل قاعدة البيانات، استخدم البيانات الافتراضية
        return get_default_data(endpoint)

def get_default_data(endpoint):
    """الحصول على البيانات الافتراضية في حالة فشل قاعدة البيانات"""
    if endpoint == "/branches":
        return [
            {"branch_id": 1, "name": "الفرع الرئيسي", "address": "القاهرة", "phone": "01090829393"},
            {"branch_id": 2, "name": "فرع المعادي", "address": "المعادي", "phone": "01090829394"},
            {"branch_id": 3, "name": "فرع مدينة نصر", "address": "مدينة نصر", "phone": "01090829395"}
        ]
    elif endpoint == "/customers":
        return []
    elif endpoint == "/services":
        return [
            {"service_id": 1, "service_code": "S001", "name": "قص شعر رجالي", "price_standard": 50.0},
            {"service_id": 2, "service_code": "S002", "name": "قص شعر نسائي", "price_standard": 80.0},
            {"service_id": 3, "service_code": "S003", "name": "صبغة شعر", "price_standard": 150.0}
        ]
    elif endpoint == "/products":
        return [
            {"product_id": 1, "product_code": "P001", "name": "شامبو للشعر", "price_standard": 45.0},
            {"product_id": 2, "product_code": "P002", "name": "كريم للشعر", "price_standard": 35.0},
            {"product_id": 3, "product_code": "P003", "name": "زيت للشعر", "price_standard": 25.0}
        ]
    else:
        return []

def login_page():
    """صفحة تسجيل الدخول"""

    # رسالة ترحيب للمستخدمين الجدد
    st.markdown("""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
               color: white; padding: 2rem; border-radius: 15px; margin: 1rem 0; text-align: center;">
        <h1>🎉 مرحباً بك في SalonProManager</h1>
        <p style="font-size: 1.2rem; margin: 1rem 0;">
            نظام إدارة الصالونات الشامل والمتطور
        </p>
        <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 1.5rem; margin: 1rem 0;">
            <h3>📋 بيانات تسجيل الدخول:</h3>
            <p style="font-size: 1.1rem;"><strong>👤 اسم المستخدم:</strong> admin</p>
            <p style="font-size: 1.1rem;"><strong>🔐 كلمة المرور:</strong> 123</p>
        </div>
        <p style="font-size: 1rem; opacity: 0.9;">
            💡 النظام يعمل حالياً بالبيانات المحلية - جميع الميزات متاحة!
        </p>
    </div>
    """, unsafe_allow_html=True)

    st.title("🔐 تسجيل الدخول - SalonProManager")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        with st.form("login_form"):
            st.subheader("🔐 تسجيل الدخول")

            # معلومات المستخدم
            username = st.text_input("اسم المستخدم", placeholder="admin")
            password = st.text_input("كلمة المرور", type="password", placeholder="admin123")

            # اختيار الفرع
            st.subheader("🏢 اختر الفرع")
            branch_options = {
                "فرع مدينة نصر": {
                    "id": 1,
                    "address": "1 محمود سامي البارودي – من حسنين هيكل",
                    "phone": "01090829393"
                },
                "فرع جسر السويس (أحمد عصمت)": {
                    "id": 2,
                    "address": "شارع أحمد عصمت – أمام بوابة 4 نادي الشمس",
                    "phone": "01090829393"
                },
                "فرع الزهراء": {
                    "id": 3,
                    "address": "مدينة الزهراء - القاهرة",
                    "phone": "01090829393"
                },
                "الإدارة العامة": {
                    "id": 0,
                    "address": "المقر الرئيسي - القاهرة",
                    "phone": "01090829393"
                }
            }

            selected_branch = st.selectbox(
                "اختر الفرع الذي تريد العمل به:",
                options=list(branch_options.keys()),
                help="سيتم عرض بيانات هذا الفرع فقط"
            )

            # عرض معلومات الفرع المختار
            if selected_branch:
                branch_info = branch_options[selected_branch]
                st.info(f"📍 {branch_info['address']}\n📞 {branch_info['phone']}")

            submit = st.form_submit_button("🚀 دخول للنظام", use_container_width=True)

            if submit:
                if username and password:
                    # محاكاة تسجيل الدخول
                    if username == "admin" and password == "admin123":
                        branch_info = branch_options[selected_branch]

                        st.session_state.authenticated = True
                        st.session_state.user = {
                            "username": username,
                            "full_name": get_user_name_by_branch(username, branch_info["id"]),
                            "role": get_user_role_by_branch(username, branch_info["id"]),
                            "permissions": get_user_permissions_by_branch(username, branch_info["id"])
                        }
                        st.session_state.current_branch_id = branch_info["id"]
                        st.session_state.current_branch_name = selected_branch
                        st.session_state.current_branch_info = branch_info

                        st.success(f"مرحباً {st.session_state.user['full_name']} في {selected_branch}")
                        st.rerun()
                    else:
                        st.error("اسم المستخدم أو كلمة المرور غير صحيحة")
                else:
                    st.error("يرجى إدخال اسم المستخدم وكلمة المرور")

def get_user_name_by_branch(username, branch_id):
    """الحصول على اسم المستخدم حسب الفرع"""
    if username == "admin":
        if branch_id == 0:
            return "المدير العام"
        elif branch_id == 1:
            return "مدير فرع مدينة نصر"
        elif branch_id == 2:
            return "مدير فرع جسر السويس"
        elif branch_id == 3:
            return "مدير فرع الزهراء"
    return "مدير النظام"

def get_user_role_by_branch(username, branch_id):
    """الحصول على دور المستخدم حسب الفرع"""
    if username == "admin":
        if branch_id == 0:
            return "مدير عام"
        else:
            return "مدير فرع"
    return "موظف"

def get_user_permissions_by_branch(username, branch_id):
    """الحصول على صلاحيات المستخدم حسب الفرع"""
    if username == "admin":
        return ["all"]  # الإدارة العامة لها صلاحية الكل

    # جلب صلاحيات المستخدم من قاعدة البيانات
    try:
        query = """
        SELECT u.role, u.permissions
        FROM users u
        WHERE u.username = ?
        """
        result = execute_query(query, (username,))

        if result:
            user_data = result[0]
            role = user_data.get('role', 'employee')

            # تحديد الصلاحيات حسب الدور
            if role == 'admin':
                return ["all"]
            elif role == 'manager':
                return ["read", "write", "delete", "reports", "manage_employees"]
            elif role == 'employee':
                return ["read", "write"]
            else:
                return ["read"]
        else:
            return ["read"]  # صلاحيات افتراضية

    except Exception as e:
        st.warning(f"تعذر جلب صلاحيات المستخدم: {str(e)}")
        return ["read"]

def get_user_branches(username):
    """جلب الفروع المخصصة للمستخدم"""
    if username == "admin":
        # الإدارة العامة ترى جميع الفروع
        try:
            query = "SELECT * FROM branches WHERE is_active = 1"
            return execute_query(query) or []
        except:
            return [{"branch_id": 1, "name": "الفرع الرئيسي", "address": "القاهرة"}]

    try:
        # للمستخدمين العاديين، جلب الفروع من جدول branches
        query = "SELECT * FROM branches WHERE is_active = 1"
        result = execute_query(query)

        if result:
            return result
        else:
            # إذا لم توجد فروع، أعطه الفرع الافتراضي
            return [{"branch_id": 1, "name": "الفرع الرئيسي"}]

    except Exception as e:
        st.warning(f"تعذر جلب الفروع: {str(e)}")
        return [{"branch_id": 1, "name": "الفرع الرئيسي"}]

def show_professional_invoice_print(invoice):
    """عرض الفاتورة بتصميم احترافي للطباعة"""
    st.markdown(f"""
    <style>
    .print-invoice {{
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 1rem 0;
        font-family: 'Arial', sans-serif;
    }}
    .invoice-header-print {{
        text-align: center;
        border-bottom: 3px solid #3b82f6;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }}
    .invoice-details {{
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin: 1.5rem 0;
    }}
    .invoice-items-table {{
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
    }}
    .invoice-items-table th,
    .invoice-items-table td {{
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        text-align: right;
    }}
    .invoice-items-table th {{
        background: #f3f4f6;
        font-weight: bold;
    }}
    .invoice-total-section {{
        background: #f8fafc;
        border: 2px solid #3b82f6;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: center;
    }}
    @media print {{
        .print-invoice {{
            box-shadow: none;
            border: 1px solid #000;
        }}
    }}
    </style>

    <div class="print-invoice">
        <div class="invoice-header-print">
            <h1 style="color: #3b82f6; margin: 0;">🏢 SalonProManager</h1>
            <h2 style="color: #6b7280; margin: 0.5rem 0;">فاتورة مبيعات</h2>
            <p style="margin: 0; color: #9ca3af;">نظام إدارة صالونات احترافي</p>
        </div>

        <div class="invoice-details">
            <div>
                <h3 style="color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">
                    📋 بيانات الفاتورة
                </h3>
                <p><strong>رقم الفاتورة:</strong> INV-{invoice['id']:04d}</p>
                <p><strong>التاريخ:</strong> {invoice['date']}</p>
                <p><strong>الوقت:</strong> {invoice['time']}</p>
                <p><strong>طريقة الدفع:</strong> {invoice['payment_method']}</p>
                <p><strong>الكاشير:</strong> {invoice.get('created_by', 'غير محدد')}</p>
            </div>

            <div>
                <h3 style="color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">
                    👤 بيانات العميل
                </h3>
                <p><strong>الاسم:</strong> {invoice['customer_name']}</p>
                <p><strong>الفرع:</strong> {invoice.get('branch_name', 'الفرع الرئيسي')}</p>
            </div>
        </div>

        <h3 style="color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 0.5rem;">
            🛍️ تفاصيل العناصر
        </h3>

        <table class="invoice-items-table">
            <thead>
                <tr>
                    <th>البيان</th>
                    <th>النوع</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
    """, unsafe_allow_html=True)

    # عرض الخدمات
    if invoice.get('services'):
        for service_name, service_data in invoice['services'].items():
            st.markdown(f"""
                <tr>
                    <td>{service_name}</td>
                    <td>خدمة</td>
                    <td>{service_data.get('quantity', 1)}</td>
                    <td>{service_data.get('price', 0):.2f} ج.م</td>
                    <td>{service_data.get('total', service_data.get('price', 0)):.2f} ج.م</td>
                </tr>
            """, unsafe_allow_html=True)

    # عرض المنتجات
    if invoice.get('products'):
        for product_name, product_data in invoice['products'].items():
            st.markdown(f"""
                <tr>
                    <td>{product_name}</td>
                    <td>منتج</td>
                    <td>{product_data.get('quantity', 1)}</td>
                    <td>{product_data.get('price', 0):.2f} ج.م</td>
                    <td>{product_data.get('total', product_data.get('price', 0)):.2f} ج.م</td>
                </tr>
            """, unsafe_allow_html=True)

    st.markdown(f"""
            </tbody>
        </table>

        <div class="invoice-total-section">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; text-align: right;">
                <div>
                    <p><strong>المجموع الفرعي:</strong></p>
                    <p><strong>الخصم:</strong></p>
                    <p><strong>الضريبة ({invoice.get('tax_rate', 14)}%):</strong></p>
                    {f"<p><strong>رسوم خدمة:</strong></p>" if invoice.get('service_charge', 0) > 0 else ""}
                </div>
                <div>
                    <p>{invoice['subtotal']:.2f} ج.م</p>
                    <p>-{invoice['discount_amount']:.2f} ج.م</p>
                    <p>+{invoice['tax_amount']:.2f} ج.م</p>
                    {f"<p>+{invoice['service_charge']:.2f} ج.م</p>" if invoice.get('service_charge', 0) > 0 else ""}
                </div>
            </div>

            <hr style="margin: 1rem 0; border: 1px solid #3b82f6;">

            <h2 style="color: #3b82f6; margin: 0;">
                الإجمالي النهائي: {invoice['total_amount']:.2f} ج.م
            </h2>
        </div>

        <div style="text-align: center; margin-top: 2rem; color: #6b7280;">
            <p>شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</p>
            <p>📞 01090829393 | 📧 <EMAIL></p>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # زر الطباعة
    if st.button("🖨️ طباعة الفاتورة", use_container_width=True):
        st.success("تم إرسال الفاتورة للطباعة!")

def show_return_form(original_invoice):
    """عرض نموذج المرتجعات"""
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                color: white; padding: 1.5rem; border-radius: 15px; margin: 1rem 0;">
        <h3 style="margin: 0; text-align: center;">
            ↩️ مرتجع فاتورة رقم INV-{original_invoice['id']:04d}
        </h3>
    </div>
    """, unsafe_allow_html=True)

    with st.form(f"return_form_{original_invoice['id']}"):
        st.markdown("### 📋 تفاصيل المرتجع")

        col1, col2 = st.columns(2)

        with col1:
            return_reason = st.selectbox(
                "سبب المرتجع",
                ["عيب في المنتج", "عدم رضا العميل", "خطأ في الطلب", "منتج منتهي الصلاحية", "أخرى"]
            )

            if return_reason == "أخرى":
                custom_reason = st.text_input("اكتب السبب")
                return_reason = custom_reason if custom_reason else return_reason

        with col2:
            return_date = st.date_input("تاريخ المرتجع", value=datetime.now().date())
            refund_method = st.selectbox(
                "طريقة الاسترداد",
                ["نقدي", "رصيد للعميل", "استبدال", "كارت ائتمان"]
            )

        st.markdown("### 🛍️ العناصر المراد إرجاعها")

        # عرض عناصر الفاتورة الأصلية للاختيار
        return_items = []

        # الخدمات
        if original_invoice.get('services'):
            st.markdown("**الخدمات:**")
            for service_name, service_data in original_invoice['services'].items():
                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    st.write(f"• {service_name}")

                with col2:
                    st.write(f"السعر: {service_data.get('price', 0):.2f} ج.م")

                with col3:
                    if st.checkbox("إرجاع", key=f"return_service_{service_name}"):
                        return_items.append({
                            'type': 'service',
                            'name': service_name,
                            'price': service_data.get('price', 0),
                            'quantity': service_data.get('quantity', 1),
                            'total': service_data.get('total', service_data.get('price', 0))
                        })

        # المنتجات
        if original_invoice.get('products'):
            st.markdown("**المنتجات:**")
            for product_name, product_data in original_invoice['products'].items():
                col1, col2, col3, col4 = st.columns([2, 1, 1, 1])

                with col1:
                    st.write(f"• {product_name}")

                with col2:
                    st.write(f"الكمية: {product_data.get('quantity', 1)}")

                with col3:
                    st.write(f"السعر: {product_data.get('price', 0):.2f} ج.م")

                with col4:
                    if st.checkbox("إرجاع", key=f"return_product_{product_name}"):
                        return_quantity = st.number_input(
                            f"كمية الإرجاع",
                            min_value=1,
                            max_value=product_data.get('quantity', 1),
                            value=1,
                            key=f"return_qty_{product_name}"
                        )

                        return_items.append({
                            'type': 'product',
                            'name': product_name,
                            'price': product_data.get('price', 0),
                            'quantity': return_quantity,
                            'total': product_data.get('price', 0) * return_quantity
                        })

        # حساب إجمالي المرتجع
        if return_items:
            total_return = sum([item['total'] for item in return_items])
            st.markdown(f"""
            <div style="background: #e8f5e8; border: 2px solid #27ae60; border-radius: 10px;
                        padding: 1rem; text-align: center; margin: 1rem 0;">
                <h3 style="color: #27ae60; margin: 0;">
                    إجمالي المرتجع: {total_return:.2f} ج.م
                </h3>
            </div>
            """, unsafe_allow_html=True)

        notes = st.text_area("ملاحظات إضافية")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.form_submit_button("✅ تأكيد المرتجع", type="primary", use_container_width=True):
                if return_items:
                    # إنشاء مرتجع جديد
                    return_invoice = {
                        'id': len(st.session_state.get('returns', [])) + 1,
                        'original_invoice_id': original_invoice['id'],
                        'customer_name': original_invoice['customer_name'],
                        'date': return_date.strftime('%Y-%m-%d'),
                        'time': datetime.now().strftime('%H:%M'),
                        'reason': return_reason,
                        'refund_method': refund_method,
                        'items': return_items,
                        'total_amount': total_return,
                        'notes': notes,
                        'created_by': st.session_state.user['username'],
                        'branch_id': original_invoice.get('branch_id', 1),
                        'branch_name': original_invoice.get('branch_name', 'الفرع الرئيسي'),
                        'status': 'مكتمل'
                    }

                    # حفظ المرتجع
                    if 'returns' not in st.session_state:
                        st.session_state.returns = []

                    st.session_state.returns.append(return_invoice)

                    # إضافة معاملة نقدية للمرتجع
                    if refund_method == "نقدي":
                        add_cash_transaction(
                            transaction_type="expense",
                            amount=total_return,
                            description=f"مرتجع فاتورة INV-{original_invoice['id']:04d} - {return_reason}",
                            user=st.session_state.user['username'],
                            branch_id=original_invoice.get('branch_id', 1)
                        )

                    st.success(f"✅ تم إنشاء المرتجع بنجاح! رقم المرتجع: RET-{return_invoice['id']:04d}")
                    st.session_state[f'show_return_form_{original_invoice["id"]}'] = False
                    st.rerun()
                else:
                    st.error("⚠️ يجب اختيار عنصر واحد على الأقل للإرجاع!")

        with col2:
            if st.form_submit_button("💾 حفظ كمسودة", use_container_width=True):
                st.info("تم حفظ المرتجع كمسودة")

        with col3:
            if st.form_submit_button("❌ إلغاء", use_container_width=True):
                st.session_state[f'show_return_form_{original_invoice["id"]}'] = False
                st.rerun()

def main_app():
    """التطبيق الرئيسي"""

    # تهيئة اللغة
    if 'language' not in st.session_state:
        st.session_state.language = 'ar'

    # تهيئة قاعدة البيانات
    if 'db_initialized' not in st.session_state:
        with st.spinner('جاري تهيئة قاعدة البيانات...'):
            try:
                init_database()
                st.session_state.db_initialized = True
                st.success("تم الاتصال بقاعدة البيانات بنجاح!")
            except Exception as e:
                st.error(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
                st.info("سيتم استخدام البيانات المحلية بدلاً من قاعدة البيانات")
                st.session_state.db_initialized = False

    # زر تبديل اللغة في أعلى الصفحة
    col1, col2, col3 = st.columns([4, 1, 1])

    with col2:
        if st.button("🌐 العربية" if st.session_state.language == 'en' else "🌐 English",
                     use_container_width=True):
            st.session_state.language = 'en' if st.session_state.language == 'ar' else 'ar'
            st.rerun()

    with col3:
        # عرض التاريخ والوقت
        current_time = datetime.now()
        if st.session_state.language == 'ar':
            st.write(f"📅 {current_time.strftime('%Y-%m-%d')}")
            st.write(f"⏰ {current_time.strftime('%H:%M')}")
        else:
            st.write(f"📅 {current_time.strftime('%d-%m-%Y')}")
            st.write(f"⏰ {current_time.strftime('%I:%M %p')}")

    # الشريط الجانبي
    with st.sidebar:
        # عنوان التطبيق حسب اللغة
        app_title = get_text('app_title', st.session_state.language)
        st.title("🏢 " + app_title.split(' - ')[0])

        # معلومات المستخدم والفرع
        user = st.session_state.user
        user_label = get_text('user', st.session_state.language)
        st.write(f"👤 **{user['full_name']}**")
        st.write(f"💼 {user.get('role', get_text('employee', st.session_state.language))}")

        # اختيار الفرع من قاعدة البيانات
        st.markdown("### 🏢 اختيار الفرع")

        # جلب الفروع المتاحة للمستخدم
        user_branches = get_user_branches(st.session_state.user['username'])

        if user_branches:
            # إنشاء خيارات الفروع
            branch_options = {}
            for branch in user_branches:
                branch_name = branch.get('name', f"فرع {branch.get('branch_id', 'غير محدد')}")
                branch_options[branch_name] = {
                    'id': branch.get('branch_id', 1),
                    'name': branch_name,
                    'address': branch.get('address', ''),
                    'phone': branch.get('phone', '')
                }

            # اختيار الفرع
            current_branch_name = st.session_state.get('current_branch_name', list(branch_options.keys())[0])

            selected_branch_name = st.selectbox(
                "اختر الفرع:",
                options=list(branch_options.keys()),
                index=list(branch_options.keys()).index(current_branch_name) if current_branch_name in branch_options else 0,
                key="branch_selector"
            )

            # تحديث معلومات الفرع المختار
            if selected_branch_name != st.session_state.get('current_branch_name'):
                selected_branch_info = branch_options[selected_branch_name]
                st.session_state.current_branch_id = selected_branch_info['id']
                st.session_state.current_branch_name = selected_branch_info['name']
                st.session_state.current_branch_info = selected_branch_info
                st.rerun()

            # عرض معلومات الفرع الحالي
            current_branch_info = branch_options.get(selected_branch_name, {})
            st.write(f"🏢 **{selected_branch_name}**")
            if current_branch_info.get('address'):
                st.write(f"📍 {current_branch_info['address']}")
            if current_branch_info.get('phone'):
                st.write(f"📞 {current_branch_info['phone']}")
        else:
            # فرع افتراضي إذا لم توجد فروع
            st.session_state.current_branch_id = 1
            st.session_state.current_branch_name = "الفرع الرئيسي"
            st.write(f"🏢 **الفرع الرئيسي**")
            st.info("لا توجد فروع متاحة في قاعدة البيانات")

        # إحصائيات سريعة للفرع
        st.divider()
        col1, col2 = st.columns(2)
        with col1:
            st.metric("مواعيد اليوم", "5", "1")
        with col2:
            st.metric("إيرادات اليوم", "1,200 ج.م", "200")

        st.divider()

        # قائمة التنقل حسب الصلاحيات
        user_permissions = user.get('permissions', ['basic'])

        available_pages = ["لوحة القيادة"]

        if 'all' in user_permissions or 'branch_management' in user_permissions:
            available_pages.extend(["إدارة الفروع", "إدارة الموظفين"])

        if 'all' in user_permissions or 'customers' in user_permissions:
            available_pages.append("إدارة العملاء")

        if 'all' in user_permissions or 'inventory' in user_permissions:
            available_pages.append("إدارة الفئات والأصناف")

        if 'all' in user_permissions or 'services' in user_permissions:
            available_pages.extend(["إدارة المواعيد", "إدارة الخدمات"])

        if 'all' in user_permissions or 'sales' in user_permissions:
            available_pages.extend(["إدارة المنتجات", "المبيعات والفواتير", "إدارة المرتجعات", "إدارة النقدية", "الخزنة"])

        if 'all' in user_permissions or 'reports' in user_permissions:
            available_pages.append("التقارير")

        if 'all' in user_permissions:
            available_pages.extend(["الإعدادات", "استعلام قاعدة البيانات"])

        page = st.selectbox("اختر الصفحة", available_pages)

        st.divider()

        # معلومات إضافية
        st.write("📞 **للدعم:** 01090829393")
        st.write("💬 **واتساب:** [اضغط هنا](https://wa.me/201090829393)")

        if st.button("تسجيل الخروج", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.user = None
            st.session_state.token = None
            if 'current_branch_id' in st.session_state:
                del st.session_state.current_branch_id
            if 'current_branch_name' in st.session_state:
                del st.session_state.current_branch_name
            st.rerun()
    
    # المحتوى الرئيسي
    if page == "لوحة القيادة":
        dashboard_page()
    elif page == "إدارة الفروع":
        branches_page()
    elif page == "إدارة العملاء":
        customers_page()
    elif page == "إدارة الفئات والأصناف":
        categories_page()
    elif page == "إدارة المواعيد":
        appointments_page()
    elif page == "إدارة الخدمات":
        services_page()
    elif page == "إدارة الموظفين":
        employees_page()
    elif page == "إدارة المنتجات":
        products_page()
    elif page == "المبيعات والفواتير":
        sales_page()
    elif page == "إدارة المرتجعات":
        returns_page()
    elif page == "إدارة النقدية":
        cash_management_page()
    elif page == "الخزنة":
        show_cash_register_page()
    elif page == "التقارير":
        reports_page()
    elif page == "الإعدادات":
        settings_page()
    elif page == "استعلام قاعدة البيانات":
        database_query_page()

def cash_management_page():
    """صفحة إدارة النقدية والحسابات المالية"""
    st.title("💰 إدارة النقدية والحسابات المالية")

    # تهيئة نظام النقدية
    init_cash_management()

    current_branch_id = st.session_state.get('current_branch_id', 1)
    current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

    # الرصيد الحالي
    current_balance = get_cash_balance_by_branch(current_branch_id)

    # الشريط العلوي مع الرصيد
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                color: white; padding: 2rem; border-radius: 15px; margin-bottom: 2rem;
                box-shadow: 0 8px 32px rgba(44, 62, 80, 0.3);">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h2 style="margin: 0; color: white;">💰 إدارة النقدية - {current_branch_name}</h2>
                <p style="margin: 0; opacity: 0.9;">إدارة شاملة للمعاملات النقدية والحسابات</p>
            </div>
            <div style="text-align: center; background: rgba(255,255,255,0.2);
                        padding: 1.5rem; border-radius: 15px;">
                <h3 style="margin: 0; color: white;">الرصيد النقدي الحالي</h3>
                <h1 style="margin: 0.5rem 0 0 0; color: #f1c40f; font-size: 2.5rem;
                           text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{current_balance:.2f} ج.م</h1>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # تبويبات إدارة النقدية
    tab1, tab2, tab3, tab4 = st.tabs(["💵 المعاملات النقدية", "📊 تقارير النقدية", "💸 المصروفات", "📈 التحليلات المالية"])

    with tab1:
        st.subheader("💵 إدارة المعاملات النقدية")

        col1, col2 = st.columns([1, 2])

        with col1:
            # نموذج إضافة معاملة نقدية
            with st.form("cash_transaction_form"):
                st.markdown("**➕ إضافة معاملة نقدية جديدة**")

                transaction_type = st.selectbox(
                    "نوع المعاملة",
                    ["income", "expense"],
                    format_func=lambda x: "إيراد 💰" if x == "income" else "مصروف 💸"
                )

                amount = st.number_input("المبلغ (ج.م)", min_value=0.01, step=0.01)
                description = st.text_area("وصف المعاملة", placeholder="اكتب وصف تفصيلي للمعاملة...")

                if st.form_submit_button("💾 حفظ المعاملة", use_container_width=True):
                    if amount > 0 and description:
                        add_cash_transaction(
                            amount=amount,
                            transaction_type=transaction_type,
                            description=description,
                            branch_id=current_branch_id,
                            user=st.session_state.user['username']
                        )
                        st.success(f"✅ تم حفظ المعاملة بنجاح!")
                        st.rerun()
                    else:
                        st.error("⚠️ يرجى ملء جميع الحقول المطلوبة")

        with col2:
            # عرض المعاملات الحديثة
            st.markdown("**📋 المعاملات الحديثة**")

            if st.session_state.cash_transactions:
                # تصفية المعاملات حسب الفرع
                branch_transactions = [t for t in st.session_state.cash_transactions
                                     if t.get('branch_id') == current_branch_id]

                if branch_transactions:
                    # ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
                    branch_transactions.sort(key=lambda x: x['timestamp'], reverse=True)

                    for transaction in branch_transactions[:10]:  # عرض آخر 10 معاملات
                        transaction_color = "#2ecc71" if transaction['type'] in ['income', 'invoice_payment'] else "#e74c3c"
                        transaction_icon = "💰" if transaction['type'] in ['income', 'invoice_payment'] else "💸"
                        transaction_sign = "+" if transaction['type'] in ['income', 'invoice_payment'] else "-"

                        st.markdown(f"""
                        <div style="background: white; border-left: 4px solid {transaction_color};
                                    padding: 1rem; margin: 0.5rem 0; border-radius: 8px;
                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>{transaction_icon} {transaction['description']}</strong><br>
                                    <small style="color: #666;">📅 {transaction['date']} ⏰ {transaction['time']} 👤 {transaction['user']}</small>
                                </div>
                                <div style="text-align: right;">
                                    <h3 style="margin: 0; color: {transaction_color};">
                                        {transaction_sign}{transaction['amount']:.2f} ج.م
                                    </h3>
                                </div>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)
                else:
                    st.info("لا توجد معاملات نقدية في هذا الفرع")
            else:
                st.info("لا توجد معاملات نقدية")

def dashboard_page():
    """لوحة القيادة المحسنة مع بيانات حقيقية"""
    lang = st.session_state.get('language', 'ar')

    # العنوان حسب اللغة
    title = get_text('dashboard', lang)
    st.title(f"📊 {title}")

    # تهيئة النقدية
    init_cash_management()

    current_branch_id = st.session_state.get('current_branch_id', 1)
    current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

    # حساب البيانات الحقيقية
    today = datetime.now().strftime('%Y-%m-%d')

    # حساب الفواتير والمبيعات
    all_invoices = st.session_state.get('invoices', [])
    branch_invoices = [inv for inv in all_invoices
                      if inv.get('branch_id') == current_branch_id and not inv.get('is_deleted', False)]

    today_invoices = [inv for inv in branch_invoices if inv.get('date') == today]
    total_sales_today = sum([inv['total_amount'] for inv in today_invoices])
    total_sales_all = sum([inv['total_amount'] for inv in branch_invoices])

    # حساب العملاء
    all_customers = st.session_state.get('customers', [])
    branch_customers = [c for c in all_customers
                       if c.get('branch_id') == current_branch_id and not c.get('is_deleted', False)]

    # حساب المواعيد
    all_appointments = st.session_state.get('appointments', [])
    today_appointments = [apt for apt in all_appointments if apt.get('date') == today]

    # حساب الرصيد النقدي
    cash_balance = get_cash_balance_by_branch(current_branch_id)

    # الشريط العلوي مع معلومات الفرع
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                color: white; padding: 2rem; border-radius: 15px; margin-bottom: 2rem;
                box-shadow: 0 8px 32px rgba(44, 62, 80, 0.3);">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h2 style="margin: 0; color: white;">📊 لوحة التحكم - {current_branch_name}</h2>
                <p style="margin: 0; opacity: 0.9;">نظرة شاملة على أداء الفرع</p>
            </div>
            <div style="text-align: center;">
                <h3 style="margin: 0; color: white;">📅 {datetime.now().strftime('%Y-%m-%d')}</h3>
                <p style="margin: 0; opacity: 0.9;">⏰ {datetime.now().strftime('%H:%M')}</p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # مؤشرات الأداء الرئيسية مع بيانات حقيقية
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                    color: white; padding: 1.5rem; border-radius: 15px; text-align: center;
                    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);">
            <h3 style="margin: 0; font-size: 1rem;">📅 مواعيد اليوم</h3>
            <h1 style="margin: 0.5rem 0 0 0; font-size: 2.5rem;">{len(today_appointments)}</h1>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
                    color: white; padding: 1.5rem; border-radius: 15px; text-align: center;
                    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);">
            <h3 style="margin: 0; font-size: 1rem;">💰 مبيعات اليوم</h3>
            <h1 style="margin: 0.5rem 0 0 0; font-size: 2rem;">{total_sales_today:.0f} ج.م</h1>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                    color: white; padding: 1.5rem; border-radius: 15px; text-align: center;
                    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);">
            <h3 style="margin: 0; font-size: 1rem;">👥 إجمالي العملاء</h3>
            <h1 style="margin: 0.5rem 0 0 0; font-size: 2.5rem;">{len(branch_customers)}</h1>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                    color: white; padding: 1.5rem; border-radius: 15px; text-align: center;
                    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);">
            <h3 style="margin: 0; font-size: 1rem;">💵 الرصيد النقدي</h3>
            <h1 style="margin: 0.5rem 0 0 0; font-size: 2rem;">{cash_balance:.0f} ج.م</h1>
        </div>
        """, unsafe_allow_html=True)
    
    st.divider()

    # الرسوم البيانية مع بيانات حقيقية
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📈 الإيرادات الأسبوعية")
        if PLOTLY_AVAILABLE and branch_invoices:
            # حساب الإيرادات لآخر 7 أيام
            from datetime import timedelta

            daily_revenue = {}
            for i in range(7):
                date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
                day_invoices = [inv for inv in branch_invoices if inv.get('date') == date]
                daily_revenue[date] = sum([inv['total_amount'] for inv in day_invoices])

            dates = list(daily_revenue.keys())
            revenues = list(daily_revenue.values())

            fig = px.line(
                x=dates,
                y=revenues,
                title="الإيرادات اليومية لآخر 7 أيام",
                labels={'x': 'التاريخ', 'y': 'الإيرادات (ج.م)'}
            )
            fig.update_traces(line_color='#3498db', line_width=3)
            fig.update_layout(
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)',
                font=dict(family="Cairo", size=12)
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            # عرض بيانات افتراضية
            revenue_data = {
                'اليوم': ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                'الإيرادات': [total_sales_today if i == 6 else 0 for i in range(7)]
            }
            st.line_chart(pd.DataFrame(revenue_data).set_index('اليوم'))

    with col2:
        st.subheader("🔧 الخدمات الأكثر شعبية")
        if PLOTLY_AVAILABLE and branch_invoices:
            # حساب الخدمات الأكثر استخداماً
            service_counts = {}
            for invoice in branch_invoices:
                if invoice.get('services'):
                    for service_name, service_data in invoice['services'].items():
                        if service_name not in service_counts:
                            service_counts[service_name] = 0
                        service_counts[service_name] += service_data['quantity']

            if service_counts:
                # أخذ أفضل 5 خدمات
                top_services = sorted(service_counts.items(), key=lambda x: x[1], reverse=True)[:5]
                services = [item[0] for item in top_services]
                counts = [item[1] for item in top_services]

                fig = px.pie(
                    values=counts,
                    names=services,
                    title="توزيع الخدمات الأكثر طلباً",
                    color_discrete_sequence=px.colors.qualitative.Set3
                )
                fig.update_layout(
                    plot_bgcolor='rgba(0,0,0,0)',
                    paper_bgcolor='rgba(0,0,0,0)',
                    font=dict(family="Cairo", size=12)
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("لا توجد بيانات خدمات لعرضها")
        else:
            # عرض بيانات افتراضية
            services_data = {
                'الخدمة': ['قص شعر', 'صبغة', 'تسريحة', 'مكياج', 'عناية بالبشرة'],
                'العدد': [1, 0, 0, 0, 0] if len(today_invoices) > 0 else [0, 0, 0, 0, 0]
            }
            st.bar_chart(pd.DataFrame(services_data).set_index('الخدمة'))

    st.divider()

    # إحصائيات إضافية
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📊 إحصائيات المبيعات")
        if branch_invoices:
            avg_invoice = total_sales_all / len(branch_invoices)
            st.metric("متوسط الفاتورة", f"{avg_invoice:.2f} ج.م")
            st.metric("إجمالي الفواتير", len(branch_invoices))
            st.metric("فواتير اليوم", len(today_invoices))
        else:
            st.info("لا توجد فواتير لعرض الإحصائيات")

    with col2:
        st.subheader("💰 التدفق النقدي")
        if st.session_state.cash_transactions:
            branch_transactions = [t for t in st.session_state.cash_transactions
                                 if t.get('branch_id') == current_branch_id]

            today_income = sum([t['amount'] for t in branch_transactions
                              if t['date'] == today and t['type'] in ['income', 'invoice_payment']])
            today_expenses = sum([t['amount'] for t in branch_transactions
                                if t['date'] == today and t['type'] == 'expense'])

            st.metric("إيرادات اليوم", f"{today_income:.2f} ج.م")
            st.metric("مصروفات اليوم", f"{today_expenses:.2f} ج.م")
            st.metric("صافي التدفق", f"{today_income - today_expenses:.2f} ج.م")
        else:
            st.info("لا توجد معاملات نقدية")

    with col3:
        st.subheader("👥 إحصائيات العملاء")
        if branch_customers:
            # حساب العملاء الجدد هذا الشهر (افتراضي)
            new_customers_month = len([c for c in branch_customers if c.get('created_this_month', False)])

            st.metric("إجمالي العملاء", len(branch_customers))
            st.metric("عملاء جدد (الشهر)", new_customers_month)

            # حساب المناطق
            zones = set([c.get('zone_id', 1) for c in branch_customers])
            st.metric("المناطق المغطاة", len(zones))
        else:
            st.info("لا توجد بيانات عملاء")

    # أزرار سريعة للعمليات الشائعة
    st.divider()
    st.subheader("⚡ عمليات سريعة")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🧾 فاتورة جديدة", use_container_width=True, type="primary"):
            st.session_state.show_new_invoice = True
            st.rerun()

    with col2:
        if st.button("👤 عميل جديد", use_container_width=True):
            st.session_state.show_add_customer = True
            st.rerun()

    with col3:
        if st.button("📅 موعد جديد", use_container_width=True):
            st.session_state.show_add_appointment = True
            st.rerun()

    with col4:
        if st.button("💰 معاملة نقدية", use_container_width=True):
            st.session_state.show_cash_transaction = True
            st.rerun()

def branches_page():
    """صفحة إدارة الفروع"""
    st.title("🏢 إدارة الفروع")
    
    # أزرار العمليات
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("إضافة فرع جديد", use_container_width=True):
            st.session_state.show_add_branch = True
    
    with col2:
        if st.button("تحديث القائمة", use_container_width=True):
            st.rerun()
    
    # نموذج إضافة فرع
    if st.session_state.get('show_add_branch', False):
        with st.expander("إضافة فرع جديد", expanded=True):
            with st.form("add_branch_form"):
                name = st.text_input("اسم الفرع")
                address = st.text_area("العنوان")
                phone = st.text_input("رقم الهاتف")
                working_hours = st.text_input("ساعات العمل")
                
                col1, col2 = st.columns(2)
                with col1:
                    submit = st.form_submit_button("إضافة", use_container_width=True)
                with col2:
                    cancel = st.form_submit_button("إلغاء", use_container_width=True)
                
                if submit and name:
                    branch_data = {
                        "name": name,
                        "address": address,
                        "phone": phone,
                        "working_hours": working_hours
                    }
                    
                    response = make_api_request("/branches", method="POST", data=branch_data)
                    if response:
                        st.success("تم إضافة الفرع بنجاح!")
                        st.session_state.show_add_branch = False
                        st.rerun()
                    else:
                        st.error("حدث خطأ أثناء إضافة الفرع")
                
                if cancel:
                    st.session_state.show_add_branch = False
                    st.rerun()
    
    # عرض قائمة الفروع
    st.subheader("قائمة الفروع")
    branches = make_api_request("/branches")

    if branches:
        for branch in branches:
            with st.container():
                col1, col2, col3, col4 = st.columns([2, 2, 1, 1])

                with col1:
                    st.write(f"**{branch['name']}**")
                    st.write(f"📍 {branch.get('address', 'لا يوجد عنوان')}")

                with col2:
                    st.write(f"📞 {branch.get('phone', 'لا يوجد هاتف')}")
                    status_color = "🟢" if branch.get('is_active', True) else "🔴"
                    status_text = "نشط" if branch.get('is_active', True) else "غير نشط"
                    st.write(f"{status_color} {status_text}")

                with col3:
                    if st.button("تعديل", key=f"edit_{branch['branch_id']}", use_container_width=True):
                        st.session_state.edit_branch_id = branch['branch_id']
                        st.session_state.show_edit_branch = True
                        st.rerun()

                with col4:
                    if st.button("حذف", key=f"delete_{branch['branch_id']}", use_container_width=True):
                        # تأكيد الحذف
                        response = make_api_request(f"/branches/{branch['branch_id']}", method="DELETE")
                        if response:
                            st.success("تم حذف الفرع بنجاح!")
                            st.rerun()
                        else:
                            st.error("حدث خطأ في حذف الفرع")

                st.divider()
    else:
        st.info("لا توجد فروع مسجلة")

def categories_page():
    """صفحة إدارة الفئات والأصناف مع 3 مستويات أسعار"""
    st.title("📦 إدارة الفئات والأصناف")

    # الشريط العلوي مع تصميم احترافي
    st.markdown("""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; padding: 1.5rem; border-radius: 15px; margin: 1rem 0;">
        <h2 style="margin: 0; text-align: center;">📦 نظام إدارة الفئات والأصناف المتقدم</h2>
        <p style="margin: 0.5rem 0 0 0; text-align: center; opacity: 0.9;">
            إدارة شاملة للفئات والأصناف مع 3 مستويات أسعار (عادي - مميز - VIP)
        </p>
    </div>
    """, unsafe_allow_html=True)

    # تبويبات النظام
    tab1, tab2, tab3, tab4 = st.tabs(["📂 إدارة الفئات", "📦 إدارة الأصناف", "💰 مستويات الأسعار", "📊 التقارير"])

    with tab1:
        st.subheader("📂 إدارة فئات المنتجات والخدمات")

        # أزرار العمليات
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("➕ إضافة فئة جديدة", use_container_width=True):
                st.session_state.show_add_category = True

        with col2:
            if st.button("🔄 تحديث القائمة", use_container_width=True):
                st.rerun()

        # نموذج إضافة فئة جديدة
        if st.session_state.get('show_add_category', False):
            with st.expander("➕ إضافة فئة جديدة", expanded=True):
                with st.form("add_category_form"):
                    col1, col2 = st.columns(2)

                    with col1:
                        category_name = st.text_input("اسم الفئة *", placeholder="مثال: منتجات العناية بالشعر")
                        category_type = st.selectbox("نوع الفئة", ["خدمات", "منتجات"])
                        category_description = st.text_area("وصف الفئة", placeholder="وصف مختصر للفئة...")

                    with col2:
                        category_color = st.color_picker("لون الفئة", "#667eea")
                        category_icon = st.selectbox("أيقونة الفئة",
                                                   ["💇‍♀️", "💆‍♀️", "💅", "🧴", "🧼", "💄", "🎨", "✨", "🌟", "💎"])
                        is_active = st.checkbox("فئة نشطة", value=True)

                    if st.form_submit_button("💾 حفظ الفئة", use_container_width=True):
                        if category_name:
                            # حفظ مباشرة في جدول items
                            try:
                                conn = get_db_connection()
                                if conn:
                                    cursor = conn.cursor()

                                    # توليد كود تسلسلي للصنف
                                    if category_type == "خدمة":
                                        item_code = get_next_sequence_code('service_code')
                                        item_type = 'service'
                                    else:
                                        item_code = get_next_sequence_code('product_code')
                                        item_type = 'product'

                                    if not item_code:
                                        # كود افتراضي في حالة الفشل
                                        item_code = f"{'S' if item_type == 'service' else 'P'}{datetime.now().strftime('%m%d%H%M')}"

                                    cursor.execute("""
                                        INSERT INTO items (
                                            item_code, item_name, item_type, description,
                                            price_tier_1, price_tier_2, price_tier_3,
                                            duration_minutes, commission_rate, is_active, created_by
                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    """, (
                                        item_code, category_name, item_type, category_description,
                                        0.0, 0.0, 0.0,  # أسعار افتراضية
                                        30 if item_type == 'service' else None,  # مدة الخدمة
                                        15.0 if item_type == 'service' else None,  # عمولة الخدمة
                                        is_active, st.session_state.user['username']
                                    ))

                                    conn.commit()
                                    conn.close()

                                    st.success(f"✅ تم حفظ الصنف '{category_name}' برقم {item_code} في قاعدة البيانات بنجاح!")
                                    st.session_state.show_add_category = False
                                    st.rerun()
                                else:
                                    st.error("❌ خطأ في الاتصال بقاعدة البيانات")
                            except Exception as e:
                                st.error(f"❌ خطأ في حفظ الفئة: {str(e)}")
                        else:
                            st.error("⚠️ يرجى إدخال اسم الفئة")

        # عرض الفئات الموجودة من قاعدة البيانات
        categories_from_db = get_categories_by_branch(st.session_state.get('current_branch_id', 1))

        if categories_from_db:
            st.markdown("### 📂 الفئات المتاحة")

            for i, category in enumerate(categories_from_db):
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                with col1:
                    status = "🟢 نشط" if category.get('is_active', True) else "🔴 غير نشط"
                    st.markdown(f"""
                    <div style="background: {category.get('color', '#667eea')}20;
                                border-left: 4px solid {category.get('color', '#667eea')};
                                padding: 1rem; border-radius: 8px; margin: 0.5rem 0;">
                        <h4 style="margin: 0; color: {category.get('color', '#667eea')};">
                            {category.get('icon', '📦')} {category['name']}
                        </h4>
                        <p style="margin: 0.5rem 0 0 0; color: #666; font-size: 0.9rem;">
                            {category.get('type', 'غير محدد')} | {status}
                        </p>
                        {f"<p style='margin: 0.5rem 0 0 0; color: #888; font-size: 0.8rem;'>{category.get('description', '')}</p>" if category.get('description') else ""}
                    </div>
                    """, unsafe_allow_html=True)

                with col2:
                    if st.button("✏️ تعديل", key=f"edit_cat_{i}", use_container_width=True):
                        st.info("وظيفة التعديل قيد التطوير")

                with col3:
                    active_text = "إلغاء تفعيل" if category.get('is_active', True) else "تفعيل"
                    if st.button(f"🔄 {active_text}", key=f"toggle_cat_{i}", use_container_width=True):
                        st.session_state.categories[i]['is_active'] = not category.get('is_active', True)
                        st.rerun()

                with col4:
                    if st.button("🗑️ حذف", key=f"delete_cat_{i}", use_container_width=True, type="secondary"):
                        st.session_state.categories.pop(i)
                        st.success("تم حذف الفئة بنجاح!")
                        st.rerun()
        else:
            st.info("📂 لا توجد فئات مضافة بعد - ابدأ بإضافة فئة جديدة")

    with tab2:
        st.subheader("📦 إدارة الأصناف والمنتجات")

        # أزرار العمليات
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("➕ إضافة صنف جديد", use_container_width=True):
                st.session_state.show_add_item = True

        with col2:
            if st.button("🔄 تحديث الأصناف", use_container_width=True):
                st.rerun()

        # نموذج إضافة صنف جديد
        if st.session_state.get('show_add_item', False):
            with st.expander("➕ إضافة صنف جديد", expanded=True):
                with st.form("add_item_form"):
                    col1, col2 = st.columns(2)

                    with col1:
                        item_name = st.text_input("اسم الصنف *", placeholder="مثال: شامبو للشعر الجاف")
                        item_code = st.text_input("كود الصنف *", placeholder="مثال: SH001")

                        # اختيار الفئة
                        categories_list = ["بدون فئة"]
                        if 'categories' in st.session_state and st.session_state.categories:
                            categories_list.extend([cat['name'] for cat in st.session_state.categories if cat.get('is_active', True)])

                        selected_category = st.selectbox("الفئة", categories_list)
                        item_type = st.selectbox("نوع الصنف", ["منتج", "خدمة"])
                        item_description = st.text_area("وصف الصنف", placeholder="وصف مفصل للصنف...")

                    with col2:
                        st.markdown("### 💰 مستويات الأسعار")

                        # 3 مستويات أسعار
                        price_normal = st.number_input("السعر العادي (ج.م) *", min_value=0.0, value=None, placeholder="أدخل السعر")
                        price_premium = st.number_input("السعر المميز (ج.م)", min_value=0.0, value=None, placeholder="أدخل السعر")
                        price_vip = st.number_input("سعر VIP (ج.م)", min_value=0.0, value=None, placeholder="أدخل السعر")

                        # معلومات إضافية
                        if item_type == "منتج":
                            stock_quantity = st.number_input("الكمية في المخزون", min_value=0, value=0)
                            min_stock = st.number_input("الحد الأدنى للمخزون", min_value=0, value=5)
                        else:
                            commission_rate = st.number_input("نسبة العمولة (%)", min_value=0.0, max_value=100.0, value=10.0)

                        is_active = st.checkbox("صنف نشط", value=True)

                    if st.form_submit_button("💾 حفظ الصنف", use_container_width=True):
                        if item_name and item_code and price_normal:
                            # حفظ مباشرة في قاعدة البيانات
                            try:
                                conn = get_db_connection()
                                if conn:
                                    cursor = conn.cursor()

                                    # حساب الأسعار المميزة إذا لم تكن محددة
                                    final_price_premium = price_premium if price_premium else price_normal * 1.2
                                    final_price_vip = price_vip if price_vip else price_normal * 1.5

                                    # إدراج البيانات الأساسية
                                    cursor.execute("""
                                        INSERT INTO items (
                                            name, code, category, type, description,
                                            price_normal, price_premium, price_vip,
                                            stock_quantity, min_stock, commission_rate,
                                            is_active, branch_id
                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    """, (
                                        item_name, item_code, selected_category, item_type, item_description,
                                        price_normal, final_price_premium, final_price_vip,
                                        stock_quantity if item_type == "منتج" else 0,
                                        min_stock if item_type == "منتج" else 5,
                                        commission_rate if item_type == "خدمة" else 0,
                                        is_active, st.session_state.get('current_branch_id', 1)
                                    ))

                                    conn.commit()
                                    conn.close()

                                    st.success(f"✅ تم حفظ الصنف '{item_name}' في قاعدة البيانات بنجاح!")
                                    st.session_state.show_add_item = False
                                    st.rerun()
                                else:
                                    st.error("❌ خطأ في الاتصال بقاعدة البيانات")
                            except Exception as e:
                                st.error(f"❌ خطأ في حفظ الصنف: {str(e)}")
                        else:
                            st.error("⚠️ يرجى إدخال اسم الصنف والكود والسعر العادي")

        # عرض الأصناف الموجودة من قاعدة البيانات
        items_from_db = get_items_by_branch(st.session_state.get('current_branch_id', 1))

        if items_from_db:
            st.markdown("### 📦 الأصناف المتاحة")

            # فلترة وبحث
            col1, col2, col3 = st.columns(3)
            with col1:
                search_term = st.text_input("🔍 البحث", placeholder="اسم أو كود الصنف")
            with col2:
                filter_category = st.selectbox("فلترة حسب الفئة", ["الكل"] + categories_list[1:])
            with col3:
                filter_type = st.selectbox("فلترة حسب النوع", ["الكل", "منتج", "خدمة"])

            # تطبيق الفلاتر
            filtered_items = items_from_db

            if search_term:
                filtered_items = [item for item in filtered_items
                                if search_term.lower() in item['name'].lower()
                                or search_term.lower() in item['code'].lower()]

            if filter_category != "الكل":
                filtered_items = [item for item in filtered_items if item['category'] == filter_category]

            if filter_type != "الكل":
                filtered_items = [item for item in filtered_items if item['type'] == filter_type]

            # عرض النتائج
            for i, item in enumerate(filtered_items):
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 2, 2, 1])

                    with col1:
                        status = "🟢 نشط" if item.get('is_active', True) else "🔴 غير نشط"
                        type_icon = "📦" if item.get('type') == "منتج" else "🔧"

                        st.markdown(f"""
                        <div style="background: #f8f9fa; border: 1px solid #dee2e6;
                                    border-radius: 8px; padding: 1rem; margin: 0.5rem 0;">
                            <h4 style="margin: 0; color: #495057;">
                                {type_icon} {item.get('name', 'غير محدد')}
                            </h4>
                            <p style="margin: 0.5rem 0; color: #6c757d; font-size: 0.9rem;">
                                كود: {item.get('code', 'غير محدد')} | فئة: {item.get('category', 'بدون فئة')} | {status}
                            </p>
                            {f"<p style='margin: 0.5rem 0 0 0; color: #868e96; font-size: 0.8rem;'>{item.get('description', '')}</p>" if item.get('description') else ""}
                        </div>
                        """, unsafe_allow_html=True)

                    with col2:
                        st.markdown("**💰 الأسعار:**")
                        st.write(f"عادي: {item.get('price_normal', 0):.2f} ج.م")
                        st.write(f"مميز: {item.get('price_premium', 0):.2f} ج.م")
                        st.write(f"VIP: {item.get('price_vip', 0):.2f} ج.م")

                    with col3:
                        if item.get('type') == "منتج":
                            st.markdown("**📊 المخزون:**")
                            stock_color = "🟢" if item.get('stock_quantity', 0) > item.get('min_stock', 5) else "🔴"
                            st.write(f"{stock_color} الكمية: {item.get('stock_quantity', 0)}")
                            st.write(f"الحد الأدنى: {item.get('min_stock', 5)}")
                        else:
                            st.markdown("**💼 العمولة:**")
                            st.write(f"النسبة: {item.get('commission_rate', 10):.1f}%")

                    with col4:
                        if st.button("✏️", key=f"edit_item_{i}", help="تعديل"):
                            st.info("وظيفة التعديل قيد التطوير")

                        if st.button("🗑️", key=f"delete_item_{i}", help="حذف", type="secondary"):
                            # حذف من قاعدة البيانات
                            try:
                                conn = get_db_connection()
                                if conn:
                                    cursor = conn.cursor()
                                    cursor.execute("UPDATE items SET is_deleted = 1 WHERE item_id = ?", (item.get('item_id'),))
                                    conn.commit()
                                    conn.close()
                                    st.success("تم حذف الصنف من قاعدة البيانات بنجاح!")
                                    st.rerun()
                            except Exception as e:
                                st.error(f"خطأ في حذف الصنف: {str(e)}")

                st.divider()
        else:
            st.info("📦 لا توجد أصناف مضافة بعد - ابدأ بإضافة صنف جديد")

    with tab3:
        st.subheader("💰 إدارة مستويات الأسعار")

        st.markdown("""
        <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
            <h4 style="margin: 0; color: #1976d2;">📋 مستويات الأسعار المتاحة</h4>
            <p style="margin: 0.5rem 0 0 0; color: #424242;">
                يمكنك تحديد 3 مستويات أسعار مختلفة لكل صنف حسب فئة العملاء
            </p>
        </div>
        """, unsafe_allow_html=True)

        # عرض مستويات الأسعار
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("""
            <div style="background: #f1f8e9; border: 2px solid #8bc34a; border-radius: 12px; padding: 1.5rem; text-align: center;">
                <h3 style="margin: 0; color: #689f38;">🟢 السعر العادي</h3>
                <p style="margin: 0.5rem 0; color: #558b2f;">للعملاء العاديين</p>
                <ul style="text-align: right; color: #689f38; margin: 1rem 0;">
                    <li>السعر الأساسي للصنف</li>
                    <li>متاح لجميع العملاء</li>
                    <li>الأكثر استخداماً</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div style="background: #fff3e0; border: 2px solid #ff9800; border-radius: 12px; padding: 1.5rem; text-align: center;">
                <h3 style="margin: 0; color: #f57c00;">🟡 السعر المميز</h3>
                <p style="margin: 0.5rem 0; color: #ef6c00;">للعملاء المميزين</p>
                <ul style="text-align: right; color: #f57c00; margin: 1rem 0;">
                    <li>سعر أعلى بـ 20% عادة</li>
                    <li>للعملاء المميزين</li>
                    <li>خدمة أفضل</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown("""
            <div style="background: #fce4ec; border: 2px solid #e91e63; border-radius: 12px; padding: 1.5rem; text-align: center;">
                <h3 style="margin: 0; color: #c2185b;">🔴 سعر VIP</h3>
                <p style="margin: 0.5rem 0; color: #ad1457;">للعملاء المتميزين</p>
                <ul style="text-align: right; color: #c2185b; margin: 1rem 0;">
                    <li>سعر أعلى بـ 50% عادة</li>
                    <li>للعملاء VIP</li>
                    <li>خدمة حصرية</li>
                </ul>
            </div>
            """, unsafe_allow_html=True)

        # إعدادات مستويات الأسعار
        st.markdown("### ⚙️ إعدادات مستويات الأسعار")

        with st.form("price_levels_settings"):
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**نسب الزيادة الافتراضية:**")
                premium_increase = st.number_input("زيادة السعر المميز (%)", min_value=0.0, max_value=100.0, value=20.0)
                vip_increase = st.number_input("زيادة سعر VIP (%)", min_value=0.0, max_value=100.0, value=50.0)

            with col2:
                st.markdown("**إعدادات العرض:**")
                show_all_prices = st.checkbox("عرض جميع الأسعار في الفاتورة", value=False)
                auto_calculate = st.checkbox("حساب تلقائي للأسعار المميزة", value=True)

            if st.form_submit_button("💾 حفظ الإعدادات", use_container_width=True):
                # حفظ الإعدادات
                price_settings = {
                    'premium_increase': premium_increase,
                    'vip_increase': vip_increase,
                    'show_all_prices': show_all_prices,
                    'auto_calculate': auto_calculate
                }
                st.session_state.price_settings = price_settings
                st.success("✅ تم حفظ إعدادات الأسعار بنجاح!")

        # عرض الأصناف مع أسعارها
        if 'items' in st.session_state and st.session_state.items:
            st.markdown("### 📊 مقارنة الأسعار")

            # جدول مقارنة الأسعار
            price_data = []
            for item in st.session_state.items:
                price_data.append({
                    'الصنف': item['name'],
                    'الكود': item['code'],
                    'النوع': item['type'],
                    'السعر العادي': f"{item['prices']['normal']:.2f} ج.م",
                    'السعر المميز': f"{item['prices']['premium']:.2f} ج.م",
                    'سعر VIP': f"{item['prices']['vip']:.2f} ج.م"
                })

            if price_data:
                df = pd.DataFrame(price_data)
                st.dataframe(df, use_container_width=True)

    with tab4:
        st.subheader("📊 تقارير الفئات والأصناف")

        # إحصائيات عامة
        col1, col2, col3, col4 = st.columns(4)

        categories_count = len(st.session_state.get('categories', []))
        items_count = len(st.session_state.get('items', []))
        services_count = len([item for item in st.session_state.get('items', []) if item['type'] == 'خدمة'])
        products_count = len([item for item in st.session_state.get('items', []) if item['type'] == 'منتج'])

        with col1:
            st.metric("إجمالي الفئات", categories_count)
        with col2:
            st.metric("إجمالي الأصناف", items_count)
        with col3:
            st.metric("الخدمات", services_count)
        with col4:
            st.metric("المنتجات", products_count)

        st.divider()

        # تقارير تفصيلية
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 📂 توزيع الأصناف حسب الفئات")
            if 'items' in st.session_state and st.session_state.items:
                category_distribution = {}
                for item in st.session_state.items:
                    category = item.get('category', 'بدون فئة')
                    category_distribution[category] = category_distribution.get(category, 0) + 1

                for category, count in category_distribution.items():
                    st.write(f"• {category}: {count} صنف")
            else:
                st.info("لا توجد أصناف لعرض التقرير")

        with col2:
            st.markdown("### 💰 متوسط الأسعار")
            if 'items' in st.session_state and st.session_state.items:
                total_normal = sum([item['prices']['normal'] for item in st.session_state.items])
                total_premium = sum([item['prices']['premium'] for item in st.session_state.items])
                total_vip = sum([item['prices']['vip'] for item in st.session_state.items])

                avg_normal = total_normal / len(st.session_state.items)
                avg_premium = total_premium / len(st.session_state.items)
                avg_vip = total_vip / len(st.session_state.items)

                st.write(f"• متوسط السعر العادي: {avg_normal:.2f} ج.م")
                st.write(f"• متوسط السعر المميز: {avg_premium:.2f} ج.م")
                st.write(f"• متوسط سعر VIP: {avg_vip:.2f} ج.م")
            else:
                st.info("لا توجد أصناف لحساب المتوسط")

        # تصدير البيانات
        st.markdown("### 📤 تصدير البيانات")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("📊 تصدير تقرير الفئات", use_container_width=True):
                st.info("وظيفة التصدير قيد التطوير")

        with col2:
            if st.button("📋 تصدير قائمة الأصناف", use_container_width=True):
                st.info("وظيفة التصدير قيد التطوير")

def customers_page():
    """صفحة إدارة العملاء"""
    st.title("👥 إدارة العملاء")

    # أزرار العمليات
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("إضافة عميل جديد", use_container_width=True):
            st.session_state.show_add_customer = True

    with col2:
        if st.button("تحديث القائمة", use_container_width=True):
            st.rerun()

    # نموذج إضافة عميل
    if st.session_state.get('show_add_customer', False):
        with st.expander("إضافة عميل جديد", expanded=True):
            with st.form("add_customer_form"):
                col1, col2 = st.columns(2)

                with col1:
                    first_name = st.text_input("الاسم الأول *")
                    last_name = st.text_input("اسم العائلة")
                    phone = st.text_input("رقم الهاتف *", placeholder="01xxxxxxxxx")
                    email = st.text_input("البريد الإلكتروني")

                with col2:
                    birth_date = st.date_input("تاريخ الميلاد", value=None)

                    # عرض الفرع الحالي
                    current_branch = st.session_state.get('current_branch_name', 'غير محدد')
                    st.info(f"🏢 سيتم تسجيل العميل في: **{current_branch}**")

                    zone_options = {
                        "مدينة نصر": 1,
                        "جسر السويس": 2,
                        "الزهراء": 3,
                        "مناطق أخرى": 4
                    }
                    selected_zone = st.selectbox("المنطقة", options=list(zone_options.keys()))
                    zone_id = zone_options[selected_zone]

                    # ملاحظة: zone_id سيتم استخدامه من المنطقة المختارة أعلاه

                notes = st.text_area("ملاحظات")

                col1, col2 = st.columns(2)
                with col1:
                    submit = st.form_submit_button("إضافة العميل", use_container_width=True)
                with col2:
                    cancel = st.form_submit_button("إلغاء", use_container_width=True)

                if submit and first_name and phone:
                    # إضافة branch_id للعميل
                    customer_data = {
                        "arabic_name": first_name,  # استخدام الاسم الأول كاسم عربي
                        "english_name": last_name,  # استخدام اسم العائلة كاسم إنجليزي
                        "phone": phone,
                        "email": email,
                        "birth_date": birth_date.isoformat() if birth_date else None,
                        "zone_id": zone_id,  # استخدام zone_id بدلاً من reference_source
                        "notes": notes,
                        "branch_id": st.session_state.get('current_branch_id', 1)  # ربط بالفرع الحالي
                    }

                    # حفظ مباشرة في قاعدة البيانات
                    if st.session_state.get('db_initialized', False):
                        try:
                            # حفظ في قاعدة البيانات أولاً
                            db_result = save_customer_to_db(customer_data)

                            if db_result:
                                # حفظ في session state كنسخة احتياطية
                                if 'customers' not in st.session_state:
                                    st.session_state.customers = []

                                new_customer = customer_data.copy()
                                new_customer['customer_id'] = len(st.session_state.customers) + 1
                                new_customer['full_name'] = f"{first_name} {last_name}".strip()
                                new_customer['branch_name'] = current_branch
                                new_customer['db_saved'] = True

                                st.session_state.customers.append(new_customer)
                                st.success(f"✅ تم حفظ العميل {new_customer['full_name']} في قاعدة البيانات بنجاح!")
                                st.session_state.show_add_customer = False
                                st.rerun()
                            else:
                                st.error("❌ فشل في حفظ العميل في قاعدة البيانات")

                        except Exception as e:
                            st.error(f"❌ خطأ في حفظ العميل: {str(e)}")
                            # حفظ محلي كنسخة احتياطية
                            if 'customers' not in st.session_state:
                                st.session_state.customers = []

                            new_customer = customer_data.copy()
                            new_customer['customer_id'] = len(st.session_state.customers) + 1
                            new_customer['full_name'] = f"{first_name} {last_name}".strip()
                            new_customer['branch_name'] = current_branch
                            new_customer['db_saved'] = False

                            st.session_state.customers.append(new_customer)
                            st.warning(f"⚠️ تم حفظ العميل {new_customer['full_name']} محلياً فقط")
                            st.session_state.show_add_customer = False
                            st.rerun()
                    else:
                        st.error("❌ قاعدة البيانات غير متصلة - لا يمكن حفظ العميل")

                if cancel:
                    st.session_state.show_add_customer = False
                    st.rerun()

    # عرض قائمة العملاء حسب الفرع الحالي
    current_branch_id = st.session_state.get('current_branch_id', 1)
    current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

    st.subheader(f"👥 عملاء {current_branch_name}")

    # جلب العملاء من قاعدة البيانات أولاً، ثم من session state كنسخة احتياطية
    branch_customers = []

    if st.session_state.get('db_initialized', False):
        try:
            # جلب من قاعدة البيانات
            db_customers = get_customers_by_branch(current_branch_id)
            if db_customers:
                # تحويل بيانات قاعدة البيانات إلى التنسيق المطلوب
                for customer in db_customers:
                    branch_customers.append({
                        'customer_id': customer['CustomerId'],
                        'full_name': customer['FirstName'] + ' ' + (customer.get('LastName') or ''),
                        'first_name': customer['FirstName'],
                        'last_name': customer.get('LastName', ''),
                        'phone': customer['PhoneNumber'],
                        'email': customer.get('Email', ''),
                        'birth_date': customer.get('DateOfBirth'),
                        'zone_id': customer.get('ReferredByZoneId', 1),
                        'notes': customer.get('Notes', ''),
                        'branch_id': customer['branch_id'],
                        'branch_name': current_branch_name,
                        'created_at': customer.get('CreatedAt'),
                        'is_deleted': False,
                        'db_saved': True
                    })
        except Exception as e:
            st.warning(f"تعذر جلب العملاء من قاعدة البيانات: {str(e)}")

    # إضافة العملاء المحليين كنسخة احتياطية (إذا لم يتم جلبهم من قاعدة البيانات)
    if not branch_customers:
        all_customers = st.session_state.get('customers', [])
        branch_customers = [c for c in all_customers if c.get('branch_id') == current_branch_id and not c.get('is_deleted', False)]

    if branch_customers:
        # إحصائيات سريعة
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("إجمالي العملاء", len(branch_customers))
        with col2:
            new_customers_today = len([c for c in branch_customers if c.get('created_today', False)])
            st.metric("عملاء جدد اليوم", new_customers_today)
        with col3:
            zones = list(set([c.get('zone_id', 1) for c in branch_customers]))
            st.metric("المناطق المغطاة", len(zones))

        st.divider()

        # عرض العملاء
        for customer in branch_customers:
            with st.container():
                col1, col2, col3, col4 = st.columns([3, 2, 1, 1])

                with col1:
                    st.write(f"**{customer.get('full_name', 'بدون اسم')}**")
                    st.write(f"📞 {customer.get('phone', 'لا يوجد هاتف')}")

                with col2:
                    st.write(f"📧 {customer.get('email', 'لا يوجد إيميل')}")
                    zone_names = {1: "مدينة نصر", 2: "جسر السويس", 3: "الزهراء", 4: "مناطق أخرى"}
                    zone_name = zone_names.get(customer.get('zone_id', 1), "غير محدد")
                    st.write(f"📍 المنطقة: {zone_name}")
                    st.write(f"🏢 الفرع: {customer.get('branch_name', current_branch_name)}")

                with col3:
                    if st.button("تعديل", key=f"edit_customer_{customer['customer_id']}", use_container_width=True):
                        st.info("وظيفة التعديل قيد التطوير")

                with col4:
                    if st.button("حذف منطقي", key=f"delete_customer_{customer['customer_id']}", use_container_width=True):
                        # حذف منطقي - إضافة علامة الحذف
                        for i, c in enumerate(st.session_state.customers):
                            if c['customer_id'] == customer['customer_id']:
                                st.session_state.customers[i]['is_deleted'] = True
                                st.session_state.customers[i]['deleted_at'] = datetime.now().isoformat()
                                st.session_state.customers[i]['deleted_by'] = st.session_state.user['username']
                                break

                        st.success(f"تم حذف العميل {customer['full_name']} منطقياً!")
                        st.info("يمكن استعادة العميل من قسم الإعدادات")
                        st.rerun()

                st.divider()
    else:
        st.info(f"لا يوجد عملاء مسجلين في {current_branch_name}")
        st.write("💡 **نصيحة:** استخدم زر 'إضافة عميل جديد' لإضافة أول عميل")

    # عرض العملاء المحذوفين منطقياً (للمديرين فقط)
    user_permissions = st.session_state.user.get('permissions', [])
    if 'all' in user_permissions or 'branch_management' in user_permissions:
        all_customers = st.session_state.get('customers', [])
        deleted_customers = [c for c in all_customers if c.get('branch_id') == current_branch_id and c.get('is_deleted', False)]

        if deleted_customers:
            with st.expander(f"🗑️ العملاء المحذوفين منطقياً ({len(deleted_customers)})"):
                for customer in deleted_customers:
                    col1, col2, col3 = st.columns([3, 2, 1])

                    with col1:
                        st.write(f"**{customer.get('full_name', 'بدون اسم')}** ❌")
                        st.write(f"📞 {customer.get('phone', 'لا يوجد هاتف')}")

                    with col2:
                        st.write(f"🗑️ حُذف في: {customer.get('deleted_at', 'غير محدد')}")
                        st.write(f"👤 بواسطة: {customer.get('deleted_by', 'غير محدد')}")

                    with col3:
                        if st.button("استعادة", key=f"restore_customer_{customer['customer_id']}", use_container_width=True):
                            # استعادة العميل
                            for i, c in enumerate(st.session_state.customers):
                                if c['customer_id'] == customer['customer_id']:
                                    st.session_state.customers[i]['is_deleted'] = False
                                    st.session_state.customers[i]['deleted_at'] = None
                                    st.session_state.customers[i]['deleted_by'] = None
                                    break

                            st.success(f"تم استعادة العميل {customer['full_name']} بنجاح!")
                            st.rerun()

                    st.divider()

def appointments_page():
    """صفحة إدارة المواعيد"""
    st.title("📅 إدارة المواعيد")

    # أزرار العمليات
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("حجز موعد جديد", use_container_width=True):
            st.session_state.show_add_appointment = True

    with col2:
        if st.button("تحديث المواعيد", use_container_width=True):
            st.rerun()

    # نموذج حجز موعد جديد
    if st.session_state.get('show_add_appointment', False):
        with st.expander("حجز موعد جديد", expanded=True):
            with st.form("add_appointment_form"):
                # جلب العملاء والخدمات والموظفين
                customers = make_api_request("/customers")
                services_data = make_api_request("/services") if hasattr(st, 'services_endpoint') else []

                col1, col2 = st.columns(2)

                with col1:
                    if customers:
                        customer_options = {f"{c.get('full_name', 'بدون اسم')} - {c.get('phone', '')}": c['customer_id'] for c in customers}
                        selected_customer = st.selectbox("العميل", options=list(customer_options.keys()))
                        customer_id = customer_options[selected_customer] if selected_customer else None
                    else:
                        st.warning("لا يوجد عملاء مسجلين")
                        customer_id = None

                    appointment_date = st.date_input("تاريخ الموعد")
                    appointment_time = st.time_input("وقت الموعد")

                with col2:
                    # خدمات مؤقتة حتى يتم تطوير API الخدمات
                    service_options = [
                        "قص شعر بروفيشنال - 200 ج.م",
                        "صبغة شعر كاملة - 300 ج.م",
                        "بشرة لايت - 500 ج.م",
                        "باديكير يد وقدم - 500 ج.م",
                        "هيدرو فيشال - 2000 ج.م"
                    ]
                    selected_service = st.selectbox("الخدمة", options=service_options)

                    duration = st.number_input("مدة الخدمة (دقيقة)", min_value=15, max_value=300, value=60)
                    notes = st.text_area("ملاحظات")

                col1, col2 = st.columns(2)
                with col1:
                    submit = st.form_submit_button("حجز الموعد", use_container_width=True)
                with col2:
                    cancel = st.form_submit_button("إلغاء", use_container_width=True)

                if submit and customer_id and appointment_date and appointment_time:
                    # حفظ الموعد (مؤقتاً في session state)
                    if 'appointments' not in st.session_state:
                        st.session_state.appointments = []

                    new_appointment = {
                        'id': len(st.session_state.appointments) + 1,
                        'customer_id': customer_id,
                        'customer_name': selected_customer.split(' - ')[0],
                        'service': selected_service,
                        'date': appointment_date.strftime('%Y-%m-%d'),
                        'time': appointment_time.strftime('%H:%M'),
                        'duration': duration,
                        'status': 'مجدول',
                        'notes': notes
                    }

                    st.session_state.appointments.append(new_appointment)
                    st.success("تم حجز الموعد بنجاح!")
                    st.session_state.show_add_appointment = False
                    st.rerun()

                if cancel:
                    st.session_state.show_add_appointment = False
                    st.rerun()

    # عرض المواعيد
    st.subheader("📋 المواعيد المجدولة")

    if 'appointments' in st.session_state and st.session_state.appointments:
        for appointment in st.session_state.appointments:
            with st.container():
                col1, col2, col3, col4 = st.columns([2, 2, 1, 1])

                with col1:
                    st.write(f"**{appointment['customer_name']}**")
                    st.write(f"📅 {appointment['date']} - ⏰ {appointment['time']}")

                with col2:
                    st.write(f"✨ {appointment['service']}")
                    status_color = "🟢" if appointment['status'] == 'مجدول' else "🔴"
                    st.write(f"{status_color} {appointment['status']}")

                with col3:
                    if st.button("تعديل", key=f"edit_app_{appointment['id']}", use_container_width=True):
                        st.info("وظيفة التعديل قيد التطوير")

                with col4:
                    if st.button("إلغاء", key=f"cancel_app_{appointment['id']}", use_container_width=True):
                        st.session_state.appointments = [a for a in st.session_state.appointments if a['id'] != appointment['id']]
                        st.success("تم إلغاء الموعد")
                        st.rerun()

                st.divider()
    else:
        st.info("لا توجد مواعيد مجدولة")

def services_page():
    """صفحة إدارة الخدمات"""
    st.title("✨ إدارة الخدمات")

    # أزرار العمليات
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("إضافة خدمة جديدة", use_container_width=True):
            st.session_state.show_add_service = True

    with col2:
        if st.button("تحديث القائمة", use_container_width=True):
            st.rerun()

    # نموذج إضافة خدمة جديدة
    if st.session_state.get('show_add_service', False):
        with st.expander("إضافة خدمة جديدة", expanded=True):
            with st.form("add_service_form"):
                col1, col2 = st.columns(2)

                with col1:
                    name = st.text_input("اسم الخدمة *")
                    price = st.number_input("السعر (ج.م) *", min_value=0.0, step=10.0)
                    duration = st.number_input("المدة (دقيقة) *", min_value=15, max_value=300, value=60)

                with col2:
                    category = st.selectbox("الفئة", [
                        "خدمات الشعر والستايل",
                        "خدمات البشرة",
                        "الباديكير والمانيكير",
                        "العلاجات المتخصصة",
                        "خدمات أخرى"
                    ])
                    commission_rate = st.number_input("نسبة العمولة (%)", min_value=0.0, max_value=100.0, value=10.0)

                description = st.text_area("وصف الخدمة")

                col1, col2 = st.columns(2)
                with col1:
                    submit = st.form_submit_button("إضافة الخدمة", use_container_width=True)
                with col2:
                    cancel = st.form_submit_button("إلغاء", use_container_width=True)

                if submit and name and price and duration:
                    # حفظ الخدمة (مؤقتاً في session state)
                    if 'services' not in st.session_state:
                        st.session_state.services = []

                    new_service = {
                        'id': len(st.session_state.services) + 1,
                        'name': name,
                        'description': description,
                        'category': category,
                        'price': price,
                        'duration': duration,
                        'commission_rate': commission_rate,
                        'is_active': True
                    }

                    st.session_state.services.append(new_service)
                    st.success("تم إضافة الخدمة بنجاح!")
                    st.session_state.show_add_service = False
                    st.rerun()

                if cancel:
                    st.session_state.show_add_service = False
                    st.rerun()

    # عرض الخدمات الافتراضية المصرية
    st.subheader("🇪🇬 خدمات الصالون المصري")

    # الخدمات الافتراضية
    default_services = [
        {"name": "قص شعر بروفيشنال", "category": "خدمات الشعر", "price": 200, "duration": 45},
        {"name": "دقن ستايلنج", "category": "خدمات الشعر", "price": 150, "duration": 30},
        {"name": "صبغة شعر كاملة", "category": "خدمات الشعر", "price": 300, "duration": 120},
        {"name": "استشوار برو", "category": "خدمات الشعر", "price": 200, "duration": 60},
        {"name": "مكواة كيرلي بروفيشنال", "category": "خدمات الشعر", "price": 500, "duration": 90},
        {"name": "بشرة لايت", "category": "خدمات البشرة", "price": 500, "duration": 60},
        {"name": "هيدرو فيشال", "category": "خدمات البشرة", "price": 2000, "duration": 150},
        {"name": "أكسچنيو الفاخرة", "category": "خدمات البشرة", "price": 3000, "duration": 180},
        {"name": "باديكير يد وقدم", "category": "الباديكير", "price": 500, "duration": 90},
        {"name": "كولاجين للشعر", "category": "العلاجات", "price": 1500, "duration": 180},
        {"name": "بوتوكس للشعر", "category": "العلاجات", "price": 1800, "duration": 200}
    ]

    # دمج الخدمات الافتراضية مع المضافة
    all_services = default_services.copy()
    if 'services' in st.session_state:
        all_services.extend(st.session_state.services)

    # عرض الخدمات في جدول منظم
    if all_services:
        # تجميع الخدمات حسب الفئة
        categories = {}
        for service in all_services:
            cat = service.get('category', 'أخرى')
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(service)

        # عرض كل فئة
        for category, services in categories.items():
            st.subheader(f"📋 {category}")

            for service in services:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 2, 1, 1])

                    with col1:
                        st.write(f"**{service['name']}**")
                        if service.get('description'):
                            st.write(f"📝 {service['description']}")

                    with col2:
                        st.write(f"💰 {service['price']} ج.م")
                        st.write(f"⏱️ {service['duration']} دقيقة")

                    with col3:
                        if service.get('id'):  # خدمة مضافة من المستخدم
                            if st.button("تعديل", key=f"edit_service_{service['id']}", use_container_width=True):
                                st.info("وظيفة التعديل قيد التطوير")

                    with col4:
                        if service.get('id'):  # خدمة مضافة من المستخدم
                            if st.button("حذف", key=f"delete_service_{service['id']}", use_container_width=True):
                                st.session_state.services = [s for s in st.session_state.services if s['id'] != service['id']]
                                st.success("تم حذف الخدمة")
                                st.rerun()

                    st.divider()
    else:
        st.info("لا توجد خدمات مضافة")

def employees_page():
    """صفحة إدارة الموظفين"""
    st.title("👨‍💼 إدارة الموظفين")

    # أزرار العمليات
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("إضافة موظف جديد", use_container_width=True):
            st.session_state.show_add_employee = True

    with col2:
        if st.button("تحديث القائمة", use_container_width=True):
            st.rerun()

    # نموذج إضافة موظف جديد
    if st.session_state.get('show_add_employee', False):
        with st.expander("إضافة موظف جديد", expanded=True):
            with st.form("add_employee_form"):
                col1, col2 = st.columns(2)

                with col1:
                    first_name = st.text_input("الاسم الأول *")
                    last_name = st.text_input("اسم العائلة")
                    phone = st.text_input("رقم الهاتف *")
                    email = st.text_input("البريد الإلكتروني")

                with col2:
                    position = st.selectbox("المنصب", [
                        "مدير",
                        "مدير مساعد",
                        "صنايعي حلاقة",
                        "أخصائي تصفيف شعر",
                        "أخصائية تنظيف بشرة",
                        "أخصائي عناية بالبشرة",
                        "فني باديكير ومانيكير",
                        "موظف استقبال",
                        "عامل نظافة"
                    ])

                    specialization = st.text_input("التخصص")
                    salary = st.number_input("الراتب الأساسي (ج.م)", min_value=0.0, step=100.0)
                    commission_rate = st.number_input("نسبة العمولة (%)", min_value=0.0, max_value=100.0, value=10.0)

                # اختيار الفرع
                branches = make_api_request("/branches")
                if branches:
                    branch_options = {branch['name']: branch['branch_id'] for branch in branches}
                    selected_branch = st.selectbox("الفرع", options=list(branch_options.keys()))
                    branch_id = branch_options[selected_branch] if selected_branch else None
                else:
                    st.warning("لا توجد فروع متاحة")
                    branch_id = None

                col1, col2 = st.columns(2)
                with col1:
                    submit = st.form_submit_button("إضافة الموظف", use_container_width=True)
                with col2:
                    cancel = st.form_submit_button("إلغاء", use_container_width=True)

                if submit and first_name and phone:
                    # حفظ الموظف (مؤقتاً في session state)
                    if 'employees' not in st.session_state:
                        st.session_state.employees = []

                    new_employee = {
                        'id': len(st.session_state.employees) + 1,
                        'first_name': first_name,
                        'last_name': last_name,
                        'full_name': f"{first_name} {last_name}".strip(),
                        'phone': phone,
                        'email': email,
                        'position': position,
                        'specialization': specialization,
                        'branch_id': branch_id,
                        'branch_name': selected_branch if branch_id else '',
                        'salary': salary,
                        'commission_rate': commission_rate,
                        'is_active': True
                    }

                    st.session_state.employees.append(new_employee)
                    st.success("تم إضافة الموظف بنجاح!")
                    st.session_state.show_add_employee = False
                    st.rerun()

                if cancel:
                    st.session_state.show_add_employee = False
                    st.rerun()

    # عرض الموظفين الافتراضيين المصريين
    st.subheader("👥 فريق العمل المصري")

    # الموظفين الافتراضيين
    default_employees = [
        {"name": "عمر محمد", "position": "مدير", "branch": "فرع الزهراء", "specialization": "إدارة عامة", "phone": "01090829393"},
        {"name": "عمر فلانتينو", "position": "مدير", "branch": "فرع جسر السويس", "specialization": "إدارة عامة", "phone": "01090829393"},
        {"name": "أسامة", "position": "صنايعي حلاقة", "branch": "فرع جسر السويس", "specialization": "حلاقة وتصفيف شعر", "phone": "01090829393"},
        {"name": "أحمد عمر", "position": "مدير", "branch": "فرع مدينة نصر", "specialization": "إدارة عامة", "phone": "01090829393"},
        {"name": "ياسمين", "position": "أخصائية تنظيف بشرة", "branch": "فرع مدينة نصر", "specialization": "عناية بالبشرة وتنظيف", "phone": "01090829393"}
    ]

    # دمج الموظفين الافتراضيين مع المضافين
    all_employees = default_employees.copy()
    if 'employees' in st.session_state:
        for emp in st.session_state.employees:
            all_employees.append({
                "name": emp['full_name'],
                "position": emp['position'],
                "branch": emp['branch_name'],
                "specialization": emp['specialization'],
                "phone": emp['phone'],
                "id": emp['id']
            })

    # تجميع الموظفين حسب الفرع
    branches_employees = {}
    for emp in all_employees:
        branch = emp.get('branch', 'غير محدد')
        if branch not in branches_employees:
            branches_employees[branch] = []
        branches_employees[branch].append(emp)

    # عرض الموظفين حسب الفرع
    for branch, employees in branches_employees.items():
        st.subheader(f"🏢 {branch}")

        for emp in employees:
            with st.container():
                col1, col2, col3, col4 = st.columns([2, 2, 1, 1])

                with col1:
                    st.write(f"**{emp['name']}**")
                    st.write(f"📞 {emp['phone']}")

                with col2:
                    st.write(f"💼 {emp['position']}")
                    st.write(f"🎯 {emp['specialization']}")

                with col3:
                    if emp.get('id'):  # موظف مضاف من المستخدم
                        if st.button("تعديل", key=f"edit_emp_{emp['id']}", use_container_width=True):
                            st.info("وظيفة التعديل قيد التطوير")

                with col4:
                    if emp.get('id'):  # موظف مضاف من المستخدم
                        if st.button("حذف", key=f"delete_emp_{emp['id']}", use_container_width=True):
                            st.session_state.employees = [e for e in st.session_state.employees if e['id'] != emp['id']]
                            st.success("تم حذف الموظف")
                            st.rerun()

                st.divider()

    # إحصائيات الموظفين
    st.subheader("📊 إحصائيات الموظفين")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("إجمالي الموظفين", len(all_employees))

    with col2:
        managers_count = len([emp for emp in all_employees if 'مدير' in emp['position']])
        st.metric("المديرين", managers_count)

    with col3:
        specialists_count = len([emp for emp in all_employees if 'أخصائي' in emp['position'] or 'صنايعي' in emp['position']])
        st.metric("المتخصصين", specialists_count)

    with col4:
        active_branches = len(branches_employees)
        st.metric("الفروع النشطة", active_branches)

def products_page():
    """صفحة إدارة المنتجات"""
    st.title("📦 إدارة المنتجات والمخزون")

    # أزرار العمليات
    col1, col2, col3, col4 = st.columns([1, 1, 1, 1])

    with col1:
        if st.button("إضافة منتج جديد", use_container_width=True):
            st.session_state.show_add_product = True

    with col2:
        if st.button("تحديث المخزون", use_container_width=True):
            st.rerun()

    with col3:
        if st.button("تقرير المخزون", use_container_width=True):
            st.session_state.show_inventory_report = True

    with col4:
        if st.button("المنتجات المنتهية", use_container_width=True):
            st.session_state.show_low_stock = True

    # نموذج إضافة منتج جديد
    if st.session_state.get('show_add_product', False):
        with st.expander("إضافة منتج جديد", expanded=True):
            with st.form("add_product_form"):
                col1, col2 = st.columns(2)

                with col1:
                    name = st.text_input("اسم المنتج *")
                    brand = st.text_input("الماركة")
                    category = st.selectbox("الفئة", [
                        "منتجات الشعر",
                        "منتجات البشرة",
                        "أدوات التصفيف",
                        "مستحضرات التجميل",
                        "منتجات العناية",
                        "أدوات ومعدات",
                        "منتجات أخرى"
                    ])
                    barcode = st.text_input("الباركود")

                with col2:
                    cost_price = st.number_input("سعر التكلفة (ج.م)", min_value=0.0, step=1.0)
                    selling_price = st.number_input("سعر البيع (ج.م) *", min_value=0.0, step=1.0)
                    stock_quantity = st.number_input("الكمية الحالية", min_value=0, step=1)
                    min_stock_level = st.number_input("الحد الأدنى للمخزون", min_value=0, step=1, value=5)

                description = st.text_area("وصف المنتج")

                col1, col2 = st.columns(2)
                with col1:
                    submit = st.form_submit_button("إضافة المنتج", use_container_width=True)
                with col2:
                    cancel = st.form_submit_button("إلغاء", use_container_width=True)

                if submit and name and selling_price:
                    # حفظ المنتج (مؤقتاً في session state)
                    if 'products' not in st.session_state:
                        st.session_state.products = []

                    new_product = {
                        'id': len(st.session_state.products) + 1,
                        'name': name,
                        'brand': brand,
                        'category': category,
                        'description': description,
                        'cost_price': cost_price,
                        'selling_price': selling_price,
                        'stock_quantity': stock_quantity,
                        'min_stock_level': min_stock_level,
                        'barcode': barcode,
                        'is_active': True
                    }

                    st.session_state.products.append(new_product)
                    st.success("تم إضافة المنتج بنجاح!")
                    st.session_state.show_add_product = False
                    st.rerun()

                if cancel:
                    st.session_state.show_add_product = False
                    st.rerun()

    # المنتجات الافتراضية
    default_products = [
        {"name": "شامبو لوريال", "brand": "L'Oreal", "category": "منتجات الشعر", "cost": 80, "price": 120, "stock": 25, "min_stock": 5},
        {"name": "كريم فرد الشعر", "brand": "Schwarzkopf", "category": "منتجات الشعر", "cost": 150, "price": 250, "stock": 15, "min_stock": 3},
        {"name": "صبغة شعر احترافية", "brand": "Wella", "category": "منتجات الشعر", "cost": 200, "price": 350, "stock": 30, "min_stock": 5},
        {"name": "كريم تنظيف البشرة", "brand": "Nivea", "category": "منتجات البشرة", "cost": 60, "price": 100, "stock": 40, "min_stock": 10},
        {"name": "ماسك الوجه", "brand": "Garnier", "category": "منتجات البشرة", "cost": 45, "price": 80, "stock": 20, "min_stock": 5},
        {"name": "مكواة شعر احترافية", "brand": "Babyliss", "category": "أدوات التصفيف", "cost": 800, "price": 1200, "stock": 5, "min_stock": 2},
        {"name": "مجفف شعر", "brand": "Philips", "category": "أدوات التصفيف", "cost": 600, "price": 900, "stock": 8, "min_stock": 2},
        {"name": "أحمر شفاه", "brand": "Maybelline", "category": "مستحضرات التجميل", "cost": 80, "price": 150, "stock": 50, "min_stock": 10},
        {"name": "كريم أساس", "brand": "Revlon", "category": "مستحضرات التجميل", "cost": 120, "price": 200, "stock": 35, "min_stock": 8}
    ]

    # دمج المنتجات الافتراضية مع المضافة
    all_products = []
    for i, product in enumerate(default_products):
        all_products.append({
            'id': f'default_{i}',
            'name': product['name'],
            'brand': product['brand'],
            'category': product['category'],
            'cost_price': product['cost'],
            'selling_price': product['price'],
            'stock_quantity': product['stock'],
            'min_stock_level': product['min_stock'],
            'is_default': True
        })

    if 'products' in st.session_state:
        all_products.extend(st.session_state.products)

    # عرض المنتجات
    st.subheader("📋 قائمة المنتجات")

    if all_products:
        # تجميع المنتجات حسب الفئة
        categories = {}
        for product in all_products:
            cat = product.get('category', 'أخرى')
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(product)

        # عرض كل فئة
        for category, products in categories.items():
            st.subheader(f"📦 {category}")

            for product in products:
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 2, 1, 1])

                    with col1:
                        st.write(f"**{product['name']}**")
                        if product.get('brand'):
                            st.write(f"🏷️ {product['brand']}")

                    with col2:
                        st.write(f"💰 {product['selling_price']} ج.م")
                        stock_color = "🟢" if product['stock_quantity'] > product['min_stock_level'] else "🔴"
                        st.write(f"{stock_color} المخزون: {product['stock_quantity']}")

                    with col3:
                        if not product.get('is_default'):  # منتج مضاف من المستخدم
                            if st.button("تعديل", key=f"edit_product_{product['id']}", use_container_width=True):
                                st.info("وظيفة التعديل قيد التطوير")

                    with col4:
                        if not product.get('is_default'):  # منتج مضاف من المستخدم
                            if st.button("حذف", key=f"delete_product_{product['id']}", use_container_width=True):
                                st.session_state.products = [p for p in st.session_state.products if p['id'] != product['id']]
                                st.success("تم حذف المنتج")
                                st.rerun()

                    st.divider()

    # إحصائيات المخزون
    st.subheader("📊 إحصائيات المخزون")
    col1, col2, col3, col4 = st.columns(4)

    total_products = len(all_products)
    low_stock_products = len([p for p in all_products if p['stock_quantity'] <= p['min_stock_level']])
    total_value = sum([p['selling_price'] * p['stock_quantity'] for p in all_products])

    with col1:
        st.metric("إجمالي المنتجات", total_products)

    with col2:
        st.metric("منتجات منخفضة المخزون", low_stock_products, delta=f"-{low_stock_products}" if low_stock_products > 0 else None)

    with col3:
        st.metric("قيمة المخزون", f"{total_value:,.0f} ج.م")

    with col4:
        categories_count = len(categories) if all_products else 0
        st.metric("عدد الفئات", categories_count)

def sales_page():
    """صفحة المبيعات والفواتير"""

    # الشريط العلوي
    st.markdown("""
    <div class="top-bar">
        <div style="display: flex; align-items: center;">
            <h2 style="margin: 0; color: white;">💰 نظام إدارة المبيعات والفواتير</h2>
        </div>
        <div style="display: flex; align-items: center; gap: 1rem;">
            <span>المستخدم: {}</span>
            <span>الفرع: {}</span>
            <span>التاريخ: {}</span>
        </div>
    </div>
    """.format(
        st.session_state.user.get('username', 'غير محدد'),
        st.session_state.get('current_branch_name', 'غير محدد'),
        datetime.now().strftime('%Y-%m-%d')
    ), unsafe_allow_html=True)

    # أزرار التحكم الرئيسية مع تصميم احترافي
    st.markdown("### 🎛️ لوحة التحكم")

    col1, col2, col3, col4, col5 = st.columns(5)

    with col1:
        if st.button("🆕 فاتورة جديدة", use_container_width=True, type="primary"):
            st.session_state.show_new_invoice = True
            st.session_state.show_invoices_list = False
            st.session_state.show_sales_stats = False

    with col2:
        if st.button("📋 عرض الفواتير", use_container_width=True):
            st.session_state.show_new_invoice = False
            st.session_state.show_invoices_list = True
            st.session_state.show_sales_stats = False

    with col3:
        if st.button("📊 إحصائيات المبيعات", use_container_width=True):
            st.session_state.show_new_invoice = False
            st.session_state.show_invoices_list = False
            st.session_state.show_sales_stats = True

    with col4:
        if st.button("🔍 البحث المتقدم", use_container_width=True):
            st.session_state.show_advanced_search = True

    with col5:
        if st.button("📄 طباعة تقرير", use_container_width=True):
            st.info("وظيفة الطباعة قيد التطوير")

    # إحصائيات سريعة في الأعلى
    if 'invoices' in st.session_state and st.session_state.invoices:
        current_branch_id = st.session_state.get('current_branch_id', 1)
        branch_invoices = [inv for inv in st.session_state.invoices
                         if inv.get('branch_id') == current_branch_id and not inv.get('is_deleted', False)]

        if branch_invoices:
            st.markdown("### 📈 إحصائيات سريعة")

            col1, col2, col3, col4 = st.columns(4)

            total_sales = sum([inv['total_amount'] for inv in branch_invoices])
            today_invoices = [inv for inv in branch_invoices if inv['date'] == datetime.now().strftime('%Y-%m-%d')]
            today_sales = sum([inv['total_amount'] for inv in today_invoices])
            avg_invoice = total_sales / len(branch_invoices) if branch_invoices else 0

            with col1:
                st.markdown("""
                <div class="metric-card">
                    <div class="stat-number">{}</div>
                    <div class="stat-label">إجمالي الفواتير</div>
                </div>
                """.format(len(branch_invoices)), unsafe_allow_html=True)

            with col2:
                st.markdown("""
                <div class="metric-card">
                    <div class="stat-number">{:.0f}</div>
                    <div class="stat-label">إجمالي المبيعات (ج.م)</div>
                </div>
                """.format(total_sales), unsafe_allow_html=True)

            with col3:
                st.markdown("""
                <div class="metric-card">
                    <div class="stat-number">{}</div>
                    <div class="stat-label">فواتير اليوم</div>
                </div>
                """.format(len(today_invoices)), unsafe_allow_html=True)

            with col4:
                st.markdown("""
                <div class="metric-card">
                    <div class="stat-number">{:.0f}</div>
                    <div class="stat-label">متوسط الفاتورة (ج.م)</div>
                </div>
                """.format(avg_invoice), unsafe_allow_html=True)

    # نموذج فاتورة جديدة مع تصميم احترافي بحجم الشاشة الكامل
    if st.session_state.get('show_new_invoice', False):
        # إخفاء الشريط الجانبي وتوسيع المحتوى
        st.markdown("""
        <style>
        .main .block-container {
            padding-top: 1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            max-width: 100% !important;
            width: 100% !important;
        }
        .professional-invoice {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin: 1rem 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
        }
        .invoice-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 1rem;
        }
        .invoice-section {
            background: rgba(255,255,255,0.95);
            color: #333;
            padding: 1.5rem;
            border-radius: 15px;
            margin: 1rem 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            width: 100%;
        }
        .invoice-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 1rem;
            margin: 0.5rem 0;
            border-left: 4px solid #3b82f6;
        }
        .invoice-total {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
        }
        .invoice-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        .stForm {
            width: 100% !important;
        }
        </style>

        <div class="professional-invoice">
            <div class="invoice-header">
                <h2 style="margin: 0;">🏢 SalonProManager</h2>
                <h3 style="margin: 0.5rem 0;">🧾 فاتورة مبيعات جديدة - بحجم الشاشة الكامل</h3>
                <p style="margin: 0; opacity: 0.9;">نظام إدارة صالونات احترافي</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # زر إغلاق الفاتورة
        col1, col2, col3 = st.columns([1, 8, 1])
        with col3:
            if st.button("❌ إغلاق", key="close_invoice", use_container_width=True):
                st.session_state.show_new_invoice = False
                st.rerun()

        # معلومات العميل والفاتورة - إزالة النموذج لتجنب مشاكل الأزرار
        st.markdown("### 👤 معلومات العميل والفاتورة")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("**بيانات العميل:**")

            # جلب العملاء من قاعدة البيانات مباشرة بدلاً من API
            customers_list = []
            selected_customer = "عميل جديد"  # قيمة افتراضية
            customer_id = None

            # محاولة جلب العملاء من قاعدة البيانات
            if st.session_state.get('db_initialized', False):
                try:
                    current_branch_id = st.session_state.get('current_branch_id', 1)
                    db_customers = get_customers_by_branch(current_branch_id)
                    if db_customers:
                        for customer in db_customers:
                            # التعامل مع هيكل البيانات الصحيح من قاعدة البيانات
                            customer_id = customer.get('CustomerId') or customer.get('customer_id')
                            first_name = customer.get('FirstName') or customer.get('first_name', '')
                            last_name = customer.get('LastName') or customer.get('last_name', '')
                            phone = customer.get('PhoneNumber') or customer.get('phone', '')

                            customers_list.append({
                                'id': customer_id,
                                'name': f"{first_name} {last_name}".strip(),
                                'phone': phone
                            })
                except Exception as e:
                    st.warning(f"تعذر جلب العملاء من قاعدة البيانات: {str(e)}")

            # إضافة العملاء من session state كنسخة احتياطية
            if not customers_list:
                session_customers = st.session_state.get('customers', [])
                current_branch_id = st.session_state.get('current_branch_id', 1)
                branch_customers = [c for c in session_customers if c.get('branch_id') == current_branch_id and not c.get('is_deleted', False)]

                for customer in branch_customers:
                    customers_list.append({
                        'id': customer['customer_id'],
                        'name': customer.get('full_name', 'بدون اسم'),
                        'phone': customer.get('phone', '')
                    })

            # إنشاء خيارات العملاء
            customer_options = ["عميل جديد"]  # البداية بعميل جديد
            if customers_list:
                customer_options.extend([f"{c['name']} - {c['phone']}" for c in customers_list])

            selected_customer = st.selectbox("اختر العميل", options=customer_options, index=0, key="invoice_customer_select")

            # تحديد معرف العميل
            if selected_customer == "عميل جديد":
                customer_id = None
            else:
                # البحث عن العميل المختار
                for c in customers_list:
                    if f"{c['name']} - {c['phone']}" == selected_customer:
                        customer_id = c['id']
                        break
                else:
                    customer_id = None

            if not customers_list:
                st.info("💡 لا يوجد عملاء مسجلين - يمكنك إضافة عميل جديد")

            # إضافة عميل جديد سريع
            new_customer_name = ""
            new_customer_phone = ""
            if selected_customer == "عميل جديد":
                new_customer_name = st.text_input("اسم العميل الجديد")
                new_customer_phone = st.text_input("رقم الهاتف")

        with col2:
            st.markdown("**تفاصيل الفاتورة:**")
            invoice_date = st.date_input("تاريخ الفاتورة", value=datetime.now().date(), key="invoice_date_input")
            payment_method = st.selectbox("طريقة الدفع",
                ["نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية", "فيزا", "ماستركارد"],
                key="invoice_payment_method_main")

            # اختيار الموظف المسؤول عن الفاتورة
            employee_options = ["اختر الموظف", "أحمد محمد", "فاطمة علي", "سارة أحمد", "نور محمد", "ليلى حسن", "مريم سالم", "هدى عبدالله"]
            selected_employee = st.selectbox("الموظف المسؤول", options=employee_options, key="invoice_employee_select")

            # رقم الفاتورة التلقائي
            next_invoice_id = len(st.session_state.get('invoices', [])) + 1
            st.text_input("رقم الفاتورة", value=f"INV-{next_invoice_id:04d}", disabled=True)

        with col3:
            st.markdown("**معلومات الفرع:**")
            current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')
            st.text_input("الفرع", value=current_branch_name, disabled=True)

            cashier_name = st.session_state.user.get('username', 'غير محدد')
            st.text_input("الكاشير", value=cashier_name, disabled=True)

            st.text_input("الوقت", value=datetime.now().strftime('%H:%M'), disabled=True)

        st.divider()

        # قسم اختيار الخدمات والمنتجات مع تصميم مشابه للصورة
        st.markdown("### 🛒 عناصر الفاتورة")

        # الحصول على الأسعار حسب فئة الفرع الحالي
        current_branch_id = st.session_state.get('current_branch_id', 1)

        # تحديد الفئة السعرية للفرع
        branch_price_categories = {
            1: "premium",    # فرع مدينة نصر - فئة بريميوم
            2: "standard",   # فرع جسر السويس - فئة عادية
            3: "economy",    # فرع الزهراء - فئة اقتصادية
            0: "standard"    # الإدارة العامة - فئة عادية
        }

        # واجهة كاشير بسيطة ومرتبة
        st.markdown("## 🛒 نظام الكاشير")

        # CSS بسيط ونظيف
        st.markdown("""
        <style>
        .simple-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
        }
        .price-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 0.5rem;
            text-align: center;
            font-weight: bold;
            color: #495057;
        }
        </style>
        """, unsafe_allow_html=True)

        # شريط البحث البسيط
        search_term = st.text_input(
            "🔍 ابحث عن خدمة أو منتج",
            placeholder="اكتب اسم الخدمة أو المنتج أو الكود (مثل: S001)",
            key="search_input"
        )

        price_categories = {
                "premium": {
                    "services": {
                        "قص شعر بروفيشنال": 260, "دقن ستايلنج": 195, "صبغة شعر كاملة": 390,
                        "استشوار برو": 260, "مكواة كيرلي بروفيشنال": 650, "بشرة لايت": 650,
                        "هيدرو فيشال": 2600, "أكسچنيو الفاخرة": 3900, "باديكير يد وقدم": 650,
                        "باديكير عناية كاملة + مساج": 910, "كولاجين للشعر": 1950,
                        "بوتوكس للشعر": 2340, "كيراتين للشعر": 2600
                    },
                    "products": {
                        "شامبو لوريال": 156, "كريم فرد الشعر": 325, "صبغة شعر احترافية": 455,
                        "كريم تنظيف البشرة": 130, "ماسك الوجه": 104, "مكواة شعر احترافية": 1560,
                        "مجفف شعر": 1170, "أحمر شفاه": 195, "كريم أساس": 260
                    }
                },
                    "standard": {
                        "services": {
                            "قص شعر بروفيشنال": 200, "دقن ستايلنج": 150, "صبغة شعر كاملة": 300,
                            "استشوار برو": 200, "مكواة كيرلي بروفيشنال": 500, "بشرة لايت": 500,
                            "هيدرو فيشال": 2000, "أكسچنيو الفاخرة": 3000, "باديكير يد وقدم": 500,
                            "باديكير عناية كاملة + مساج": 700, "كولاجين للشعر": 1500,
                            "بوتوكس للشعر": 1800, "كيراتين للشعر": 2000
                        },
                        "products": {
                            "شامبو لوريال": 120, "كريم فرد الشعر": 250, "صبغة شعر احترافية": 350,
                            "كريم تنظيف البشرة": 100, "ماسك الوجه": 80, "مكواة شعر احترافية": 1200,
                            "مجفف شعر": 900, "أحمر شفاه": 150, "كريم أساس": 200
                        }
                    },
                    "economy": {
                        "services": {
                            "قص شعر بروفيشنال": 160, "دقن ستايلنج": 120, "صبغة شعر كاملة": 240,
                            "استشوار برو": 160, "مكواة كيرلي بروفيشنال": 400, "بشرة لايت": 400,
                            "هيدرو فيشال": 1600, "أكسچنيو الفاخرة": 2400, "باديكير يد وقدم": 400,
                            "باديكير عناية كاملة + مساج": 560, "كولاجين للشعر": 1200,
                            "بوتوكس للشعر": 1440, "كيراتين للشعر": 1600
                        },
                        "products": {
                            "شامبو لوريال": 96, "كريم فرد الشعر": 200, "صبغة شعر احترافية": 280,
                            "كريم تنظيف البشرة": 80, "ماسك الوجه": 64, "مكواة شعر احترافية": 960,
                            "مجفف شعر": 720, "أحمر شفاه": 120, "كريم أساس": 160
                        }
                    }
        }

        # تحديد الفئة السعرية للفرع الحالي
        current_category = branch_price_categories.get(current_branch_id, "standard")
        services_dict = price_categories[current_category]["services"]
        products_dict = price_categories[current_category]["products"]

        # عرض معلومات الفئة السعرية
        category_names = {
                "premium": "🌟 فئة بريميوم",
                "standard": "⭐ فئة عادية",
                "economy": "💰 فئة اقتصادية"
        }

        st.info(f"💰 **الفئة السعرية:** {category_names[current_category]}")

        # قاعدة بيانات الخدمات مع الأكواد والموظفين
        services_database = {
                "S001": {"name": "قص شعر رجالي", "price": services_dict.get("قص شعر رجالي", 50), "employee": "أحمد محمد", "commission": 15},
                "S002": {"name": "قص شعر نسائي", "price": services_dict.get("قص شعر نسائي", 80), "employee": "فاطمة علي", "commission": 20},
                "S003": {"name": "صبغة شعر", "price": services_dict.get("صبغة شعر", 150), "employee": "سارة أحمد", "commission": 25},
                "S004": {"name": "فرد شعر", "price": services_dict.get("فرد شعر", 200), "employee": "نور محمد", "commission": 30},
                "S005": {"name": "تسريحة عروس", "price": services_dict.get("تسريحة عروس", 300), "employee": "ليلى حسن", "commission": 45},
                "S006": {"name": "حلاقة ذقن", "price": services_dict.get("حلاقة ذقن", 25), "employee": "أحمد محمد", "commission": 8},
                "S007": {"name": "ماسك للوجه", "price": services_dict.get("ماسك للوجه", 100), "employee": "مريم سالم", "commission": 15},
                "S008": {"name": "تنظيف بشرة", "price": services_dict.get("تنظيف بشرة", 120), "employee": "هدى عبدالله", "commission": 18},
        }

        # قاعدة بيانات المنتجات مع الأكواد
        products_database = {
                "P001": {"name": "شامبو للشعر الجاف", "price": products_dict.get("شامبو للشعر الجاف", 45), "stock": 50},
                "P002": {"name": "كريم للشعر", "price": products_dict.get("كريم للشعر", 35), "stock": 30},
                "P003": {"name": "زيت للشعر", "price": products_dict.get("زيت للشعر", 25), "stock": 40},
                "P004": {"name": "صبغة شعر طبيعية", "price": products_dict.get("صبغة شعر طبيعية", 80), "stock": 20},
                "P005": {"name": "ماسك للوجه", "price": products_dict.get("ماسك للوجه", 60), "stock": 25},
                "P006": {"name": "كريم مرطب", "price": products_dict.get("كريم مرطب", 40), "stock": 35},
                "P007": {"name": "سيروم للشعر", "price": products_dict.get("سيروم للشعر", 90), "stock": 15},
                "P008": {"name": "مقص شعر احترافي", "price": products_dict.get("مقص شعر احترافي", 150), "stock": 10},
        }

        # دمج جميع الأصناف للبحث السريع
        all_items = {}

        # إضافة الخدمات مع الأكواد
        for code, service_data in services_database.items():
                display_name = f"{code} - {service_data['name']} - {service_data['price']} ج.م"
                all_items[display_name] = {
                    'type': 'service',
                    'code': code,
                    'name': service_data['name'],
                    'price': service_data['price'],
                    'employee': service_data['employee'],
                    'commission': service_data['commission'],
                    'icon': '💆‍♀️'
            }

        # إضافة المنتجات مع الأكواد
        for code, product_data in products_database.items():
            display_name = f"{code} - {product_data['name']} - {product_data['price']} ج.م"
            all_items[display_name] = {
                'type': 'product',
                'code': code,
                'name': product_data['name'],
                'price': product_data['price'],
                'stock': product_data['stock'],
                'icon': '🛍️'
            }

        # نظام البحث المحسن من قاعدة البيانات
        if search_term:
            # البحث في جدول items
            try:
                conn = get_db_connection()
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT item_code, item_name, item_type, price_tier_2,
                               ISNULL(stock_quantity, 0) as stock_quantity
                        FROM items
                        WHERE is_active = 1 AND is_deleted = 0
                        AND (item_code LIKE ? OR item_name LIKE ?)
                        ORDER BY item_type, item_code
                    """, (f'%{search_term}%', f'%{search_term}%'))

                    search_results = cursor.fetchall()
                    conn.close()

                    if search_results:
                        st.markdown("### 🎯 نتائج البحث:")

                        # عرض النتائج من قاعدة البيانات
                        for result in search_results[:5]:
                            item_code, item_name, item_type, price, stock = result
                            col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                            with col1:
                                icon = "🔧" if item_type == 'service' else "📦"
                                st.write(f"{icon} **{item_name}**")
                                st.caption(f"الكود: {item_code}")

                            with col2:
                                st.write(f"**{price:.0f} ج.م**")

                            with col3:
                                quantity = st.number_input("كمية", min_value=1, value=1,
                                                         key=f"qty_{item_code}",
                                                         label_visibility="collapsed")

                            with col4:
                                if st.button("إضافة", key=f"add_{item_code}",
                                           use_container_width=True):
                                    new_item = {
                                        'type': item_type,
                                        'code': item_code,
                                        'name': item_name,
                                        'price': price,
                                        'quantity': quantity,
                                        'total': price * quantity,
                                        'employee': 'غير محدد',
                                        'commission': (price * 0.15 * quantity) if item_type == 'service' else 0
                                    }

                                    st.session_state.invoice_items.append(new_item)
                                    st.success(f"✅ تم إضافة {item_name}")
                                    st.rerun()
                    else:
                        st.warning("لم يتم العثور على نتائج")

            except Exception as e:
                # في حالة فشل قاعدة البيانات، استخدم النظام القديم
                filtered_items = {}
                for display_name, item_data in all_items.items():
                    if (search_term.lower() in display_name.lower() or
                        search_term.lower() in item_data['code'].lower() or
                        search_term.lower() in item_data['name'].lower()):
                        filtered_items[display_name] = item_data

                if filtered_items:
                    st.markdown("### 🎯 نتائج البحث:")
                    for item_name, item_data in list(filtered_items.items())[:5]:
                        col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                        with col1:
                            icon = "🔧" if item_data['type'] == 'service' else "📦"
                            st.write(f"{icon} **{item_data['name']}**")
                            st.caption(f"الكود: {item_data['code']}")

                        with col2:
                            st.write(f"**{item_data['price']:.0f} ج.م**")

                        with col3:
                            quantity = st.number_input("كمية", min_value=1, value=1,
                                                     key=f"qty_{item_data['code']}",
                                                     label_visibility="collapsed")

                        with col4:
                            if st.button("إضافة", key=f"add_{item_data['code']}",
                                       use_container_width=True):
                                new_item = {
                                    'type': item_data['type'],
                                    'code': item_data['code'],
                                    'name': item_data['name'],
                                    'price': item_data['price'],
                                    'quantity': quantity,
                                    'total': item_data['price'] * quantity,
                                    'employee': 'غير محدد',
                                    'commission': item_data.get('commission', 0) * quantity if item_data['type'] == 'service' else 0
                                }

                                st.session_state.invoice_items.append(new_item)
                                st.success(f"✅ تم إضافة {item_data['name']}")
                                st.rerun()
                else:
                    st.warning("لم يتم العثور على نتائج")

        # عرض طريقة الدفع
        payment_method = st.selectbox("طريقة الدفع",
            ["نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية", "فيزا", "ماستركارد"],
            key="invoice_payment_method")

        # إنشاء جدول تفاعلي للخدمات والمنتجات
        if 'invoice_items' not in st.session_state:
            st.session_state.invoice_items = []

        # عرض العناصر المضافة للفاتورة
        if st.session_state.invoice_items:
            st.markdown("### 📋 العناصر في الفاتورة")

            # جدول بسيط
            for i, item in enumerate(st.session_state.invoice_items):
                col1, col2, col3, col4, col5 = st.columns([3, 1, 1, 1, 1])

                with col1:
                    # تحديد الأيقونة حسب النوع
                    icon = "🔧" if item['type'] == 'service' else "📦"
                    code = item.get('code', item.get('name', 'غير محدد'))
                    st.write(f"{icon} **{code}** - {item['name']}")
                    if item['type'] == 'service' and item.get('employee'):
                        st.caption(f"👤 {item['employee']}")

                with col2:
                    st.write(f"{item['price']:.0f} ج.م")

                with col3:
                    st.write(f"الكمية: {item['quantity']}")

                with col4:
                    st.write(f"**{item['total']:.0f} ج.م**")

                with col5:
                    if st.button("🗑️", key=f"del_{i}", help="حذف"):
                        st.session_state.invoice_items.pop(i)
                        st.rerun()

            # حساب الإجمالي مع الخصم والضريبة
            subtotal = sum([item['total'] for item in st.session_state.invoice_items])

            # خانات الخصم والضريبة
            st.markdown("### 💰 تفاصيل الفاتورة")
            col1, col2, col3 = st.columns(3)

            with col1:
                discount_type = st.selectbox("نوع الخصم", ["لا يوجد", "نسبة مئوية", "مبلغ ثابت"], key="discount_type")

            with col2:
                if discount_type == "نسبة مئوية":
                    discount_value = st.number_input("نسبة الخصم (%)", min_value=0.0, max_value=100.0, value=None, placeholder="أدخل النسبة", key="discount_percent")
                    discount_amount = subtotal * (discount_value / 100) if discount_value else 0.0
                elif discount_type == "مبلغ ثابت":
                    discount_amount = st.number_input("مبلغ الخصم (ج.م)", min_value=0.0, max_value=float(subtotal), value=None, placeholder="أدخل المبلغ", key="discount_amount")
                    if not discount_amount:
                        discount_amount = 0.0
                else:
                    discount_amount = 0.0

            with col3:
                tax_rate = st.number_input("نسبة الضريبة (%)", min_value=0.0, max_value=100.0, value=None, placeholder="أدخل النسبة", key="tax_rate")
                tax_amount = (subtotal - discount_amount) * (tax_rate / 100) if tax_rate else 0.0

            # حساب الإجمالي النهائي
            total_amount = subtotal - discount_amount + tax_amount

            # عرض الملخص
            st.markdown("#### 📊 ملخص الفاتورة")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("المجموع الفرعي", f"{subtotal:.2f} ج.م")
            with col2:
                st.metric("الخصم", f"-{discount_amount:.2f} ج.م")
            with col3:
                st.metric("الضريبة", f"+{tax_amount:.2f} ج.م")
            with col4:
                st.metric("الإجمالي النهائي", f"{total_amount:.2f} ج.م")

            # أزرار الحفظ
            col1, col2 = st.columns(2)
            with col1:
                if st.button("✅ حفظ الفاتورة", type="primary", use_container_width=True):
                    # حفظ الفاتورة مع التفاصيل الكاملة
                    invoice_id = len(st.session_state.get('invoices', [])) + 1
                    new_invoice = {
                        'id': invoice_id,
                        'date': datetime.now().strftime('%Y-%m-%d'),
                        'time': datetime.now().strftime('%H:%M'),
                        'customer_name': selected_customer if selected_customer != "عميل جديد" else "عميل نقدي",
                        'items': st.session_state.invoice_items.copy(),
                        'subtotal': subtotal,
                        'discount_amount': discount_amount,
                        'tax_amount': tax_amount,
                        'total_amount': total_amount,
                        'payment_method': 'نقدي',
                        'branch_id': st.session_state.get('current_branch_id', 1),
                        'branch_name': st.session_state.get('current_branch_name', 'الفرع الرئيسي'),
                        'created_by': st.session_state.user['username'],
                        'status': 'مدفوعة',
                        'is_deleted': False
                    }

                    # حفظ مباشر في قاعدة البيانات (إزالة الشرط المانع)
                    try:
                        conn = get_db_connection()
                        if conn:
                                cursor = conn.cursor()

                                # تحديد معرف العميل الحقيقي
                                customer_id = 1  # افتراضي
                                if selected_customer != "اختر العميل":
                                    # البحث عن العميل في قاعدة البيانات
                                    cursor.execute("""
                                        SELECT CustomerId FROM Customers
                                        WHERE FirstName + ' ' + LastName = ? OR FirstName = ?
                                        ORDER BY CustomerId DESC
                                    """, (selected_customer, selected_customer.split()[0] if ' ' in selected_customer else selected_customer))
                                    customer_result = cursor.fetchone()
                                    if customer_result:
                                        customer_id = customer_result[0]

                                # تحديد معرف الموظف المسؤول
                                responsible_employee_id = 1  # افتراضي
                                if selected_employee != "اختر الموظف":
                                    cursor.execute("""
                                        SELECT employee_id FROM employees
                                        WHERE first_name + ' ' + ISNULL(last_name, '') = ? OR first_name = ?
                                        ORDER BY employee_id DESC
                                    """, (selected_employee, selected_employee.split()[0] if ' ' in selected_employee else selected_employee))
                                    employee_result = cursor.fetchone()
                                    if employee_result:
                                        responsible_employee_id = employee_result[0]

                                # توليد كود الفاتورة التسلسلي
                                branch_id = st.session_state.get('current_branch_id', 1)
                                sequence_name = f'branch_invoice_{branch_id}'
                                invoice_code = get_next_sequence_code(sequence_name, branch_id)

                                if not invoice_code:
                                    # في حالة فشل التوليد، استخدم كود افتراضي
                                    invoice_code = f"INV{datetime.now().strftime('%Y%m%d%H%M%S')}"

                                # حفظ الفاتورة الرئيسية مع الكود التسلسلي
                                cursor.execute("""
                                    INSERT INTO invoices (
                                        invoice_code, customer_id, branch_id, employee_id, invoice_date,
                                        subtotal, tax_amount, discount_amount, total_amount,
                                        payment_method, payment_status, notes, responsible_employee
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    invoice_code,  # الكود التسلسلي
                                    customer_id,  # معرف العميل الحقيقي
                                    branch_id,  # معرف الفرع
                                    responsible_employee_id,  # معرف الموظف
                                    datetime.now().date(),  # تاريخ الفاتورة
                                    subtotal,  # المجموع الفرعي
                                    tax_amount,  # الضريبة
                                    discount_amount,  # الخصم
                                    total_amount,  # الإجمالي
                                    'نقدي',  # طريقة الدفع (افتراضي)
                                    'مدفوعة',  # حالة الدفع
                                    f'فاتورة {invoice_code} للعميل {selected_customer} - {len(st.session_state.invoice_items)} عنصر',  # ملاحظات
                                    selected_employee if selected_employee != "اختر الموظف" else "غير محدد"  # الموظف المسؤول
                                ))

                                # الحصول على معرف الفاتورة المحفوظة
                                cursor.execute("SELECT @@IDENTITY")
                                result = cursor.fetchone()
                                invoice_db_id = int(result[0]) if result and result[0] else None

                                # إذا فشل @@IDENTITY، جرب طريقة أخرى
                                if not invoice_db_id:
                                    cursor.execute("""
                                        SELECT TOP 1 invoice_id FROM invoices
                                        WHERE customer_id = ? AND total_amount = ?
                                        ORDER BY created_at DESC
                                    """, (customer_id, total_amount))
                                    result = cursor.fetchone()
                                    invoice_db_id = int(result[0]) if result and result[0] else None

                                if invoice_db_id:
                                    # حفظ عناصر الفاتورة
                                    items_saved = 0
                                    for item in st.session_state.invoice_items:
                                        cursor.execute("""
                                            INSERT INTO invoice_items (
                                                invoice_id, item_type, item_code, item_name,
                                                quantity, unit_price, total_price, employee_name, commission_amount
                                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                        """, (
                                            invoice_db_id,
                                            item['type'],
                                            item['code'],
                                            item['name'],
                                            item['quantity'],
                                            item['price'],
                                            item['total'],
                                            selected_employee if selected_employee != "اختر الموظف" else "غير محدد",
                                            item['total'] * 0.15 if item['type'] == 'service' else 0  # عمولة 15% للخدمات
                                        ))
                                        items_saved += 1

                                    # إضافة المبلغ للخزنة (دائماً للدفع النقدي)
                                    cursor.execute("""
                                        INSERT INTO cash_register (
                                            transaction_type, amount, description, reference_type,
                                            reference_id, branch_id, created_by, payment_method, notes
                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    """, (
                                        'income',  # نوع المعاملة
                                        total_amount,  # المبلغ
                                        f"فاتورة رقم {invoice_db_id} - {selected_customer}",  # الوصف
                                        'invoice',  # نوع المرجع
                                        invoice_db_id,  # معرف المرجع
                                        st.session_state.get('current_branch_id', 1),  # الفرع
                                        st.session_state.user['username'],  # المنشئ
                                        'نقدي',  # طريقة الدفع
                                        f"دفعة نقدية من العميل: {selected_customer} - الموظف: {selected_employee}"  # ملاحظات
                                    ))

                                    conn.commit()
                                    conn.close()

                                    # رسائل النجاح مع الكود التسلسلي
                                    st.success(f"🎉 تم حفظ الفاتورة {invoice_code} (رقم {invoice_db_id}) في قاعدة البيانات بنجاح!")

                                    col1, col2, col3, col4 = st.columns(4)
                                    with col1:
                                        st.metric("🔢 كود الفاتورة", invoice_code)
                                    with col2:
                                        st.metric("📋 عدد العناصر", items_saved)
                                    with col3:
                                        st.metric("💰 المبلغ الإجمالي", f"{total_amount:.2f} ج.م")
                                    with col4:
                                        st.metric("👤 العميل", selected_customer)

                                    st.info(f"💵 تم إضافة {total_amount:.2f} ج.م للخزنة - الفاتورة {invoice_code}")
                                    st.balloons()

                                    # إضافة للقائمة المحلية أيضاً (للعرض)
                                    new_invoice = {
                                        'id': invoice_db_id,
                                        'customer_name': selected_customer,
                                        'date': datetime.now().strftime('%Y-%m-%d'),
                                        'time': datetime.now().strftime('%H:%M'),
                                        'items': st.session_state.invoice_items.copy(),
                                        'subtotal': subtotal,
                                        'discount_amount': discount_amount,
                                        'tax_amount': tax_amount,
                                        'total_amount': total_amount,
                                        'payment_method': 'نقدي',
                                        'status': 'مدفوعة',
                                        'branch_id': st.session_state.get('current_branch_id', 1),
                                        'branch_name': st.session_state.get('current_branch_name', 'الفرع الحالي'),
                                        'created_by': st.session_state.user['username'],
                                        'employee': selected_employee,
                                        'is_deleted': False
                                    }

                                    if 'invoices' not in st.session_state:
                                        st.session_state.invoices = []
                                    st.session_state.invoices.append(new_invoice)
                                else:
                                    st.error("❌ فشل في الحصول على معرف الفاتورة")
                                    conn.close()
                                    raise Exception("فشل في الحصول على معرف الفاتورة")

                        else:
                            # حفظ محلي كنسخة احتياطية
                            if 'invoices' not in st.session_state:
                                st.session_state.invoices = []
                            st.session_state.invoices.append(new_invoice)
                            st.success(f"✅ تم حفظ الفاتورة رقم {invoice_id} محلياً!")

                    except Exception as e:
                        # في حالة فشل قاعدة البيانات، احفظ محلياً
                        if 'invoices' not in st.session_state:
                            st.session_state.invoices = []
                        st.session_state.invoices.append(new_invoice)
                        st.warning(f"⚠️ تم حفظ الفاتورة محلياً - خطأ في قاعدة البيانات")

                    # مسح عناصر الفاتورة
                    st.session_state.invoice_items = []
                    st.rerun()

            with col2:
                if st.button("❌ إلغاء", use_container_width=True):
                    st.session_state.invoice_items = []
                    st.rerun()
        else:
            st.info("ابحث عن خدمة أو منتج لإضافته للفاتورة")














    # عرض الفواتير حسب الفرع الحالي مع تصميم محسن
    if st.session_state.get('show_invoices_list', True):
        current_branch_id = st.session_state.get('current_branch_id', 1)
        current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

        # عنوان مع تصميم احترافي
        st.markdown(f"""
        <div class="invoice-container">
            <div class="invoice-header">
                <h2 style="margin: 0; text-align: center;">📋 فواتير {current_branch_name}</h2>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if 'invoices' in st.session_state and st.session_state.invoices:
            # تصفية الفواتير حسب الفرع الحالي
            branch_invoices = [inv for inv in st.session_state.invoices
                             if inv.get('branch_id') == current_branch_id and not inv.get('is_deleted', False)]

            if branch_invoices:
                # شريط البحث والفلترة
                st.markdown("### 🔍 البحث والفلترة")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    search_term = st.text_input("🔍 البحث", placeholder="رقم الفاتورة أو اسم العميل")

                with col2:
                    date_filter = st.date_input("📅 تاريخ محدد")

                with col3:
                    payment_filter = st.selectbox("💳 طريقة الدفع", ["الكل", "نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية"])

                with col4:
                    sort_by = st.selectbox("📊 ترتيب حسب", ["الأحدث أولاً", "الأقدم أولاً", "المبلغ (تصاعدي)", "المبلغ (تنازلي)"])

                # تطبيق الفلاتر
                filtered_invoices = branch_invoices

                if search_term:
                    filtered_invoices = [inv for inv in filtered_invoices
                                       if search_term.lower() in str(inv.get('id', '')).lower()
                                       or search_term.lower() in inv.get('customer_name', '').lower()]

                if date_filter:
                    filtered_invoices = [inv for inv in filtered_invoices
                                       if inv.get('date') == date_filter.strftime('%Y-%m-%d')]

                if payment_filter != "الكل":
                    filtered_invoices = [inv for inv in filtered_invoices
                                       if inv.get('payment_method') == payment_filter]

                # ترتيب النتائج
                if sort_by == "الأحدث أولاً":
                    filtered_invoices = sorted(filtered_invoices, key=lambda x: x['id'], reverse=True)
                elif sort_by == "الأقدم أولاً":
                    filtered_invoices = sorted(filtered_invoices, key=lambda x: x['id'])
                elif sort_by == "المبلغ (تصاعدي)":
                    filtered_invoices = sorted(filtered_invoices, key=lambda x: x['total_amount'])
                elif sort_by == "المبلغ (تنازلي)":
                    filtered_invoices = sorted(filtered_invoices, key=lambda x: x['total_amount'], reverse=True)

                # إحصائيات سريعة للنتائج المفلترة
                if filtered_invoices:
                    total_sales = sum([inv['total_amount'] for inv in filtered_invoices])
                    today_invoices = [inv for inv in filtered_invoices if inv['date'] == datetime.now().strftime('%Y-%m-%d')]
                    today_sales = sum([inv['total_amount'] for inv in today_invoices])
                    avg_invoice = total_sales / len(filtered_invoices)

                    st.markdown("### 📊 إحصائيات النتائج")
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("عدد الفواتير", len(filtered_invoices))
                    with col2:
                        st.metric("إجمالي المبيعات", f"{total_sales:.2f} ج.م")
                    with col3:
                        st.metric("متوسط الفاتورة", f"{avg_invoice:.2f} ج.م")
                    with col4:
                        st.metric("فواتير اليوم", len(today_invoices))

                    st.divider()

                    # عرض الفواتير في تصميم بطاقات محسن
                    st.markdown("### 📄 الفواتير")

                    for invoice in filtered_invoices:
                        # تحديد لون البطاقة حسب حالة الفاتورة
                        if invoice.get('status') == 'مدفوعة':
                            border_color = "#16a34a"
                            bg_color = "#f0fdf4"
                        elif invoice.get('status') == 'معلقة':
                            border_color = "#d97706"
                            bg_color = "#fffbeb"
                        else:
                            border_color = "#dc2626"
                            bg_color = "#fef2f2"

                        # بطاقة الفاتورة
                        st.markdown(f"""
                        <div style="background: {bg_color}; border: 2px solid {border_color};
                                    border-radius: 12px; padding: 1.5rem; margin: 1rem 0;
                                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                                <h3 style="margin: 0; color: {border_color};">🧾 فاتورة رقم INV-{invoice['id']:04d}</h3>
                                <span style="background: {border_color}; color: white; padding: 0.25rem 0.75rem;
                                            border-radius: 20px; font-size: 0.9rem; font-weight: bold;">
                                    {invoice.get('status', 'غير محدد')}
                                </span>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)

                        # تفاصيل الفاتورة
                        col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

                        with col1:
                            st.markdown("**👤 بيانات العميل:**")
                            st.write(f"الاسم: {invoice['customer_name']}")
                            st.write(f"التاريخ: {invoice['date']}")
                            st.write(f"الوقت: {invoice['time']}")

                        with col2:
                            st.markdown("**💳 بيانات الدفع:**")
                            st.write(f"طريقة الدفع: {invoice['payment_method']}")
                            st.write(f"الكاشير: {invoice.get('created_by', 'غير محدد')}")
                            st.write(f"الفرع: {invoice.get('branch_name', current_branch_name)}")

                        with col3:
                            st.markdown("**📋 تفاصيل العناصر:**")
                            if invoice.get('services'):
                                services_count = len(invoice['services'])
                                st.write(f"الخدمات: {services_count}")
                                for service in list(invoice['services'].keys())[:2]:
                                    st.write(f"• {service}")
                                if services_count > 2:
                                    st.write(f"... و {services_count - 2} خدمات أخرى")

                            if invoice.get('products'):
                                products_count = len(invoice['products'])
                                st.write(f"المنتجات: {products_count}")
                                for product in list(invoice['products'].keys())[:2]:
                                    st.write(f"• {product}")
                                if products_count > 2:
                                    st.write(f"... و {products_count - 2} منتجات أخرى")

                        with col4:
                            st.markdown("**💰 المبلغ الإجمالي:**")
                            st.markdown(f"""
                            <div style="background: white; border: 2px solid {border_color};
                                        border-radius: 8px; padding: 1rem; text-align: center; margin-bottom: 1rem;">
                                <h2 style="margin: 0; color: {border_color}; font-size: 1.5rem;">
                                    {invoice['total_amount']:.2f} ج.م
                                </h2>
                            </div>
                            """, unsafe_allow_html=True)

                            # أزرار العمليات المحسنة
                            if st.button("🖨️ طباعة", key=f"print_invoice_{invoice['id']}", use_container_width=True):
                                show_professional_invoice_print(invoice)

                            if st.button("↩️ مرتجع", key=f"return_invoice_{invoice['id']}", use_container_width=True, type="secondary"):
                                st.session_state[f'show_return_form_{invoice["id"]}'] = True
                                st.rerun()

                            if st.button("🗑️ حذف", key=f"delete_invoice_{invoice['id']}", use_container_width=True, type="secondary"):
                                for i, inv in enumerate(st.session_state.invoices):
                                    if inv['id'] == invoice['id']:
                                        st.session_state.invoices[i]['is_deleted'] = True
                                        st.session_state.invoices[i]['deleted_at'] = datetime.now().isoformat()
                                        st.session_state.invoices[i]['deleted_by'] = st.session_state.user['username']
                                        break

                                st.success(f"تم حذف الفاتورة رقم {invoice['id']} منطقياً!")
                                st.rerun()

                        # نموذج المرتجعات
                        if st.session_state.get(f'show_return_form_{invoice["id"]}', False):
                            show_return_form(invoice)

                        # عرض تفاصيل إضافية
                        with st.expander(f"📋 تفاصيل كاملة - فاتورة {invoice['id']}", expanded=False):
                            col1, col2 = st.columns(2)

                            with col1:
                                st.markdown("**💰 تفاصيل المبالغ:**")
                                st.write(f"المجموع الفرعي: {invoice['subtotal']:.2f} ج.م")
                                st.write(f"الخصم: -{invoice['discount_amount']:.2f} ج.م")
                                st.write(f"الضريبة: +{invoice['tax_amount']:.2f} ج.م")
                                if invoice.get('service_charge', 0) > 0:
                                    st.write(f"رسوم خدمة: +{invoice['service_charge']:.2f} ج.م")
                                st.write(f"**الإجمالي: {invoice['total_amount']:.2f} ج.م**")

                            with col2:
                                if invoice.get('notes'):
                                    st.markdown("**📝 ملاحظات:**")
                                    st.write(invoice['notes'])

                                if invoice.get('employee_commissions'):
                                    st.markdown("**💼 العمولات:**")
                                    for employee, commission_data in invoice['employee_commissions'].items():
                                        st.write(f"👤 {employee}: {commission_data['total_commission']:.2f} ج.م")

                        st.divider()
                else:
                    st.info("لا توجد فواتير تطابق معايير البحث")
            else:
                st.info(f"لا توجد فواتير في {current_branch_name}")
        else:
            st.info("لا توجد فواتير محفوظة")

    # تقرير المبيعات
    if st.session_state.get('show_sales_report', False):
        st.subheader("📊 تقرير المبيعات")

        if 'invoices' in st.session_state and st.session_state.invoices:
            total_sales = sum([inv['total_amount'] for inv in st.session_state.invoices])
            total_invoices = len(st.session_state.invoices)
            avg_invoice = total_sales / total_invoices if total_invoices > 0 else 0

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("إجمالي المبيعات", f"{total_sales:.2f} ج.م")

            with col2:
                st.metric("عدد الفواتير", total_invoices)

            with col3:
                st.metric("متوسط الفاتورة", f"{avg_invoice:.2f} ج.م")

            with col4:
                cash_invoices = len([inv for inv in st.session_state.invoices if inv['payment_method'] == 'نقدي'])
                st.metric("المدفوعات النقدية", cash_invoices)
        else:
            st.info("لا توجد بيانات مبيعات لعرضها")

def returns_page():
    """صفحة إدارة المرتجعات"""
    st.title("↩️ إدارة المرتجعات")

    # الشريط العلوي
    current_branch_id = st.session_state.get('current_branch_id', 1)
    current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                color: white; padding: 1.5rem; border-radius: 15px; margin: 1rem 0;">
        <h2 style="margin: 0; text-align: center;">↩️ نظام إدارة المرتجعات - {current_branch_name}</h2>
        <p style="margin: 0.5rem 0 0 0; text-align: center; opacity: 0.9;">
            إدارة شاملة لجميع عمليات الإرجاع والاسترداد
        </p>
    </div>
    """, unsafe_allow_html=True)

    # تبويبات المرتجعات
    tab1, tab2, tab3, tab4 = st.tabs(["📋 قائمة المرتجعات", "📊 إحصائيات المرتجعات", "🔍 البحث المتقدم", "📈 تقارير المرتجعات"])

    with tab1:
        st.subheader("📋 جميع المرتجعات")

        if 'returns' in st.session_state and st.session_state.returns:
            # تصفية المرتجعات حسب الفرع
            branch_returns = [ret for ret in st.session_state.returns
                            if ret.get('branch_id') == current_branch_id]

            if branch_returns:
                # إحصائيات سريعة
                col1, col2, col3, col4 = st.columns(4)

                total_returns = len(branch_returns)
                total_refund_amount = sum([ret['total_amount'] for ret in branch_returns])
                today_returns = [ret for ret in branch_returns if ret['date'] == datetime.now().strftime('%Y-%m-%d')]
                avg_return_amount = total_refund_amount / total_returns if total_returns > 0 else 0

                with col1:
                    st.metric("إجمالي المرتجعات", total_returns)
                with col2:
                    st.metric("إجمالي المبالغ المستردة", f"{total_refund_amount:.2f} ج.م")
                with col3:
                    st.metric("مرتجعات اليوم", len(today_returns))
                with col4:
                    st.metric("متوسط المرتجع", f"{avg_return_amount:.2f} ج.م")

                st.divider()

                # عرض المرتجعات في بطاقات
                for return_item in sorted(branch_returns, key=lambda x: x['id'], reverse=True):
                    # تحديد لون البطاقة حسب طريقة الاسترداد
                    if return_item['refund_method'] == 'نقدي':
                        border_color = "#16a34a"
                        bg_color = "#f0fdf4"
                    elif return_item['refund_method'] == 'رصيد للعميل':
                        border_color = "#d97706"
                        bg_color = "#fffbeb"
                    elif return_item['refund_method'] == 'استبدال':
                        border_color = "#3b82f6"
                        bg_color = "#eff6ff"
                    else:
                        border_color = "#8b5cf6"
                        bg_color = "#f5f3ff"

                    st.markdown(f"""
                    <div style="background: {bg_color}; border: 2px solid {border_color};
                                border-radius: 12px; padding: 1.5rem; margin: 1rem 0;
                                box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <h3 style="margin: 0; color: {border_color};">↩️ مرتجع رقم RET-{return_item['id']:04d}</h3>
                            <span style="background: {border_color}; color: white; padding: 0.25rem 0.75rem;
                                        border-radius: 20px; font-size: 0.9rem; font-weight: bold;">
                                {return_item['refund_method']}
                            </span>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                    # تفاصيل المرتجع
                    col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

                    with col1:
                        st.markdown("**📋 بيانات المرتجع:**")
                        st.write(f"الفاتورة الأصلية: INV-{return_item['original_invoice_id']:04d}")
                        st.write(f"العميل: {return_item['customer_name']}")
                        st.write(f"التاريخ: {return_item['date']}")
                        st.write(f"الوقت: {return_item['time']}")

                    with col2:
                        st.markdown("**💰 تفاصيل المبلغ:**")
                        st.write(f"المبلغ المسترد: {return_item['total_amount']:.2f} ج.م")
                        st.write(f"طريقة الاسترداد: {return_item['refund_method']}")
                        st.write(f"الحالة: {return_item.get('status', 'مكتمل')}")
                        st.write(f"المسؤول: {return_item['created_by']}")

                    with col3:
                        st.markdown("**📝 السبب والملاحظات:**")
                        st.write(f"سبب الإرجاع: {return_item['reason']}")
                        if return_item.get('notes'):
                            st.write(f"ملاحظات: {return_item['notes']}")
                        st.write(f"عدد العناصر: {len(return_item['items'])}")

                    with col4:
                        st.markdown("**🛍️ العناصر:**")
                        for item in return_item['items'][:2]:  # عرض أول عنصرين فقط
                            st.write(f"• {item['name']}")
                        if len(return_item['items']) > 2:
                            st.write(f"... و {len(return_item['items']) - 2} عناصر أخرى")

                    # تفاصيل إضافية
                    with st.expander(f"📋 تفاصيل كاملة - مرتجع {return_item['id']}", expanded=False):
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("**🛍️ العناصر المرتجعة:**")
                            for item in return_item['items']:
                                st.write(f"• {item['name']} ({item['type']})")
                                st.write(f"  الكمية: {item['quantity']} | السعر: {item['price']:.2f} ج.م | الإجمالي: {item['total']:.2f} ج.م")

                        with col2:
                            if return_item.get('notes'):
                                st.markdown("**📝 ملاحظات إضافية:**")
                                st.write(return_item['notes'])

                    st.divider()
            else:
                st.info(f"لا توجد مرتجعات في {current_branch_name}")
        else:
            st.info("لا توجد مرتجعات محفوظة")

    with tab2:
        st.subheader("📊 إحصائيات المرتجعات")

        if 'returns' in st.session_state and st.session_state.returns:
            branch_returns = [ret for ret in st.session_state.returns
                            if ret.get('branch_id') == current_branch_id]

            if branch_returns:
                # إحصائيات شاملة
                col1, col2, col3 = st.columns(3)

                # تجميع حسب طريقة الاسترداد
                refund_methods = {}
                for ret in branch_returns:
                    method = ret['refund_method']
                    if method not in refund_methods:
                        refund_methods[method] = {'count': 0, 'amount': 0}
                    refund_methods[method]['count'] += 1
                    refund_methods[method]['amount'] += ret['total_amount']

                with col1:
                    st.markdown("**💳 حسب طريقة الاسترداد:**")
                    for method, data in refund_methods.items():
                        st.write(f"• {method}: {data['count']} مرتجع ({data['amount']:.2f} ج.م)")

                # تجميع حسب السبب
                reasons = {}
                for ret in branch_returns:
                    reason = ret['reason']
                    reasons[reason] = reasons.get(reason, 0) + 1

                with col2:
                    st.markdown("**📝 حسب سبب الإرجاع:**")
                    for reason, count in sorted(reasons.items(), key=lambda x: x[1], reverse=True):
                        st.write(f"• {reason}: {count} مرتجع")

                # إحصائيات زمنية
                with col3:
                    st.markdown("**📅 إحصائيات زمنية:**")
                    today_returns = [ret for ret in branch_returns if ret['date'] == datetime.now().strftime('%Y-%m-%d')]
                    this_week_returns = [ret for ret in branch_returns
                                       if (datetime.now() - datetime.strptime(ret['date'], '%Y-%m-%d')).days <= 7]

                    st.write(f"• اليوم: {len(today_returns)} مرتجع")
                    st.write(f"• هذا الأسبوع: {len(this_week_returns)} مرتجع")
                    st.write(f"• الإجمالي: {len(branch_returns)} مرتجع")

                # رسم بياني للمرتجعات
                if PLOTLY_AVAILABLE and len(branch_returns) > 1:
                    import plotly.express as px

                    # مرتجعات حسب التاريخ
                    returns_by_date = {}
                    for ret in branch_returns:
                        date = ret['date']
                        if date not in returns_by_date:
                            returns_by_date[date] = {'count': 0, 'amount': 0}
                        returns_by_date[date]['count'] += 1
                        returns_by_date[date]['amount'] += ret['total_amount']

                    if returns_by_date:
                        dates = list(returns_by_date.keys())
                        amounts = [returns_by_date[date]['amount'] for date in dates]

                        fig = px.bar(x=dates, y=amounts, title="المرتجعات اليومية (المبلغ)")
                        fig.update_layout(
                            xaxis_title="التاريخ",
                            yaxis_title="المبلغ (ج.م)"
                        )
                        st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("لا توجد مرتجعات لعرض الإحصائيات")
        else:
            st.info("لا توجد بيانات مرتجعات")

    with tab3:
        st.subheader("🔍 البحث المتقدم في المرتجعات")

        # فلاتر البحث
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            search_term = st.text_input("🔍 البحث", placeholder="رقم المرتجع أو اسم العميل")

        with col2:
            date_filter = st.date_input("📅 تاريخ محدد")

        with col3:
            refund_method_filter = st.selectbox("💳 طريقة الاسترداد",
                                              ["الكل", "نقدي", "رصيد للعميل", "استبدال", "كارت ائتمان"])

        with col4:
            reason_filter = st.selectbox("📝 سبب الإرجاع",
                                       ["الكل", "عيب في المنتج", "عدم رضا العميل", "خطأ في الطلب", "منتهي الصلاحية", "أخرى"])

        # تطبيق الفلاتر
        if 'returns' in st.session_state and st.session_state.returns:
            branch_returns = [ret for ret in st.session_state.returns
                            if ret.get('branch_id') == current_branch_id]

            filtered_returns = branch_returns

            if search_term:
                filtered_returns = [ret for ret in filtered_returns
                                  if search_term.lower() in str(ret.get('id', '')).lower()
                                  or search_term.lower() in ret.get('customer_name', '').lower()]

            if date_filter:
                filtered_returns = [ret for ret in filtered_returns
                                  if ret.get('date') == date_filter.strftime('%Y-%m-%d')]

            if refund_method_filter != "الكل":
                filtered_returns = [ret for ret in filtered_returns
                                  if ret.get('refund_method') == refund_method_filter]

            if reason_filter != "الكل":
                filtered_returns = [ret for ret in filtered_returns
                                  if ret.get('reason') == reason_filter]

            # عرض النتائج
            if filtered_returns:
                st.markdown(f"### 📋 نتائج البحث ({len(filtered_returns)} مرتجع)")

                for ret in filtered_returns:
                    with st.container():
                        col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

                        with col1:
                            st.write(f"**RET-{ret['id']:04d}** - {ret['customer_name']}")
                            st.write(f"📅 {ret['date']} | ⏰ {ret['time']}")

                        with col2:
                            st.write(f"💰 {ret['total_amount']:.2f} ج.م")
                            st.write(f"💳 {ret['refund_method']}")

                        with col3:
                            st.write(f"📝 {ret['reason']}")
                            st.write(f"👤 {ret['created_by']}")

                        with col4:
                            st.write(f"🛍️ {len(ret['items'])} عناصر")

                        st.divider()
            else:
                st.info("لا توجد مرتجعات تطابق معايير البحث")
        else:
            st.info("لا توجد مرتجعات للبحث فيها")

    with tab4:
        st.subheader("📈 تقارير المرتجعات")

        if 'returns' in st.session_state and st.session_state.returns:
            branch_returns = [ret for ret in st.session_state.returns
                            if ret.get('branch_id') == current_branch_id]

            if branch_returns:
                # تقرير شامل
                st.markdown("### 📊 التقرير الشامل")

                col1, col2, col3, col4 = st.columns(4)

                total_returns = len(branch_returns)
                total_amount = sum([ret['total_amount'] for ret in branch_returns])
                avg_amount = total_amount / total_returns if total_returns > 0 else 0

                # حساب معدل المرتجعات (نسبة إلى الفواتير)
                total_invoices = len(st.session_state.get('invoices', []))
                return_rate = (total_returns / total_invoices * 100) if total_invoices > 0 else 0

                with col1:
                    st.metric("إجمالي المرتجعات", total_returns)
                with col2:
                    st.metric("إجمالي المبالغ", f"{total_amount:.2f} ج.م")
                with col3:
                    st.metric("متوسط المرتجع", f"{avg_amount:.2f} ج.م")
                with col4:
                    st.metric("معدل المرتجعات", f"{return_rate:.1f}%")

                # أكثر الأسباب شيوعاً
                st.markdown("### 📝 أكثر أسباب الإرجاع شيوعاً")
                reasons = {}
                for ret in branch_returns:
                    reason = ret['reason']
                    if reason not in reasons:
                        reasons[reason] = {'count': 0, 'amount': 0}
                    reasons[reason]['count'] += 1
                    reasons[reason]['amount'] += ret['total_amount']

                for reason, data in sorted(reasons.items(), key=lambda x: x[1]['count'], reverse=True):
                    col1, col2, col3 = st.columns([2, 1, 1])
                    with col1:
                        st.write(f"• {reason}")
                    with col2:
                        st.write(f"{data['count']} مرتجع")
                    with col3:
                        st.write(f"{data['amount']:.2f} ج.م")

                # تحليل الاتجاهات
                st.markdown("### 📈 تحليل الاتجاهات")

                # مقارنة بالشهر الماضي
                current_month_returns = [ret for ret in branch_returns
                                       if datetime.strptime(ret['date'], '%Y-%m-%d').month == datetime.now().month]

                st.info(f"📊 مرتجعات هذا الشهر: {len(current_month_returns)} مرتجع بقيمة {sum([ret['total_amount'] for ret in current_month_returns]):.2f} ج.م")

            else:
                st.info("لا توجد مرتجعات لإنشاء التقارير")
        else:
            st.info("لا توجد بيانات مرتجعات")

def reports_page():
    """صفحة التقارير"""
    st.title("📊 التقارير والإحصائيات")

    # تبويبات التقارير
    tab1, tab2, tab3, tab4 = st.tabs(["تقارير المبيعات", "تقارير العملاء", "تقارير المواعيد", "تقارير العمولات"])

    current_branch_id = st.session_state.get('current_branch_id', 1)
    current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

    with tab1:
        st.subheader(f"💰 تقارير المبيعات - {current_branch_name}")

        # إحصائيات المبيعات
        if 'invoices' in st.session_state:
            branch_invoices = [inv for inv in st.session_state.invoices
                             if inv.get('branch_id') == current_branch_id and not inv.get('is_deleted', False)]

            if branch_invoices:
                col1, col2, col3, col4 = st.columns(4)

                total_invoices = len(branch_invoices)
                total_sales = sum([inv['total_amount'] for inv in branch_invoices])
                today_invoices = [inv for inv in branch_invoices if inv['date'] == datetime.now().strftime('%Y-%m-%d')]
                today_sales = sum([inv['total_amount'] for inv in today_invoices])

                with col1:
                    st.metric("إجمالي الفواتير", total_invoices)
                with col2:
                    st.metric("إجمالي المبيعات", f"{total_sales:.2f} ج.م")
                with col3:
                    st.metric("فواتير اليوم", len(today_invoices))
                with col4:
                    st.metric("مبيعات اليوم", f"{today_sales:.2f} ج.م")


                # رسم بياني للمبيعات
                if PLOTLY_AVAILABLE:
                    import plotly.express as px

                    sales_by_date = {}
                    for invoice in branch_invoices:
                        date = invoice['date']
                        if date not in sales_by_date:
                            sales_by_date[date] = 0
                        sales_by_date[date] += invoice['total_amount']

                    if sales_by_date:
                        dates = list(sales_by_date.keys())
                        amounts = list(sales_by_date.values())

                        fig = px.bar(x=dates, y=amounts, title="المبيعات اليومية")
                        fig.update_layout(
                            xaxis_title="التاريخ",
                            yaxis_title="المبلغ (ج.م)"
                        )
                        st.plotly_chart(fig, use_container_width=True)

                # أفضل الخدمات
                st.subheader("🏆 أفضل الخدمات مبيعاً")
                service_sales = {}
                for invoice in branch_invoices:
                    for service_name, service_data in invoice.get('services', {}).items():
                        if service_name not in service_sales:
                            service_sales[service_name] = {'count': 0, 'total': 0}
                        service_sales[service_name]['count'] += service_data.get('quantity', 1)
                        service_sales[service_name]['total'] += service_data.get('total', service_data.get('price', 0))

                if service_sales:
                    sorted_services = sorted(service_sales.items(), key=lambda x: x[1]['total'], reverse=True)
                    for i, (service, data) in enumerate(sorted_services[:5]):
                        col1, col2, col3 = st.columns([2, 1, 1])
                        with col1:
                            st.write(f"{i+1}. {service}")
                        with col2:
                            st.write(f"العدد: {data['count']}")
                        with col3:
                            st.write(f"الإجمالي: {data['total']:.2f} ج.م")
            else:
                st.info("لا توجد فواتير لعرض التقارير")
        else:
            st.info("لا توجد بيانات مبيعات")

    with tab2:
        st.subheader(f"👥 تقارير العملاء - {current_branch_name}")

        if 'customers' in st.session_state:
            branch_customers = [c for c in st.session_state.customers
                              if c.get('branch_id') == current_branch_id and not c.get('is_deleted', False)]

            if branch_customers:
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("إجمالي العملاء", len(branch_customers))

                with col2:
                    male_customers = len([c for c in branch_customers if c.get('gender') == 'ذكر'])
                    st.metric("العملاء الذكور", male_customers)

                with col3:
                    female_customers = len([c for c in branch_customers if c.get('gender') == 'أنثى'])
                    st.metric("العملاء الإناث", female_customers)

                # توزيع العملاء حسب المنطقة
                st.subheader("📍 توزيع العملاء حسب المنطقة")
                areas = {}
                for customer in branch_customers:
                    area = customer.get('area', 'غير محدد')
                    areas[area] = areas.get(area, 0) + 1

                for area, count in areas.items():
                    st.write(f"• {area}: {count} عميل")
            else:
                st.info("لا توجد عملاء لعرض التقارير")
        else:
            st.info("لا توجد بيانات عملاء")

    with tab3:
        st.subheader(f"📅 تقارير المواعيد - {current_branch_name}")

        if 'appointments' in st.session_state:
            branch_appointments = [app for app in st.session_state.appointments
                                 if app.get('branch_id') == current_branch_id and not app.get('is_deleted', False)]

            if branch_appointments:
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("إجمالي المواعيد", len(branch_appointments))

                with col2:
                    confirmed_appointments = len([app for app in branch_appointments if app.get('status') == 'مؤكد'])
                    st.metric("المواعيد المؤكدة", confirmed_appointments)

                with col3:
                    today_appointments = [app for app in branch_appointments if app['date'] == datetime.now().strftime('%Y-%m-%d')]
                    st.metric("مواعيد اليوم", len(today_appointments))
            else:
                st.info("لا توجد مواعيد لعرض التقارير")
        else:
            st.info("لا توجد بيانات مواعيد")

    with tab4:
        st.subheader(f"💼 تقارير العمولات - {current_branch_name}")

        if 'invoices' in st.session_state:
            branch_invoices = [inv for inv in st.session_state.invoices
                             if inv.get('branch_id') == current_branch_id and not inv.get('is_deleted', False)]

            # تجميع العمولات حسب الموظف
            employee_commissions = {}
            total_commissions = 0

            for invoice in branch_invoices:
                commissions = invoice.get('employee_commissions', {})
                for employee, commission_data in commissions.items():
                    if employee not in employee_commissions:
                        employee_commissions[employee] = {
                            'total_sales': 0,
                            'total_commission': 0,
                            'services_count': 0
                        }

                    employee_commissions[employee]['total_sales'] += commission_data['total_sales']
                    employee_commissions[employee]['total_commission'] += commission_data['total_commission']
                    employee_commissions[employee]['services_count'] += len(commission_data['services'])
                    total_commissions += commission_data['total_commission']

            if employee_commissions:
                # إحصائيات العمولات
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("إجمالي العمولات", f"{total_commissions:.2f} ج.م")

                with col2:
                    st.metric("عدد الموظفين", len(employee_commissions))

                with col3:
                    avg_commission = total_commissions / len(employee_commissions) if employee_commissions else 0
                    st.metric("متوسط العمولة", f"{avg_commission:.2f} ج.م")

                st.divider()

                # تفاصيل العمولات لكل موظف
                st.subheader("👤 تفاصيل العمولات حسب الموظف")

                # ترتيب الموظفين حسب العمولة
                sorted_employees = sorted(employee_commissions.items(),
                                        key=lambda x: x[1]['total_commission'], reverse=True)

                for employee, data in sorted_employees:
                    with st.expander(f"💼 {employee}"):
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            st.metric("إجمالي المبيعات", f"{data['total_sales']:.2f} ج.م")

                        with col2:
                            st.metric("إجمالي العمولة", f"{data['total_commission']:.2f} ج.م")

                        with col3:
                            st.metric("عدد الخدمات", data['services_count'])

                        # نسبة العمولة
                        commission_rate = (data['total_commission'] / data['total_sales'] * 100) if data['total_sales'] > 0 else 0
                        st.info(f"📊 نسبة العمولة: {commission_rate:.1f}%")

                # رسم بياني للعمولات
                if PLOTLY_AVAILABLE and len(employee_commissions) > 1:
                    import plotly.express as px

                    employees = list(employee_commissions.keys())
                    commissions = [data['total_commission'] for data in employee_commissions.values()]

                    fig = px.pie(values=commissions, names=employees, title="توزيع العمولات حسب الموظف")
                    st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("لا توجد بيانات عمولات لعرضها")
        else:
            st.info("لا توجد فواتير تحتوي على عمولات")

def settings_page():
    """صفحة الإعدادات"""
    st.title("⚙️ إعدادات النظام")

    # تبويبات الإعدادات
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["إعدادات عامة", "إعدادات الفروع", "إعدادات المالية", "إدارة الصلاحيات", "النسخ الاحتياطي"])

    with tab1:
        st.subheader("🏢 معلومات الصالون")

        with st.form("salon_settings"):
            col1, col2 = st.columns(2)

            with col1:
                salon_name = st.text_input("اسم الصالون", value="SalonProManager")
                salon_phone = st.text_input("رقم الهاتف الرئيسي", value="01090829393")
                salon_email = st.text_input("البريد الإلكتروني", value="<EMAIL>")

            with col2:
                working_hours = st.text_input("ساعات العمل", value="يومياً من 10 صباحاً حتى 12 منتصف الليل")
                currency = st.selectbox("العملة", ["جنيه مصري (ج.م)", "دولار أمريكي ($)", "يورو (€)"], index=0, key="settings_currency")
                language = st.selectbox("اللغة", ["العربية", "English"], index=0, key="settings_language")

            salon_address = st.text_area("العنوان الرئيسي", value="القاهرة، مصر")

            if st.form_submit_button("حفظ الإعدادات العامة"):
                st.success("تم حفظ الإعدادات العامة بنجاح!")

    with tab2:
        st.subheader("🏢 إعدادات الفروع")

        # عرض الفروع الحالية
        branches = make_api_request("/branches")
        if branches:
            st.write("**الفروع الحالية:**")
            for branch in branches:
                with st.expander(f"🏢 {branch['name']}"):
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"📍 العنوان: {branch.get('address', 'غير محدد')}")
                        st.write(f"📞 الهاتف: {branch.get('phone', 'غير محدد')}")
                    with col2:
                        st.write(f"⏰ ساعات العمل: {branch.get('working_hours', 'غير محدد')}")
                        status = "نشط" if branch.get('is_active', True) else "غير نشط"
                        st.write(f"📊 الحالة: {status}")
        else:
            st.info("لا توجد فروع مسجلة")

    with tab3:
        st.subheader("💰 الإعدادات المالية")

        with st.form("financial_settings"):
            col1, col2 = st.columns(2)

            with col1:
                st.write("**الضرائب:**")
                tax_rate = st.number_input("نسبة الضريبة (%)", min_value=0.0, max_value=100.0, value=14.0)
                service_tax = st.checkbox("تطبيق الضريبة على الخدمات", value=True)
                product_tax = st.checkbox("تطبيق الضريبة على المنتجات", value=True)

            with col2:
                st.write("**العمولات:**")
                default_commission = st.number_input("نسبة العمولة الافتراضية (%)", min_value=0.0, max_value=100.0, value=10.0)
                manager_commission = st.number_input("عمولة المدير (%)", min_value=0.0, max_value=100.0, value=5.0)
                specialist_commission = st.number_input("عمولة المتخصص (%)", min_value=0.0, max_value=100.0, value=15.0)

            st.write("**طرق الدفع المتاحة:**")
            payment_methods = st.multiselect(
                "اختر طرق الدفع",
                ["نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية", "فيزا", "ماستركارد"],
                default=["نقدي", "كارت", "محفظة إلكترونية"]
            )

            if st.form_submit_button("حفظ الإعدادات المالية"):
                st.success("تم حفظ الإعدادات المالية بنجاح!")

    with tab4:
        st.subheader("🔐 إدارة صلاحيات المستخدمين")

        # إضافة مستخدم جديد
        with st.expander("➕ إضافة مستخدم جديد", expanded=False):
            with st.form("add_user_form"):
                col1, col2 = st.columns(2)

                with col1:
                    new_username = st.text_input("اسم المستخدم *")
                    new_password = st.text_input("كلمة المرور *", type="password")
                    full_name = st.text_input("الاسم الكامل *")
                    email = st.text_input("البريد الإلكتروني")

                with col2:
                    user_role = st.selectbox("الدور", [
                        "super_admin", "branch_manager", "cashier",
                        "specialist", "receptionist"
                    ], format_func=lambda x: {
                        "super_admin": "مدير عام",
                        "branch_manager": "مدير فرع",
                        "cashier": "كاشير",
                        "specialist": "متخصص",
                        "receptionist": "موظف استقبال"
                    }[x])

                    # اختيار الفروع المتاحة من قاعدة البيانات
                    try:
                        # جلب الفروع من قاعدة البيانات
                        branches_query = "SELECT name FROM branches WHERE is_active = 1"
                        branches_result = execute_query(branches_query)

                        if branches_result:
                            branch_options = [branch['name'] for branch in branches_result]
                            # إضافة خيار الإدارة العامة
                            if "الإدارة العامة" not in branch_options:
                                branch_options.insert(0, "الإدارة العامة")
                        else:
                            # فروع افتراضية إذا لم توجد في قاعدة البيانات
                            branch_options = ["الإدارة العامة", "الفرع الرئيسي", "فرع المعادي", "فرع مدينة نصر"]
                    except:
                        # فروع افتراضية في حالة الخطأ
                        branch_options = ["الإدارة العامة", "الفرع الرئيسي", "فرع المعادي", "فرع مدينة نصر"]

                    available_branches = st.multiselect(
                        "الفروع المتاحة",
                        branch_options,
                        help="اختر الفروع التي يمكن للمستخدم الوصول إليها"
                    )

                if st.form_submit_button("إضافة المستخدم"):
                    if new_username and new_password and full_name:
                        # حفظ المستخدم الجديد
                        if 'users' not in st.session_state:
                            st.session_state.users = []

                        new_user = {
                            'username': new_username,
                            'full_name': full_name,
                            'email': email,
                            'role': user_role,
                            'branches': available_branches,
                            'is_active': True,
                            'created_at': datetime.now().isoformat()
                        }

                        st.session_state.users.append(new_user)
                        st.success(f"تم إضافة المستخدم {full_name} بنجاح!")
                    else:
                        st.error("يرجى ملء جميع الحقول المطلوبة")

        # عرض المستخدمين الحاليين
        st.subheader("👥 المستخدمين الحاليين")

        # المستخدمين الافتراضيين
        default_users = [
            {
                'username': 'admin',
                'full_name': 'المدير العام',
                'role': 'super_admin',
                'branches': ['جميع الفروع'],
                'is_active': True,
                'is_default': True
            }
        ]

        # دمج المستخدمين الافتراضيين مع المضافين
        all_users = default_users.copy()
        if 'users' in st.session_state:
            all_users.extend(st.session_state.users)

        for user in all_users:
            with st.container():
                col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

                with col1:
                    st.write(f"**{user['full_name']}**")
                    st.write(f"👤 {user['username']}")

                with col2:
                    role_names = {
                        "super_admin": "مدير عام",
                        "branch_manager": "مدير فرع",
                        "cashier": "كاشير",
                        "specialist": "متخصص",
                        "receptionist": "موظف استقبال"
                    }
                    st.write(f"💼 {role_names.get(user['role'], user['role'])}")
                    status = "نشط" if user.get('is_active', True) else "غير نشط"
                    st.write(f"📊 {status}")

                with col3:
                    branches_text = ", ".join(user.get('branches', []))
                    st.write(f"🏢 **الفروع:**")
                    st.write(branches_text)

                with col4:
                    if not user.get('is_default', False):
                        if st.button("تعديل", key=f"edit_user_{user['username']}", use_container_width=True):
                            st.info("وظيفة التعديل قيد التطوير")

                        if st.button("حذف", key=f"delete_user_{user['username']}", use_container_width=True):
                            # حذف المستخدم
                            st.session_state.users = [u for u in st.session_state.users if u['username'] != user['username']]
                            st.success(f"تم حذف المستخدم {user['full_name']}")
                            st.rerun()

                st.divider()

        # عرض الصلاحيات حسب الدور
        st.subheader("📋 الصلاحيات حسب الأدوار")

        permissions_info = {
            "super_admin": {
                "name": "مدير عام",
                "permissions": ["جميع الصلاحيات", "إدارة المستخدمين", "إدارة الفروع", "التقارير المالية"]
            },
            "branch_manager": {
                "name": "مدير فرع",
                "permissions": ["إدارة العملاء", "إدارة المواعيد", "المبيعات", "تقارير الفرع", "إدارة الموظفين"]
            },
            "cashier": {
                "name": "كاشير",
                "permissions": ["إنشاء فواتير", "عرض العملاء", "عرض المنتجات", "طباعة الفواتير"]
            },
            "specialist": {
                "name": "متخصص",
                "permissions": ["عرض المواعيد", "تعديل المواعيد", "عرض العملاء", "عرض الخدمات"]
            },
            "receptionist": {
                "name": "موظف استقبال",
                "permissions": ["حجز المواعيد", "إضافة العملاء", "عرض الخدمات", "البحث"]
            }
        }

        for role, info in permissions_info.items():
            with st.expander(f"👤 {info['name']}"):
                st.write("**الصلاحيات المتاحة:**")
                for permission in info['permissions']:
                    st.write(f"✅ {permission}")

    with tab5:
        st.subheader("💾 النسخ الاحتياطي والاستعادة")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**إنشاء نسخة احتياطية:**")
            if st.button("إنشاء نسخة احتياطية الآن", use_container_width=True):
                with st.spinner("جاري إنشاء النسخة الاحتياطية..."):
                    # محاكاة عملية النسخ الاحتياطي
                    import time
                    time.sleep(2)
                st.success("تم إنشاء النسخة الاحتياطية بنجاح!")

            auto_backup = st.checkbox("النسخ الاحتياطي التلقائي", value=True)
            if auto_backup:
                backup_frequency = st.selectbox("تكرار النسخ", ["يومي", "أسبوعي", "شهري"], index=0, key="backup_frequency_select")

        with col2:
            st.write("**استعادة النسخة الاحتياطية:**")
            uploaded_file = st.file_uploader("اختر ملف النسخة الاحتياطية", type=['sql', 'bak'])
            if uploaded_file:
                if st.button("استعادة النسخة الاحتياطية", use_container_width=True):
                    st.warning("تأكد من أن هذا الإجراء سيحل محل البيانات الحالية!")
                    if st.button("تأكيد الاستعادة"):
                        st.success("تم استعادة النسخة الاحتياطية بنجاح!")

    # معلومات النظام
    st.divider()
    st.subheader("ℹ️ معلومات النظام")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("إصدار النظام", "2.0.0")

    with col2:
        st.metric("قاعدة البيانات", "SQL Server")

    with col3:
        st.metric("آخر تحديث", "2024-01-15")

    with col4:
        st.metric("حالة النظام", "يعمل بشكل طبيعي", delta="✅")

    # معلومات الاتصال والدعم
    st.divider()
    st.subheader("📞 الدعم والمساعدة")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**للدعم الفني:**")
        st.write("📞 هاتف: 01090829393")
        st.write("📧 إيميل: <EMAIL>")
        st.write("💬 واتساب: https://wa.me/201090829393")

    with col2:
        st.write("**روابط مفيدة:**")
        st.write("📖 [دليل المستخدم](#)")
        st.write("🎥 [فيديوهات تعليمية](#)")
        st.write("❓ [الأسئلة الشائعة](#)")
        st.write("🔄 [تحديثات النظام](#)")

def show_cash_register_page():
    """صفحة الخزنة المحدثة"""
    st.title("💰 إدارة الخزنة")

    current_branch_id = st.session_state.get('current_branch_id', 1)
    current_branch_name = st.session_state.get('current_branch_name', 'الفرع الحالي')

    # عرض رصيد الخزنة
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor()

            # حساب رصيد الخزنة
            cursor.execute("""
                SELECT SUM(
                    CASE
                        WHEN transaction_type = 'income' THEN amount
                        WHEN transaction_type = 'expense' THEN -amount
                        ELSE 0
                    END
                ) as balance
                FROM cash_register
                WHERE branch_id = ? AND is_deleted = 0
            """, (current_branch_id,))

            balance_result = cursor.fetchone()
            current_balance = balance_result[0] if balance_result and balance_result[0] else 0

            # الشريط العلوي مع الرصيد
            st.markdown(f"""
            <div style="background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                        color: white; padding: 2rem; border-radius: 15px; margin-bottom: 2rem;
                        box-shadow: 0 8px 32px rgba(44, 62, 80, 0.3);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h2 style="margin: 0; color: white;">💰 إدارة الخزنة - {current_branch_name}</h2>
                        <p style="margin: 0; opacity: 0.9;">تتبع جميع المعاملات النقدية</p>
                    </div>
                    <div style="text-align: center; background: rgba(255,255,255,0.2);
                                padding: 1.5rem; border-radius: 15px;">
                        <h3 style="margin: 0; color: white;">الرصيد النقدي الحالي</h3>
                        <h1 style="margin: 0.5rem 0 0 0; color: #f1c40f; font-size: 2.5rem;
                                   text-shadow: 0 2px 4px rgba(0,0,0,0.3);">{current_balance:.2f} ج.م</h1>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            # تبويبات الخزنة
            tab1, tab2, tab3 = st.tabs(["📋 المعاملات", "💸 إضافة مصروف", "📊 التقارير"])

            with tab1:
                st.subheader("📋 آخر المعاملات")

                # عرض آخر المعاملات
                cursor.execute("""
                    SELECT TOP 20
                        transaction_type, amount, description, transaction_date,
                        created_by, payment_method, reference_type
                    FROM cash_register
                    WHERE branch_id = ? AND is_deleted = 0
                    ORDER BY transaction_date DESC
                """, (current_branch_id,))

                transactions = cursor.fetchall()

                if transactions:
                    for transaction in transactions:
                        transaction_type = transaction[0]
                        amount = transaction[1]
                        description = transaction[2]
                        date = transaction[3]
                        user = transaction[4]
                        payment_method = transaction[5]
                        reference_type = transaction[6]

                        icon = "💰" if transaction_type == 'income' else "💸"
                        color = "#2ecc71" if transaction_type == 'income' else "#e74c3c"
                        sign = "+" if transaction_type == 'income' else "-"

                        st.markdown(f"""
                        <div style="background: white; border-left: 4px solid {color};
                                    padding: 1rem; margin: 0.5rem 0; border-radius: 8px;
                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>{icon} {description}</strong><br>
                                    <small style="color: #666;">
                                        📅 {date} | 👤 {user} | 💳 {payment_method} | 📋 {reference_type}
                                    </small>
                                </div>
                                <div style="text-align: right;">
                                    <h3 style="margin: 0; color: {color};">
                                        {sign}{amount:.2f} ج.م
                                    </h3>
                                </div>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)
                else:
                    st.info("لا توجد معاملات في الخزنة")

            with tab2:
                st.subheader("💸 إضافة مصروف جديد")

                with st.form("expense_form"):
                    col1, col2 = st.columns(2)

                    with col1:
                        expense_amount = st.number_input("المبلغ (ج.م)", min_value=0.01, step=0.01)
                        expense_description = st.text_input("وصف المصروف")

                    with col2:
                        expense_category = st.selectbox("نوع المصروف", [
                            "فواتير", "مواد خام", "صيانة", "رواتب", "إيجار", "أخرى"
                        ])
                        expense_notes = st.text_area("ملاحظات إضافية")

                    if st.form_submit_button("💾 حفظ المصروف", use_container_width=True):
                        if expense_amount > 0 and expense_description:
                            try:
                                cursor.execute("""
                                    INSERT INTO cash_register (
                                        transaction_type, amount, description, reference_type,
                                        branch_id, created_by, payment_method, notes
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    'expense',
                                    expense_amount,
                                    f"{expense_category}: {expense_description}",
                                    'expense',
                                    current_branch_id,
                                    st.session_state.user['username'],
                                    'نقدي',
                                    expense_notes
                                ))

                                conn.commit()
                                st.success(f"✅ تم حفظ المصروف بقيمة {expense_amount:.2f} ج.م")
                                st.rerun()
                            except Exception as e:
                                st.error(f"❌ خطأ في حفظ المصروف: {str(e)}")
                        else:
                            st.error("⚠️ يرجى ملء جميع الحقول المطلوبة")

            with tab3:
                st.subheader("📊 تقارير الخزنة")

                # إحصائيات سريعة
                col1, col2, col3 = st.columns(3)

                # إجمالي الإيرادات
                cursor.execute("""
                    SELECT SUM(amount) FROM cash_register
                    WHERE branch_id = ? AND transaction_type = 'income' AND is_deleted = 0
                """, (current_branch_id,))
                total_income = cursor.fetchone()[0] or 0

                # إجمالي المصروفات
                cursor.execute("""
                    SELECT SUM(amount) FROM cash_register
                    WHERE branch_id = ? AND transaction_type = 'expense' AND is_deleted = 0
                """, (current_branch_id,))
                total_expenses = cursor.fetchone()[0] or 0

                # عدد المعاملات
                cursor.execute("""
                    SELECT COUNT(*) FROM cash_register
                    WHERE branch_id = ? AND is_deleted = 0
                """, (current_branch_id,))
                total_transactions = cursor.fetchone()[0] or 0

                with col1:
                    st.metric("💰 إجمالي الإيرادات", f"{total_income:.2f} ج.م")

                with col2:
                    st.metric("💸 إجمالي المصروفات", f"{total_expenses:.2f} ج.م")

                with col3:
                    st.metric("📋 عدد المعاملات", total_transactions)

            conn.close()
        else:
            st.error("❌ فشل الاتصال بقاعدة البيانات")

    except Exception as e:
        st.error(f"❌ خطأ في جلب بيانات الخزنة: {str(e)}")

def database_query_page():
    """صفحة استعلام قاعدة البيانات"""
    st.title("🗄️ استعلام قاعدة البيانات")

    # التحقق من حالة قاعدة البيانات
    if not st.session_state.get('db_initialized', False):
        st.error("❌ قاعدة البيانات غير متصلة!")
        st.info("يرجى التأكد من إعدادات الاتصال بقاعدة البيانات")
        return

    st.success("✅ قاعدة البيانات متصلة بنجاح!")

    # أزرار اختبار وإدارة قاعدة البيانات
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🔍 اختبار الاتصال", use_container_width=True):
            try:
                conn = get_db_connection()
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    conn.close()
                    if result:
                        st.success("✅ الاتصال يعمل بشكل ممتاز!")
                    else:
                        st.error("❌ مشكلة في الاتصال")
                else:
                    st.error("❌ فشل في الاتصال")
            except Exception as e:
                st.error(f"❌ خطأ في الاتصال: {str(e)}")

    with col2:
        if st.button("📊 عرض حالة الجداول", use_container_width=True):
            try:
                # عرض عدد السجلات في كل جدول
                tables_info = {}

                tables = ['branches', 'users', 'customers', 'services', 'products', 'invoices', 'invoice_items', 'cash_transactions']

                for table in tables:
                    try:
                        query = f"SELECT COUNT(*) as count FROM {table}"
                        result = execute_query(query)
                        if result:
                            tables_info[table] = result[0]['count']
                        else:
                            tables_info[table] = 0
                    except:
                        tables_info[table] = "خطأ"

                st.markdown("### 📋 حالة الجداول:")
                for table, count in tables_info.items():
                    if count == "خطأ":
                        st.error(f"❌ {table}: خطأ في الوصول")
                    elif count == 0:
                        st.warning(f"⚠️ {table}: فارغ ({count} سجل)")
                    else:
                        st.success(f"✅ {table}: {count} سجل")

            except Exception as e:
                st.error(f"❌ خطأ في عرض حالة الجداول: {str(e)}")

    with col3:
        if st.button("🔧 إصلاح قاعدة البيانات", use_container_width=True):
            with st.spinner("جاري إصلاح قاعدة البيانات..."):
                try:
                    # إضافة الأعمدة المفقودة مع معالجة أفضل للأخطاء
                    alter_queries = [
                        {
                            'name': 'إضافة branch_id للعملاء',
                            'query': "ALTER TABLE customers ADD branch_id INT DEFAULT 1"
                        },
                        {
                            'name': 'إضافة branch_id للفواتير',
                            'query': "ALTER TABLE invoices ADD branch_id INT DEFAULT 1"
                        },
                        {
                            'name': 'إضافة branch_id للمعاملات النقدية',
                            'query': "ALTER TABLE cash_transactions ADD branch_id INT DEFAULT 1"
                        },
                        {
                            'name': 'إضافة is_deleted للعملاء',
                            'query': "ALTER TABLE customers ADD is_deleted BIT DEFAULT 0"
                        },
                        {
                            'name': 'إضافة is_deleted للفواتير',
                            'query': "ALTER TABLE invoices ADD is_deleted BIT DEFAULT 0"
                        }
                    ]

                    success_count = 0
                    for item in alter_queries:
                        try:
                            # التحقق من وجود العمود أولاً
                            table_name = item['query'].split()[2]  # استخراج اسم الجدول
                            column_name = item['query'].split()[4]  # استخراج اسم العمود

                            check_query = f"""
                            SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
                            WHERE TABLE_NAME = '{table_name}' AND COLUMN_NAME = '{column_name}'
                            """

                            result = execute_query(check_query)
                            if result and result[0]['count'] == 0:
                                # العمود غير موجود، أضفه
                                execute_query(item['query'], fetch=False)
                                st.success(f"✅ {item['name']}")
                                success_count += 1
                            else:
                                st.info(f"ℹ️ {item['name']} - موجود مسبقاً")

                        except Exception as e:
                            if "already exists" in str(e) or "Duplicate column name" in str(e):
                                st.info(f"ℹ️ {item['name']} - موجود مسبقاً")
                            else:
                                st.warning(f"⚠️ {item['name']}: {str(e)}")

                    if success_count > 0:
                        st.success(f"🎉 تم إضافة {success_count} عمود جديد بنجاح!")
                    else:
                        st.info("💡 جميع الأعمدة موجودة مسبقاً")

                    st.success("✅ قاعدة البيانات جاهزة للاستخدام!")

                except Exception as e:
                    st.error(f"❌ فشل في إصلاح قاعدة البيانات: {str(e)}")

    st.divider()

    # أزرار إدارة قاعدة البيانات المتقدمة
    col1_adv, col2_adv, col3_adv = st.columns(3)

    with col1_adv:
        if st.button("🔄 إعادة تهيئة الجداول", use_container_width=True):
            try:
                with st.spinner("جاري إعادة تهيئة الجداول..."):
                    init_database()
                st.success("✅ تم إعادة تهيئة الجداول بنجاح!")
            except Exception as e:
                st.error(f"❌ خطأ في إعادة التهيئة: {str(e)}")

    with col2_adv:
        if st.button("🗑️ إنشاء قاعدة البيانات من جديد", use_container_width=True, type="secondary"):
            st.session_state.show_recreate_confirm = True

    with col3_adv:
        if st.button("📊 تقرير شامل عن قاعدة البيانات", use_container_width=True):
            try:
                conn = get_db_connection()
                if conn:
                    cursor = conn.cursor()

                    st.markdown("### 📊 تقرير شامل عن قاعدة البيانات")

                    # معلومات عامة
                    cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'")
                    tables_count = cursor.fetchone()[0]

                    st.info(f"📋 إجمالي الجداول: {tables_count}")

                    # تفاصيل كل جدول
                    tables = ['branches', 'customers', 'employees', 'invoices', 'invoice_items']

                    for table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]

                            if table == 'invoices':
                                cursor.execute(f"SELECT SUM(total_amount) FROM {table} WHERE is_deleted = 0")
                                total_sales = cursor.fetchone()[0] or 0
                                st.metric(f"📋 {table}", f"{count} سجل", f"إجمالي المبيعات: {total_sales:.2f} ج.م")
                            else:
                                st.metric(f"📋 {table}", f"{count} سجل")
                        except:
                            st.error(f"❌ خطأ في جدول {table}")

                    conn.close()
                else:
                    st.error("❌ فشل الاتصال بقاعدة البيانات")
            except Exception as e:
                st.error(f"❌ خطأ في إنشاء التقرير: {str(e)}")

    # نافذة تأكيد إعادة إنشاء قاعدة البيانات
    if st.session_state.get('show_recreate_confirm', False):
        st.markdown("---")
        st.markdown("### ⚠️ تأكيد إعادة إنشاء قاعدة البيانات")

        st.error("**تحذير: هذه العملية ستحذف جميع البيانات الموجودة نهائياً!**")

        st.markdown("**ما سيحدث:**")
        st.write("• حذف جميع الجداول الموجودة")
        st.write("• إنشاء جداول جديدة بهيكل محدث")
        st.write("• إصلاح مشاكل التواريخ (من 1970)")
        st.write("• إضافة أسماء عربية وإنجليزية للعملاء")
        st.write("• إضافة حقل 'عرفتنا عن طريق'")
        st.write("• إدراج بيانات افتراضية")

        col1_confirm, col2_confirm, col3_confirm = st.columns(3)

        with col1_confirm:
            if st.button("✅ نعم، أنشئ قاعدة البيانات من جديد", type="primary"):
                with st.spinner("جاري إنشاء قاعدة البيانات من جديد..."):
                    success = recreate_database_from_scratch()
                    if success:
                        st.session_state.db_initialized = True
                        st.session_state.using_local_data = False
                        st.session_state.show_recreate_confirm = False
                        st.rerun()

        with col2_confirm:
            if st.button("❌ إلغاء", type="secondary"):
                st.session_state.show_recreate_confirm = False
                st.rerun()

        with col3_confirm:
            st.write("") # مساحة فارغة

    st.divider()

    # تبويبات مختلفة للاستعلامات
    tab1, tab2, tab3, tab4 = st.tabs(["📋 الفواتير", "👥 العملاء", "🏢 الفروع", "💰 المعاملات النقدية"])

    with tab1:
        st.subheader("📋 استعلام الفواتير")

        # زر لنقل الفواتير المحلية إلى قاعدة البيانات
        if st.button("🔄 نقل الفواتير المحلية إلى قاعدة البيانات", type="primary"):
            if 'invoices' in st.session_state and st.session_state.invoices:
                success_count = 0
                error_count = 0

                with st.spinner('جاري نقل الفواتير...'):
                    for invoice in st.session_state.invoices:
                        if not invoice.get('is_deleted', False) and not invoice.get('db_id'):
                            try:
                                # إعداد بيانات الفاتورة
                                invoice_data = {
                                    'customer_id': invoice.get('customer_id', 1),
                                    'branch_id': invoice.get('branch_id', 1),
                                    'subtotal': invoice.get('subtotal', 0),
                                    'discount_type': 'percentage',
                                    'discount_value': 0,
                                    'discount_amount': invoice.get('discount_amount', 0),
                                    'tax_rate': 14.0,
                                    'tax_amount': invoice.get('tax_amount', 0),
                                    'service_charge': invoice.get('service_charge', 0),
                                    'total_amount': invoice.get('total_amount', 0),
                                    'payment_method': invoice.get('payment_method', 'نقدي'),
                                    'status': 'completed',
                                    'notes': invoice.get('notes', ''),
                                    'created_by': invoice.get('created_by', 'admin')
                                }

                                # إعداد عناصر الفاتورة
                                items_data = []

                                # إضافة الخدمات
                                for service_name, service_data in invoice.get('services', {}).items():
                                    items_data.append({
                                        'type': 'service',
                                        'id': 0,
                                        'name': service_name,
                                        'quantity': service_data.get('quantity', 1),
                                        'price': service_data.get('price', 0),
                                        'total': service_data.get('total', 0),
                                        'employee': service_data.get('employee', ''),
                                        'commission_rate': 15.0,
                                        'commission_amount': service_data.get('total', 0) * 0.15
                                    })

                                # إضافة المنتجات
                                for product_name, product_data in invoice.get('products', {}).items():
                                    items_data.append({
                                        'type': 'product',
                                        'id': 0,
                                        'name': product_name,
                                        'quantity': product_data.get('quantity', 1),
                                        'price': product_data.get('price', 0),
                                        'total': product_data.get('total', 0),
                                        'employee': '',
                                        'commission_rate': 0,
                                        'commission_amount': 0
                                    })

                                # حفظ في قاعدة البيانات
                                db_invoice_id = save_invoice_to_db(invoice_data, items_data)

                                if db_invoice_id:
                                    invoice['db_id'] = db_invoice_id
                                    success_count += 1
                                else:
                                    error_count += 1

                            except Exception as e:
                                st.error(f"خطأ في نقل الفاتورة {invoice.get('id')}: {str(e)}")
                                error_count += 1

                if success_count > 0:
                    st.success(f"✅ تم نقل {success_count} فاتورة بنجاح إلى قاعدة البيانات!")
                if error_count > 0:
                    st.warning(f"⚠️ فشل في نقل {error_count} فاتورة")
            else:
                st.info("لا توجد فواتير محلية لنقلها")

        st.divider()

        col1, col2 = st.columns([1, 2])

        with col1:
            # فلاتر البحث
            st.markdown("**🔍 فلاتر البحث:**")

            # اختيار الفرع
            branches = get_branches()
            if branches:
                branch_options = {f"{b['name']} (ID: {b['branch_id']})": b['branch_id'] for b in branches}
                selected_branch = st.selectbox("اختر الفرع", ["جميع الفروع"] + list(branch_options.keys()))
                branch_id = branch_options.get(selected_branch) if selected_branch != "جميع الفروع" else None
            else:
                branch_id = None
                st.warning("لا توجد فروع في قاعدة البيانات")

            # فترة زمنية
            date_from = st.date_input("من تاريخ", value=datetime.now().date())
            date_to = st.date_input("إلى تاريخ", value=datetime.now().date())

            # طريقة الدفع
            payment_method = st.selectbox("طريقة الدفع", ["الكل", "نقدي", "كارت", "تحويل بنكي", "محفظة إلكترونية"])

            if st.button("🔍 بحث", use_container_width=True):
                st.session_state.search_invoices = True

        with col2:
            st.markdown("**📊 نتائج البحث:**")

            # أزرار الاختبار
            col1_test, col2_test = st.columns(2)

            with col1_test:
                if st.button("📋 عرض آخر 10 فواتير", use_container_width=True):
                    st.session_state.show_recent_invoices = True

            with col2_test:
                if st.button("🔍 اختبار قاعدة البيانات", use_container_width=True):
                    st.session_state.test_database = True

            # اختبار قاعدة البيانات
            if st.session_state.get('test_database', False):
                st.markdown("### 🔍 اختبار الاتصال بقاعدة البيانات")

                try:
                    conn = get_db_connection()
                    if conn:
                        st.success("✅ الاتصال بقاعدة البيانات ناجح")

                        # اختبار عدد الفواتير
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) as count FROM invoices WHERE is_deleted = 0")
                        count_result = cursor.fetchone()
                        invoice_count = count_result[0] if count_result else 0

                        st.info(f"📊 عدد الفواتير في قاعدة البيانات: {invoice_count}")

                        # اختبار عدد عناصر الفواتير
                        cursor.execute("SELECT COUNT(*) as count FROM invoice_items")
                        items_result = cursor.fetchone()
                        items_count = items_result[0] if items_result else 0

                        st.info(f"📦 عدد عناصر الفواتير: {items_count}")

                        conn.close()
                    else:
                        st.error("❌ فشل الاتصال بقاعدة البيانات")

                except Exception as e:
                    st.error(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")

                st.session_state.test_database = False

            # عرض آخر الفواتير
            if st.session_state.get('show_recent_invoices', False):
                query = """
                SELECT TOP 10
                    i.invoice_id,
                    i.customer_id,
                    i.branch_id,
                    i.employee_id,
                    i.invoice_date,
                    i.subtotal,
                    i.tax_amount,
                    i.discount_amount,
                    i.total_amount,
                    i.payment_method,
                    i.payment_status,
                    i.notes,
                    i.created_at
                FROM invoices i
                WHERE i.is_deleted = 0
                ORDER BY i.created_at DESC
                """

                recent_invoices = execute_query(query)

                if recent_invoices:
                    st.success(f"✅ آخر {len(recent_invoices)} فاتورة:")

                    for invoice in recent_invoices:
                        st.markdown(f"""
                        <div style="background: #f8f9fa; border: 1px solid #dee2e6;
                                    border-radius: 8px; padding: 1rem; margin: 0.5rem 0;">
                            <strong>🧾 فاتورة رقم {invoice['invoice_id']}</strong><br>
                            👤 العميل: {invoice['customer_id']}<br>
                            🏢 الفرع: {invoice['branch_id']}<br>
                            👨‍💼 الموظف: {invoice['employee_id']}<br>
                            💰 المبلغ: {invoice['total_amount']:.2f} ج.م<br>
                            💳 الدفع: {invoice['payment_method']}<br>
                            📅 التاريخ: {invoice['invoice_date']}<br>
                            ✅ الحالة: {invoice['payment_status']}
                        </div>
                        """, unsafe_allow_html=True)
                else:
                    st.warning("⚠️ لا توجد فواتير في قاعدة البيانات")

                st.session_state.show_recent_invoices = False

            if st.session_state.get('search_invoices', False):
                # بناء الاستعلام
                query = """
                SELECT
                    i.invoice_id,
                    i.customer_id,
                    i.branch_id,
                    i.employee_id,
                    i.invoice_date,
                    i.subtotal,
                    i.tax_amount,
                    i.discount_amount,
                    i.total_amount,
                    i.payment_method,
                    i.payment_status,
                    i.notes,
                    i.created_at
                FROM invoices i
                WHERE i.is_deleted = 0
                """

                params = []

                # إضافة فلاتر
                if branch_id:
                    query += " AND i.branch_id = ?"
                    params.append(branch_id)

                if payment_method != "الكل":
                    query += " AND i.payment_method = ?"
                    params.append(payment_method)

                query += " AND CAST(i.created_at AS DATE) BETWEEN ? AND ?"
                params.extend([date_from, date_to])

                query += " ORDER BY i.created_at DESC"

                # تنفيذ الاستعلام
                invoices = execute_query(query, params)

                if invoices:
                    st.success(f"✅ تم العثور على {len(invoices)} فاتورة")

                    # إحصائيات سريعة
                    total_amount = sum([inv['total_amount'] for inv in invoices])
                    avg_amount = total_amount / len(invoices)

                    col1_stats, col2_stats, col3_stats = st.columns(3)
                    with col1_stats:
                        st.metric("إجمالي المبلغ", f"{total_amount:.2f} ج.م")
                    with col2_stats:
                        st.metric("متوسط الفاتورة", f"{avg_amount:.2f} ج.م")
                    with col3_stats:
                        st.metric("عدد الفواتير", len(invoices))

                    # عرض الفواتير
                    for invoice in invoices:
                        with st.expander(f"فاتورة رقم {invoice['invoice_id']} - عميل {invoice['customer_id']} - {invoice['total_amount']:.2f} ج.م"):
                            col1_inv, col2_inv = st.columns(2)

                            with col1_inv:
                                st.write(f"**رقم الفاتورة:** {invoice['invoice_id']}")
                                st.write(f"**العميل:** {invoice['customer_id']}")
                                st.write(f"**الموظف:** {invoice['employee_id']}")
                                st.write(f"**الفرع:** {invoice['branch_id']}")
                                st.write(f"**المبلغ:** {invoice['total_amount']:.2f} ج.م")

                            with col2_inv:
                                st.write(f"**طريقة الدفع:** {invoice['payment_method']}")
                                st.write(f"**الحالة:** {invoice['payment_status']}")
                                st.write(f"**تاريخ الفاتورة:** {invoice['invoice_date']}")
                                st.write(f"**تاريخ الإنشاء:** {invoice['created_at']}")

                            # جلب عناصر الفاتورة
                            items_query = "SELECT * FROM invoice_items WHERE invoice_id = ?"
                            items = execute_query(items_query, (invoice['invoice_id'],))

                            if items:
                                st.markdown("**📋 عناصر الفاتورة:**")
                                items_df = pd.DataFrame(items)
                                st.dataframe(items_df[['item_name', 'quantity', 'unit_price', 'total_price']], use_container_width=True)
                else:
                    st.warning("⚠️ لم يتم العثور على فواتير تطابق معايير البحث")

    with tab2:
        st.subheader("👥 استعلام العملاء")

        # زر لنقل العملاء المحليين إلى قاعدة البيانات
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 نقل العملاء المحليين إلى قاعدة البيانات", use_container_width=True):
                if 'customers' in st.session_state and st.session_state.customers:
                    success_count = 0
                    error_count = 0

                    with st.spinner('جاري نقل العملاء...'):
                        for customer in st.session_state.customers:
                            if not customer.get('is_deleted', False) and not customer.get('db_id'):
                                try:
                                    customer_data = {
                                        'first_name': customer.get('first_name', customer.get('name', 'عميل')),
                                        'last_name': customer.get('last_name', ''),
                                        'phone': customer.get('phone', ''),
                                        'email': customer.get('email', ''),
                                        'birth_date': customer.get('birth_date'),
                                        'zone_id': customer.get('zone_id', 1),
                                        'notes': customer.get('notes', ''),
                                        'branch_id': customer.get('branch_id', 1)
                                    }

                                    result = save_customer_to_db(customer_data)

                                    if result:
                                        customer['db_id'] = True
                                        success_count += 1
                                    else:
                                        error_count += 1

                                except Exception as e:
                                    st.error(f"خطأ في نقل العميل {customer.get('name', 'غير محدد')}: {str(e)}")
                                    error_count += 1

                    if success_count > 0:
                        st.success(f"✅ تم نقل {success_count} عميل بنجاح إلى قاعدة البيانات!")
                    if error_count > 0:
                        st.warning(f"⚠️ فشل في نقل {error_count} عميل")
                else:
                    st.info("لا توجد عملاء محليين لنقلهم")

        with col2:
            if st.button("📊 جلب جميع العملاء من قاعدة البيانات", use_container_width=True):
                query = """
                SELECT
                    c.customer_id,
                    c.first_name + ' ' + ISNULL(c.last_name, '') as full_name,
                    c.phone,
                    c.email,
                    b.name as branch_name,
                    c.created_at,
                    c.zone_id,
                    c.notes
                FROM customers c
                JOIN branches b ON c.branch_id = b.branch_id
                WHERE c.is_deleted = 0
                ORDER BY c.created_at DESC
                """

                customers = execute_query(query)

                if customers:
                    st.success(f"✅ تم العثور على {len(customers)} عميل في قاعدة البيانات")

                    # عرض العملاء في بطاقات
                    for customer in customers:
                        with st.expander(f"👤 {customer['full_name']} - {customer['phone']}"):
                            col1_cust, col2_cust = st.columns(2)

                            with col1_cust:
                                st.write(f"**رقم العميل:** {customer['customer_id']}")
                                st.write(f"**الاسم:** {customer['full_name']}")
                                st.write(f"**الهاتف:** {customer['phone']}")
                                st.write(f"**البريد:** {customer.get('email', 'غير محدد')}")

                            with col2_cust:
                                st.write(f"**الفرع:** {customer['branch_name']}")
                                st.write(f"**المنطقة:** {customer.get('zone_id', 'غير محدد')}")
                                st.write(f"**تاريخ التسجيل:** {customer['created_at']}")
                                if customer.get('notes'):
                                    st.write(f"**ملاحظات:** {customer['notes']}")

                    # عرض جدول مختصر أيضاً
                    st.markdown("### 📋 جدول العملاء:")
                    customers_df = pd.DataFrame(customers)
                    st.dataframe(customers_df[['customer_id', 'full_name', 'phone', 'branch_name', 'created_at']], use_container_width=True)
                else:
                    st.warning("⚠️ لا توجد عملاء في قاعدة البيانات")

        st.divider()

        # إحصائيات العملاء
        st.markdown("### 📊 إحصائيات العملاء:")

        col1_stats, col2_stats = st.columns(2)

        with col1_stats:
            st.markdown("**📱 العملاء المحليين:**")
            local_customers = len(st.session_state.get('customers', []))
            local_active = len([c for c in st.session_state.get('customers', []) if not c.get('is_deleted', False)])
            st.metric("إجمالي العملاء المحليين", local_customers)
            st.metric("العملاء النشطين محلياً", local_active)

        with col2_stats:
            st.markdown("**🗄️ العملاء في قاعدة البيانات:**")
            try:
                db_query = "SELECT COUNT(*) as total FROM customers WHERE is_deleted = 0"
                db_result = execute_query(db_query)
                db_customers = db_result[0]['total'] if db_result else 0

                branch_query = "SELECT COUNT(DISTINCT branch_id) as branches FROM customers WHERE is_deleted = 0"
                branch_result = execute_query(branch_query)
                db_branches = branch_result[0]['branches'] if branch_result else 0

                st.metric("إجمالي العملاء في قاعدة البيانات", db_customers)
                st.metric("عدد الفروع التي لديها عملاء", db_branches)
            except:
                st.error("خطأ في جلب إحصائيات قاعدة البيانات")

    with tab3:
        st.subheader("🏢 استعلام الفروع")

        if st.button("📊 جلب جميع الفروع"):
            branches = get_branches()

            if branches:
                st.success(f"✅ تم العثور على {len(branches)} فرع")
                branches_df = pd.DataFrame(branches)
                st.dataframe(branches_df, use_container_width=True)
            else:
                st.warning("⚠️ لا توجد فروع في قاعدة البيانات")

    with tab4:
        st.subheader("💰 استعلام المعاملات النقدية")

        col1, col2 = st.columns([1, 2])

        with col1:
            # فلاتر البحث
            st.markdown("**🔍 فلاتر البحث:**")

            # اختيار الفرع
            branches = get_branches()
            if branches:
                branch_options = {f"{b['name']} (ID: {b['branch_id']})": b['branch_id'] for b in branches}
                selected_branch_cash = st.selectbox("اختر الفرع", ["جميع الفروع"] + list(branch_options.keys()), key="cash_branch")
                branch_id_cash = branch_options.get(selected_branch_cash) if selected_branch_cash != "جميع الفروع" else None
            else:
                branch_id_cash = None

            # نوع المعاملة
            transaction_type = st.selectbox("نوع المعاملة", ["الكل", "income", "expense", "invoice_payment"])

            if st.button("🔍 بحث المعاملات", use_container_width=True):
                st.session_state.search_cash = True

        with col2:
            if st.session_state.get('search_cash', False):
                query = """
                SELECT
                    ct.transaction_id,
                    ct.transaction_type,
                    ct.amount,
                    ct.description,
                    ct.created_at,
                    ct.created_by,
                    b.name as branch_name
                FROM cash_transactions ct
                JOIN branches b ON ct.branch_id = b.branch_id
                WHERE 1=1
                """

                params = []

                if branch_id_cash:
                    query += " AND ct.branch_id = ?"
                    params.append(branch_id_cash)

                if transaction_type != "الكل":
                    query += " AND ct.transaction_type = ?"
                    params.append(transaction_type)

                query += " ORDER BY ct.created_at DESC"

                transactions = execute_query(query, params)

                if transactions:
                    st.success(f"✅ تم العثور على {len(transactions)} معاملة")

                    # حساب الإجماليات
                    total_income = sum([t['amount'] for t in transactions if t['transaction_type'] in ['income', 'invoice_payment']])
                    total_expense = sum([t['amount'] for t in transactions if t['transaction_type'] == 'expense'])
                    net_amount = total_income - total_expense

                    col1_cash, col2_cash, col3_cash = st.columns(3)
                    with col1_cash:
                        st.metric("إجمالي الإيرادات", f"{total_income:.2f} ج.م")
                    with col2_cash:
                        st.metric("إجمالي المصروفات", f"{total_expense:.2f} ج.م")
                    with col3_cash:
                        st.metric("صافي المبلغ", f"{net_amount:.2f} ج.م")

                    # عرض المعاملات
                    transactions_df = pd.DataFrame(transactions)
                    st.dataframe(transactions_df, use_container_width=True)
                else:
                    st.warning("⚠️ لم يتم العثور على معاملات تطابق معايير البحث")

# تشغيل التطبيق
if __name__ == "__main__":
    if st.session_state.authenticated:
        main_app()
    else:
        login_page()
