#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراجعة شاملة للنظام وإضافة الأكواد التسلسلية الآمنة
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_sequences_table():
    """إنشاء جدول الأرقام التسلسلية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء جدول الأرقام التسلسلية...")
        
        # إنشاء جدول الأرقام التسلسلية
        create_table_sql = """
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'sequences')
        BEGIN
            CREATE TABLE sequences (
                sequence_name NVARCHAR(50) PRIMARY KEY,
                current_value BIGINT NOT NULL DEFAULT 0,
                prefix NVARCHAR(10) DEFAULT '',
                suffix NVARCHAR(10) DEFAULT '',
                min_length INT DEFAULT 4,
                branch_id INT,
                created_at DATETIME DEFAULT GETDATE(),
                updated_at DATETIME DEFAULT GETDATE()
            )
        END
        """
        
        cursor.execute(create_table_sql)
        print("✅ تم إنشاء جدول sequences")
        
        # إدراج الأرقام التسلسلية الافتراضية
        default_sequences = [
            ('invoice_number', 1000, 'INV', '', 6, None),
            ('customer_code', 1000, 'CUS', '', 6, None),
            ('service_code', 100, 'S', '', 3, None),
            ('product_code', 100, 'P', '', 3, None),
            ('employee_code', 100, 'EMP', '', 3, None),
            ('branch_invoice_1', 1000, 'B1-INV', '', 6, 1),
            ('branch_invoice_2', 1000, 'B2-INV', '', 6, 2),
            ('branch_invoice_3', 1000, 'B3-INV', '', 6, 3)
        ]
        
        for seq in default_sequences:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sequences WHERE sequence_name = ?)
                BEGIN
                    INSERT INTO sequences (sequence_name, current_value, prefix, suffix, min_length, branch_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                END
            """, (seq[0], seq[0], seq[1], seq[2], seq[3], seq[4], seq[5]))
        
        print(f"✅ تم إدراج {len(default_sequences)} رقم تسلسلي افتراضي")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول sequences: {str(e)}")
        return False

def create_get_next_sequence_function():
    """إنشاء دالة للحصول على الرقم التسلسلي التالي (آمنة للعمل المتزامن)"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء دالة GetNextSequence...")
        
        # حذف الدالة إذا كانت موجودة
        cursor.execute("""
            IF OBJECT_ID('dbo.GetNextSequence', 'FN') IS NOT NULL
                DROP FUNCTION dbo.GetNextSequence
        """)
        
        # إنشاء stored procedure بدلاً من function للأمان
        cursor.execute("""
            IF OBJECT_ID('dbo.GetNextSequence', 'P') IS NOT NULL
                DROP PROCEDURE dbo.GetNextSequence
        """)
        
        create_procedure_sql = """
        CREATE PROCEDURE dbo.GetNextSequence
            @sequence_name NVARCHAR(50),
            @branch_id INT = NULL,
            @next_code NVARCHAR(50) OUTPUT
        AS
        BEGIN
            SET NOCOUNT ON;
            
            DECLARE @current_value BIGINT;
            DECLARE @prefix NVARCHAR(10);
            DECLARE @suffix NVARCHAR(10);
            DECLARE @min_length INT;
            DECLARE @formatted_number NVARCHAR(20);
            
            -- استخدام UPDLOCK و HOLDLOCK لضمان الأمان في البيئة المتزامنة
            UPDATE sequences WITH (UPDLOCK, HOLDLOCK)
            SET current_value = current_value + 1,
                updated_at = GETDATE(),
                @current_value = current_value + 1,
                @prefix = prefix,
                @suffix = suffix,
                @min_length = min_length
            WHERE sequence_name = @sequence_name 
                AND (@branch_id IS NULL OR branch_id = @branch_id OR branch_id IS NULL);
            
            -- تنسيق الرقم
            SET @formatted_number = RIGHT('000000000' + CAST(@current_value AS NVARCHAR), @min_length);
            SET @next_code = @prefix + @formatted_number + @suffix;
        END
        """
        
        cursor.execute(create_procedure_sql)
        print("✅ تم إنشاء stored procedure GetNextSequence")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء دالة GetNextSequence: {str(e)}")
        return False

def add_code_columns_to_tables():
    """إضافة أعمدة الأكواد للجداول"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إضافة أعمدة الأكواد للجداول...")
        
        # إضافة كود الفاتورة
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'invoice_code'
            )
            BEGIN
                ALTER TABLE invoices ADD invoice_code NVARCHAR(50) UNIQUE
            END
        """)
        print("✅ تم إضافة عمود invoice_code")
        
        # إضافة كود العميل
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'Customers' AND COLUMN_NAME = 'customer_code'
            )
            BEGIN
                ALTER TABLE Customers ADD customer_code NVARCHAR(50) UNIQUE
            END
        """)
        print("✅ تم إضافة عمود customer_code")
        
        # إضافة كود الموظف
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'employees' AND COLUMN_NAME = 'employee_code'
            )
            BEGIN
                ALTER TABLE employees ADD employee_code NVARCHAR(50) UNIQUE
            END
        """)
        print("✅ تم إضافة عمود employee_code")
        
        # إضافة كود الخدمة
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'services' AND COLUMN_NAME = 'service_code'
            )
            BEGIN
                ALTER TABLE services ADD service_code NVARCHAR(50) UNIQUE
            END
        """)
        print("✅ تم إضافة عمود service_code")
        
        # إضافة كود المنتج
        cursor.execute("""
            IF NOT EXISTS (
                SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'products' AND COLUMN_NAME = 'product_code'
            )
            BEGIN
                ALTER TABLE products ADD product_code NVARCHAR(50) UNIQUE
            END
        """)
        print("✅ تم إضافة عمود product_code")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة أعمدة الأكواد: {str(e)}")
        return False

def update_existing_records_with_codes():
    """تحديث السجلات الموجودة بأكواد تسلسلية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 تحديث السجلات الموجودة بأكواد...")
        
        # تحديث العملاء بأكواد
        cursor.execute("""
            SELECT CustomerId FROM Customers 
            WHERE customer_code IS NULL 
            ORDER BY CustomerId
        """)
        customers = cursor.fetchall()
        
        for customer in customers:
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence 'customer_code', NULL, @next_code OUTPUT;
                UPDATE Customers SET customer_code = @next_code WHERE CustomerId = ?;
            """, (customer[0],))
        
        print(f"✅ تم تحديث {len(customers)} عميل بأكواد")
        
        # تحديث الموظفين بأكواد
        cursor.execute("""
            SELECT employee_id FROM employees 
            WHERE employee_code IS NULL 
            ORDER BY employee_id
        """)
        employees = cursor.fetchall()
        
        for employee in employees:
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence 'employee_code', NULL, @next_code OUTPUT;
                UPDATE employees SET employee_code = @next_code WHERE employee_id = ?;
            """, (employee[0],))
        
        print(f"✅ تم تحديث {len(employees)} موظف بأكواد")
        
        # تحديث الفواتير بأكواد
        cursor.execute("""
            SELECT invoice_id, branch_id FROM invoices 
            WHERE invoice_code IS NULL 
            ORDER BY invoice_id
        """)
        invoices = cursor.fetchall()
        
        for invoice in invoices:
            sequence_name = f'branch_invoice_{invoice[1]}' if invoice[1] else 'invoice_number'
            cursor.execute("""
                DECLARE @next_code NVARCHAR(50);
                EXEC GetNextSequence ?, ?, @next_code OUTPUT;
                UPDATE invoices SET invoice_code = @next_code WHERE invoice_id = ?;
            """, (sequence_name, invoice[1], invoice[0]))
        
        print(f"✅ تم تحديث {len(invoices)} فاتورة بأكواد")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث السجلات: {str(e)}")
        return False

def test_concurrent_sequence_generation():
    """اختبار توليد الأرقام التسلسلية في بيئة متزامنة"""
    try:
        print("🔄 اختبار الأرقام التسلسلية المتزامنة...")
        
        import threading
        import time
        
        generated_codes = []
        errors = []
        
        def generate_codes(thread_id):
            try:
                conn = get_db_connection()
                if conn:
                    cursor = conn.cursor()
                    
                    for i in range(5):
                        cursor.execute("""
                            DECLARE @next_code NVARCHAR(50);
                            EXEC GetNextSequence 'invoice_number', NULL, @next_code OUTPUT;
                            SELECT @next_code;
                        """)
                        result = cursor.fetchone()
                        if result:
                            code = result[0]
                            generated_codes.append(f"Thread-{thread_id}: {code}")
                            print(f"Thread-{thread_id} generated: {code}")
                        
                        time.sleep(0.1)  # محاكاة وقت المعالجة
                    
                    conn.close()
            except Exception as e:
                errors.append(f"Thread-{thread_id}: {str(e)}")
        
        # تشغيل 3 threads متزامنة
        threads = []
        for i in range(3):
            thread = threading.Thread(target=generate_codes, args=(i+1,))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الـ threads
        for thread in threads:
            thread.join()
        
        print(f"✅ تم توليد {len(generated_codes)} كود")
        print(f"❌ عدد الأخطاء: {len(errors)}")
        
        # فحص التكرار
        codes_only = [code.split(': ')[1] for code in generated_codes]
        unique_codes = set(codes_only)
        
        if len(codes_only) == len(unique_codes):
            print("✅ جميع الأكواد فريدة - النظام آمن للعمل المتزامن")
        else:
            print("❌ يوجد تكرار في الأكواد - مشكلة في النظام")
        
        return len(errors) == 0 and len(codes_only) == len(unique_codes)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التزامن: {str(e)}")
        return False

def review_database_structure():
    """مراجعة هيكل قاعدة البيانات"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 مراجعة هيكل قاعدة البيانات...")
        
        # قائمة الجداول المطلوبة
        required_tables = [
            'Customers', 'employees', 'branches', 'categories',
            'services', 'products', 'invoices', 'invoice_items',
            'cash_register', 'sequences'
        ]
        
        existing_tables = []
        cursor.execute("""
            SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)
        
        for table in cursor.fetchall():
            existing_tables.append(table[0])
        
        print("📋 الجداول الموجودة:")
        for table in existing_tables:
            status = "✅" if table in required_tables else "⚠️"
            print(f"   {status} {table}")
        
        print("\n📋 الجداول المطلوبة:")
        for table in required_tables:
            status = "✅" if table in existing_tables else "❌"
            print(f"   {status} {table}")
        
        # فحص الأعمدة المهمة
        important_columns = {
            'invoices': ['invoice_code', 'responsible_employee'],
            'Customers': ['customer_code'],
            'employees': ['employee_code'],
            'services': ['service_code'],
            'products': ['product_code']
        }
        
        print("\n🔍 فحص الأعمدة المهمة:")
        for table, columns in important_columns.items():
            if table in existing_tables:
                cursor.execute("""
                    SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = ?
                """, (table,))
                
                existing_columns = [col[0] for col in cursor.fetchall()]
                
                for column in columns:
                    status = "✅" if column in existing_columns else "❌"
                    print(f"   {status} {table}.{column}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مراجعة قاعدة البيانات: {str(e)}")
        return False

def check_system_performance():
    """فحص أداء النظام"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("📊 فحص أداء النظام...")
        
        # إحصائيات البيانات
        stats = {}
        
        tables_to_check = ['Customers', 'employees', 'invoices', 'invoice_items', 'cash_register']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                stats[table] = count
                print(f"📋 {table}: {count} سجل")
            except:
                stats[table] = 0
                print(f"❌ {table}: غير متاح")
        
        # فحص آخر العمليات
        try:
            cursor.execute("""
                SELECT TOP 1 invoice_code, total_amount, created_at 
                FROM invoices 
                ORDER BY created_at DESC
            """)
            last_invoice = cursor.fetchone()
            if last_invoice:
                print(f"📄 آخر فاتورة: {last_invoice[0]} - {last_invoice[1]} ج.م - {last_invoice[2]}")
        except:
            print("⚠️ لا يمكن جلب آخر فاتورة")
        
        # فحص رصيد الخزنة
        try:
            cursor.execute("""
                SELECT SUM(
                    CASE 
                        WHEN transaction_type = 'income' THEN amount
                        WHEN transaction_type = 'expense' THEN -amount
                        ELSE 0
                    END
                ) FROM cash_register WHERE is_deleted = 0
            """)
            balance = cursor.fetchone()[0] or 0
            print(f"💰 رصيد الخزنة: {balance:.2f} ج.م")
        except:
            print("⚠️ لا يمكن حساب رصيد الخزنة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأداء: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 مراجعة شاملة لنظام SalonProManager")
    print("=" * 60)
    
    # إنشاء جدول الأرقام التسلسلية
    if create_sequences_table():
        print("✅ جدول الأرقام التسلسلية جاهز")
    
    # إنشاء دالة الأرقام التسلسلية
    if create_get_next_sequence_function():
        print("✅ دالة الأرقام التسلسلية جاهزة")
    
    # إضافة أعمدة الأكواد
    if add_code_columns_to_tables():
        print("✅ أعمدة الأكواد جاهزة")
    
    # تحديث السجلات الموجودة
    if update_existing_records_with_codes():
        print("✅ تحديث السجلات مكتمل")
    
    print("\n" + "=" * 60)
    
    # مراجعة هيكل قاعدة البيانات
    if review_database_structure():
        print("✅ مراجعة قاعدة البيانات مكتملة")
    
    print("\n" + "=" * 60)
    
    # اختبار التزامن
    if test_concurrent_sequence_generation():
        print("✅ اختبار التزامن نجح")
    
    print("\n" + "=" * 60)
    
    # فحص الأداء
    if check_system_performance():
        print("✅ فحص الأداء مكتمل")
    
    print("\n" + "=" * 60)
    print("🎉 المراجعة الشاملة مكتملة!")
    print("💡 النظام جاهز للعمل مع الأكواد التسلسلية الآمنة")
    
    input("\nاضغط Enter للخروج...")
