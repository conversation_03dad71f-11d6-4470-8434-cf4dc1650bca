#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ العملاء في قاعدة البيانات
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_customer_save():
    """اختبار حفظ عميل جديد"""
    try:
        print("🔄 جاري الاتصال بقاعدة البيانات...")
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # اختبار الاتصال
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # فحص هيكل جدول Customers
        print("🔍 فحص هيكل جدول Customers...")
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Customers'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        print("📋 أعمدة جدول Customers:")
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - يقبل NULL: {col[2]}")
        
        # محاولة إدراج عميل تجريبي
        print("\n🔄 محاولة إدراج عميل تجريبي...")
        
        insert_query = """
        INSERT INTO Customers (FirstName, LastName, PhoneNumber, Email, DateOfBirth, 
                             Notes, ReferredByZoneId, branch_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        test_data = (
            "عميل تجريبي",  # FirstName
            "للاختبار",     # LastName
            "01000000000",   # PhoneNumber
            "<EMAIL>", # Email
            datetime(1990, 1, 1).date(),  # DateOfBirth
            "عميل تجريبي للاختبار",  # Notes
            1,  # ReferredByZoneId
            1   # branch_id
        )
        
        cursor.execute(insert_query, test_data)
        
        # الحصول على معرف العميل الجديد
        cursor.execute("SELECT SCOPE_IDENTITY()")
        result = cursor.fetchone()
        customer_id = result[0] if result else None
        
        conn.commit()
        
        if customer_id:
            print(f"✅ تم إدراج العميل التجريبي بنجاح! معرف العميل: {customer_id}")
            
            # التحقق من الإدراج
            cursor.execute("SELECT * FROM Customers WHERE CustomerId = ?", (customer_id,))
            customer = cursor.fetchone()
            
            if customer:
                print("📊 بيانات العميل المحفوظ:")
                print(f"   • المعرف: {customer[0]}")
                print(f"   • الاسم الأول: {customer[1]}")
                print(f"   • اسم العائلة: {customer[2]}")
                print(f"   • الهاتف: {customer[3]}")
                print(f"   • الإيميل: {customer[5]}")
                print(f"   • تاريخ الميلاد: {customer[6]}")
                print(f"   • الملاحظات: {customer[7]}")
                print(f"   • معرف المنطقة: {customer[8]}")
                print(f"   • معرف الفرع: {customer[12]}")
                
                # حذف العميل التجريبي
                print("\n🗑️ حذف العميل التجريبي...")
                cursor.execute("DELETE FROM Customers WHERE CustomerId = ?", (customer_id,))
                conn.commit()
                print("✅ تم حذف العميل التجريبي")
            
        else:
            print("❌ فشل في الحصول على معرف العميل الجديد")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ العميل: {str(e)}")
        return False

def test_customer_retrieval():
    """اختبار جلب العملاء"""
    try:
        print("\n🔄 اختبار جلب العملاء...")
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # جلب العملاء حسب الفرع
        query = """
        SELECT CustomerId, FirstName, LastName, PhoneNumber, Email, DateOfBirth, 
               Notes, branch_id, CreatedAt, is_deleted
        FROM Customers 
        WHERE branch_id = ? AND ISNULL(is_deleted, 0) = 0
        ORDER BY FirstName, LastName
        """
        
        cursor.execute(query, (1,))
        customers = cursor.fetchall()
        
        print(f"📊 تم العثور على {len(customers)} عميل في الفرع 1:")
        
        for customer in customers[:5]:  # عرض أول 5 عملاء فقط
            print(f"   • {customer[1]} {customer[2]} - {customer[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جلب العملاء: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار نظام حفظ العملاء")
    print("=" * 50)
    
    # اختبار حفظ العميل
    if test_customer_save():
        print("\n✅ اختبار حفظ العميل نجح!")
    else:
        print("\n❌ اختبار حفظ العميل فشل!")
    
    # اختبار جلب العملاء
    if test_customer_retrieval():
        print("\n✅ اختبار جلب العملاء نجح!")
    else:
        print("\n❌ اختبار جلب العملاء فشل!")
    
    print("\n" + "=" * 50)
    print("🏁 انتهى الاختبار")
    
    input("\nاضغط Enter للخروج...")
