"""
اختبار قاعدة البيانات
Test Database Connection
"""

import pyodbc
from passlib.context import CryptContext

# إعداد تشفير كلمات المرور
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def test_database():
    """اختبار قاعدة البيانات"""
    
    # معلومات الاتصال
    server = "alisamaraa.ddns.net,4100"
    username = "sa"
    password = "@a123admin4"
    database_name = "SalonProManager"
    
    try:
        connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database_name};UID={username};PWD={password}"
        
        print("🔗 الاتصال بقاعدة البيانات...")
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول
        cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'")
        tables = cursor.fetchall()
        print(f"✅ الجداول الموجودة: {[table[0] for table in tables]}")
        
        # التحقق من بيانات المستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✅ عدد المستخدمين: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT username, full_name FROM users")
            users = cursor.fetchall()
            print("✅ المستخدمون:")
            for user in users:
                print(f"  - {user[0]} ({user[1]})")
        
        # التحقق من كلمة مرور admin
        cursor.execute("SELECT password_hash FROM users WHERE username = 'admin'")
        result = cursor.fetchone()
        if result:
            stored_hash = result[0]
            print(f"✅ تم العثور على مستخدم admin")
            
            # اختبار كلمة المرور
            is_valid = pwd_context.verify("admin123", stored_hash)
            print(f"✅ كلمة المرور صحيحة: {is_valid}")
            
            if not is_valid:
                print("🔧 إعادة تعيين كلمة المرور...")
                new_hash = pwd_context.hash("admin123")
                cursor.execute("UPDATE users SET password_hash = ? WHERE username = 'admin'", new_hash)
                conn.commit()
                print("✅ تم تحديث كلمة المرور")
        else:
            print("❌ لم يتم العثور على مستخدم admin")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    test_database()
