#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة IDENTITY
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_identity_methods():
    """اختبار طرق مختلفة للحصول على IDENTITY"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 اختبار طرق الحصول على IDENTITY...")
        
        # إدراج فاتورة تجريبية
        insert_query = """
        INSERT INTO invoices (
            customer_id, branch_id, employee_id, invoice_date,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_method, payment_status, notes, responsible_employee
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_query, (
            1, 1, 1, datetime.now().date(),
            100.00, 0.00, 10.00, 90.00,
            'نقدي', 'مدفوعة', 'اختبار IDENTITY', 'أحمد محمد'
        ))
        
        print("✅ تم إدراج الفاتورة")
        
        # الطريقة 1: SCOPE_IDENTITY()
        try:
            cursor.execute("SELECT SCOPE_IDENTITY()")
            result = cursor.fetchone()
            scope_id = int(result[0]) if result and result[0] else None
            print(f"🔍 SCOPE_IDENTITY(): {scope_id}")
        except Exception as e:
            print(f"❌ SCOPE_IDENTITY() فشل: {str(e)}")
            scope_id = None
        
        # الطريقة 2: @@IDENTITY
        try:
            cursor.execute("SELECT @@IDENTITY")
            result = cursor.fetchone()
            identity_id = int(result[0]) if result and result[0] else None
            print(f"🔍 @@IDENTITY: {identity_id}")
        except Exception as e:
            print(f"❌ @@IDENTITY فشل: {str(e)}")
            identity_id = None
        
        # الطريقة 3: IDENT_CURRENT
        try:
            cursor.execute("SELECT IDENT_CURRENT('invoices')")
            result = cursor.fetchone()
            ident_current = int(result[0]) if result and result[0] else None
            print(f"🔍 IDENT_CURRENT: {ident_current}")
        except Exception as e:
            print(f"❌ IDENT_CURRENT فشل: {str(e)}")
            ident_current = None
        
        # الطريقة 4: البحث بالتاريخ والمبلغ
        try:
            cursor.execute("""
                SELECT TOP 1 invoice_id FROM invoices 
                WHERE customer_id = 1 AND total_amount = 90.00 
                ORDER BY created_at DESC
            """)
            result = cursor.fetchone()
            search_id = int(result[0]) if result and result[0] else None
            print(f"🔍 البحث بالتاريخ والمبلغ: {search_id}")
        except Exception as e:
            print(f"❌ البحث فشل: {str(e)}")
            search_id = None
        
        # اختيار أفضل طريقة
        final_id = scope_id or identity_id or ident_current or search_id
        
        if final_id:
            print(f"✅ تم الحصول على معرف الفاتورة: {final_id}")
            
            # إضافة عناصر تجريبية
            cursor.execute("""
                INSERT INTO invoice_items (
                    invoice_id, item_type, item_code, item_name,
                    quantity, unit_price, total_price, employee_name, commission_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                final_id, 'service', 'S001', 'قص شعر تجريبي',
                1, 50.00, 50.00, 'أحمد محمد', 7.50
            ))
            
            cursor.execute("""
                INSERT INTO invoice_items (
                    invoice_id, item_type, item_code, item_name,
                    quantity, unit_price, total_price, employee_name, commission_amount
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                final_id, 'product', 'P001', 'شامبو تجريبي',
                2, 25.00, 50.00, 'غير محدد', 0.00
            ))
            
            print("✅ تم إضافة عناصر الفاتورة")
            
            # إضافة للخزنة
            cursor.execute("""
                INSERT INTO cash_register (
                    transaction_type, amount, description, reference_type,
                    reference_id, branch_id, created_by, payment_method, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'income', 90.00, f'فاتورة رقم {final_id} - اختبار',
                'invoice', final_id, 1, 'admin', 'نقدي', 'اختبار النظام'
            ))
            
            print("✅ تم إضافة المعاملة للخزنة")
            
            conn.commit()
            print(f"🎉 تم حفظ الفاتورة {final_id} بالكامل!")
            
        else:
            print("❌ فشل في الحصول على معرف الفاتورة بجميع الطرق")
        
        conn.close()
        return final_id is not None
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

def check_final_results():
    """فحص النتائج النهائية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return

        cursor = conn.cursor()
        
        print("\n📊 فحص النتائج النهائية...")
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        print(f"📋 إجمالي الفواتير: {total_invoices}")
        
        # عدد عناصر الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoice_items")
        total_items = cursor.fetchone()[0]
        print(f"🛍️ إجمالي عناصر الفواتير: {total_items}")
        
        # عدد معاملات الخزنة
        cursor.execute("SELECT COUNT(*) FROM cash_register WHERE reference_type = 'invoice'")
        cash_transactions = cursor.fetchone()[0]
        print(f"💰 معاملات الخزنة من الفواتير: {cash_transactions}")
        
        # آخر فاتورة
        cursor.execute("""
            SELECT TOP 1 invoice_id, total_amount, responsible_employee, created_at
            FROM invoices
            ORDER BY created_at DESC
        """)
        
        last_invoice = cursor.fetchone()
        if last_invoice:
            print(f"📄 آخر فاتورة: رقم {last_invoice[0]}, {last_invoice[1]} ج.م, {last_invoice[2]}, {last_invoice[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص النتائج: {str(e)}")

if __name__ == "__main__":
    print("🧪 اختبار إصلاح مشكلة IDENTITY")
    print("=" * 50)
    
    if test_identity_methods():
        print("\n✅ الاختبار نجح!")
    else:
        print("\n❌ الاختبار فشل!")
    
    check_final_results()
    
    print("\n" + "=" * 50)
    print("🏁 انتهى الاختبار")
    
    input("\nاضغط Enter للخروج...")
