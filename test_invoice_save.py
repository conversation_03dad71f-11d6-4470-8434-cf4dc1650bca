#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ الفواتير في قاعدة البيانات
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_invoices_table():
    """فحص هيكل جدول الفواتير"""
    try:
        conn = get_db_connection()
        if conn is None:
            return

        cursor = conn.cursor()
        
        print("🔍 فحص هيكل جدول invoices...")
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'invoices'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        print("📋 أعمدة جدول invoices:")
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - يقبل NULL: {col[2]}")
        
        print("\n🔍 فحص هيكل جدول invoice_items...")
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'invoice_items'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        print("📋 أعمدة جدول invoice_items:")
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - يقبل NULL: {col[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {str(e)}")

def test_invoice_save():
    """اختبار حفظ فاتورة جديدة"""
    try:
        print("🔄 جاري اختبار حفظ فاتورة...")
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # بيانات فاتورة تجريبية
        invoice_data = {
            'customer_id': 1,
            'branch_id': 1,
            'employee_id': 1,
            'invoice_date': datetime.now().date(),
            'subtotal': 100.00,
            'tax_amount': 14.00,
            'discount_amount': 10.00,
            'total_amount': 104.00,
            'payment_method': 'نقدي',
            'payment_status': 'مدفوعة',
            'notes': 'فاتورة تجريبية للاختبار'
        }
        
        # محاولة إدراج فاتورة
        insert_query = """
        INSERT INTO invoices (
            customer_id, branch_id, employee_id, invoice_date,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_method, payment_status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_query, (
            invoice_data['customer_id'],
            invoice_data['branch_id'],
            invoice_data['employee_id'],
            invoice_data['invoice_date'],
            invoice_data['subtotal'],
            invoice_data['tax_amount'],
            invoice_data['discount_amount'],
            invoice_data['total_amount'],
            invoice_data['payment_method'],
            invoice_data['payment_status'],
            invoice_data['notes']
        ))
        
        # الحصول على معرف الفاتورة
        invoice_id = cursor.lastrowid
        print(f"✅ تم إدراج الفاتورة بنجاح! معرف الفاتورة: {invoice_id}")
        
        # إدراج عناصر الفاتورة
        items_data = [
            {
                'item_type': 'service',
                'item_code': 'S001',
                'item_name': 'قص شعر',
                'quantity': 1,
                'unit_price': 50.00,
                'total_price': 50.00,
                'employee_name': 'أحمد محمد',
                'commission_amount': 7.50
            },
            {
                'item_type': 'product',
                'item_code': 'P001',
                'item_name': 'شامبو',
                'quantity': 2,
                'unit_price': 25.00,
                'total_price': 50.00,
                'employee_name': 'غير محدد',
                'commission_amount': 0.00
            }
        ]
        
        items_query = """
        INSERT INTO invoice_items (
            invoice_id, item_type, item_code, item_name,
            quantity, unit_price, total_price, employee_name, commission_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        for item in items_data:
            cursor.execute(items_query, (
                invoice_id,
                item['item_type'],
                item['item_code'],
                item['item_name'],
                item['quantity'],
                item['unit_price'],
                item['total_price'],
                item['employee_name'],
                item['commission_amount']
            ))
        
        conn.commit()
        print(f"✅ تم إدراج {len(items_data)} عنصر للفاتورة")
        
        # التحقق من الحفظ
        cursor.execute("SELECT * FROM invoices WHERE invoice_id = ?", (invoice_id,))
        invoice = cursor.fetchone()
        
        if invoice:
            print("📊 بيانات الفاتورة المحفوظة:")
            print(f"   • معرف الفاتورة: {invoice[0]}")
            print(f"   • معرف العميل: {invoice[1]}")
            print(f"   • معرف الفرع: {invoice[2]}")
            print(f"   • تاريخ الفاتورة: {invoice[4]}")
            print(f"   • المجموع الفرعي: {invoice[5]}")
            print(f"   • الضريبة: {invoice[6]}")
            print(f"   • الخصم: {invoice[7]}")
            print(f"   • الإجمالي: {invoice[8]}")
            print(f"   • طريقة الدفع: {invoice[9]}")
            print(f"   • حالة الدفع: {invoice[10]}")
        
        # التحقق من عناصر الفاتورة
        cursor.execute("SELECT * FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
        items = cursor.fetchall()
        
        print(f"\n📋 عناصر الفاتورة ({len(items)} عنصر):")
        for item in items:
            print(f"   • {item[3]} ({item[2]}) - الكمية: {item[4]} - السعر: {item[5]} ج.م")
        
        # حذف الفاتورة التجريبية
        print("\n🗑️ حذف الفاتورة التجريبية...")
        cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
        cursor.execute("DELETE FROM invoices WHERE invoice_id = ?", (invoice_id,))
        conn.commit()
        print("✅ تم حذف الفاتورة التجريبية")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ الفاتورة: {str(e)}")
        return False

def test_invoice_retrieval():
    """اختبار جلب الفواتير"""
    try:
        print("\n🔄 اختبار جلب الفواتير...")
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # جلب الفواتير حسب الفرع
        query = """
        SELECT i.*, c.FirstName + ' ' + ISNULL(c.LastName, '') as customer_name
        FROM invoices i
        LEFT JOIN Customers c ON i.customer_id = c.CustomerId
        WHERE i.branch_id = ? AND ISNULL(i.is_deleted, 0) = 0
        ORDER BY i.created_at DESC
        """
        
        cursor.execute(query, (1,))
        invoices = cursor.fetchall()
        
        print(f"📊 تم العثور على {len(invoices)} فاتورة في الفرع 1:")
        
        for invoice in invoices[:3]:  # عرض أول 3 فواتير فقط
            print(f"   • فاتورة {invoice[0]} - العميل: {invoice[-1]} - المبلغ: {invoice[8]} ج.م")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جلب الفواتير: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار نظام حفظ الفواتير")
    print("=" * 50)
    
    # فحص هيكل الجداول
    check_invoices_table()
    
    print("\n" + "=" * 50)
    
    # اختبار حفظ الفاتورة
    if test_invoice_save():
        print("\n✅ اختبار حفظ الفاتورة نجح!")
    else:
        print("\n❌ اختبار حفظ الفاتورة فشل!")
    
    # اختبار جلب الفواتير
    if test_invoice_retrieval():
        print("\n✅ اختبار جلب الفواتير نجح!")
    else:
        print("\n❌ اختبار جلب الفواتير فشل!")
    
    print("\n" + "=" * 50)
    print("🏁 انتهى الاختبار")
    
    input("\nاضغط Enter للخروج...")
