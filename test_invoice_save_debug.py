#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وتشخيص مشكلة حفظ الفواتير
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_invoice_save_step_by_step():
    """اختبار حفظ الفاتورة خطوة بخطوة"""
    try:
        print("🔄 بدء اختبار حفظ الفاتورة...")
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # الخطوة 1: البحث عن عميل
        print("\n📋 الخطوة 1: البحث عن العملاء...")
        cursor.execute("""
            SELECT CustomerId, FirstName, LastName FROM Customers 
            WHERE is_deleted = 0 OR is_deleted IS NULL
            ORDER BY CustomerId DESC
        """)
        customers = cursor.fetchall()
        
        if customers:
            print(f"✅ تم العثور على {len(customers)} عميل:")
            for customer in customers[:3]:
                print(f"   • العميل {customer[0]}: {customer[1]} {customer[2]}")
            customer_id = customers[0][0]
            customer_name = f"{customers[0][1]} {customers[0][2]}"
        else:
            print("❌ لا يوجد عملاء في قاعدة البيانات")
            return False
        
        # الخطوة 2: البحث عن موظف
        print("\n👨‍💼 الخطوة 2: البحث عن الموظفين...")
        cursor.execute("""
            SELECT employee_id, first_name, last_name FROM employees 
            WHERE is_active = 1 OR is_active IS NULL
            ORDER BY employee_id DESC
        """)
        employees = cursor.fetchall()
        
        if employees:
            print(f"✅ تم العثور على {len(employees)} موظف:")
            for employee in employees[:3]:
                print(f"   • الموظف {employee[0]}: {employee[1]} {employee[2] or ''}")
            employee_id = employees[0][0]
            employee_name = f"{employees[0][1]} {employees[0][2] or ''}".strip()
        else:
            print("❌ لا يوجد موظفين في قاعدة البيانات")
            employee_id = 1
            employee_name = "موظف افتراضي"
        
        # الخطوة 3: إنشاء فاتورة تجريبية
        print("\n🧾 الخطوة 3: إنشاء فاتورة تجريبية...")
        
        invoice_data = {
            'customer_id': customer_id,
            'branch_id': 1,
            'employee_id': employee_id,
            'invoice_date': datetime.now().date(),
            'subtotal': 100.00,
            'tax_amount': 0.00,
            'discount_amount': 10.00,
            'total_amount': 90.00,
            'payment_method': 'نقدي',
            'payment_status': 'مدفوعة',
            'notes': f'فاتورة تجريبية للعميل {customer_name}',
            'responsible_employee': employee_name
        }
        
        print("📝 بيانات الفاتورة:")
        for key, value in invoice_data.items():
            print(f"   • {key}: {value}")
        
        # الخطوة 4: حفظ الفاتورة
        print("\n💾 الخطوة 4: حفظ الفاتورة في قاعدة البيانات...")
        
        insert_query = """
        INSERT INTO invoices (
            customer_id, branch_id, employee_id, invoice_date,
            subtotal, tax_amount, discount_amount, total_amount,
            payment_method, payment_status, notes, responsible_employee
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_query, (
            invoice_data['customer_id'],
            invoice_data['branch_id'],
            invoice_data['employee_id'],
            invoice_data['invoice_date'],
            invoice_data['subtotal'],
            invoice_data['tax_amount'],
            invoice_data['discount_amount'],
            invoice_data['total_amount'],
            invoice_data['payment_method'],
            invoice_data['payment_status'],
            invoice_data['notes'],
            invoice_data['responsible_employee']
        ))
        
        print("✅ تم تنفيذ استعلام الإدراج")
        
        # الخطوة 5: الحصول على معرف الفاتورة
        print("\n🔍 الخطوة 5: الحصول على معرف الفاتورة...")
        
        cursor.execute("SELECT SCOPE_IDENTITY()")
        result = cursor.fetchone()
        invoice_id = int(result[0]) if result and result[0] else None
        
        if invoice_id:
            print(f"✅ تم الحصول على معرف الفاتورة: {invoice_id}")
        else:
            print("❌ فشل في الحصول على معرف الفاتورة")
            return False
        
        # الخطوة 6: إضافة عناصر الفاتورة
        print("\n🛍️ الخطوة 6: إضافة عناصر الفاتورة...")
        
        items = [
            {
                'type': 'service',
                'code': 'S001',
                'name': 'قص شعر رجالي',
                'quantity': 1,
                'unit_price': 50.00,
                'total_price': 50.00,
                'commission': 7.50
            },
            {
                'type': 'product',
                'code': 'P001',
                'name': 'شامبو للشعر',
                'quantity': 2,
                'unit_price': 25.00,
                'total_price': 50.00,
                'commission': 0.00
            }
        ]
        
        items_query = """
        INSERT INTO invoice_items (
            invoice_id, item_type, item_code, item_name,
            quantity, unit_price, total_price, employee_name, commission_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        for item in items:
            cursor.execute(items_query, (
                invoice_id,
                item['type'],
                item['code'],
                item['name'],
                item['quantity'],
                item['unit_price'],
                item['total_price'],
                employee_name,
                item['commission']
            ))
            print(f"   ✅ تم إضافة: {item['name']}")
        
        # الخطوة 7: إضافة المعاملة للخزنة
        print("\n💰 الخطوة 7: إضافة المعاملة للخزنة...")
        
        cash_query = """
        INSERT INTO cash_register (
            transaction_type, amount, description, reference_type,
            reference_id, branch_id, created_by, payment_method, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(cash_query, (
            'income',
            invoice_data['total_amount'],
            f"فاتورة رقم {invoice_id} - {customer_name}",
            'invoice',
            invoice_id,
            invoice_data['branch_id'],
            'admin',
            invoice_data['payment_method'],
            f"دفعة نقدية من العميل: {customer_name}"
        ))
        
        print("✅ تم إضافة المعاملة للخزنة")
        
        # الخطوة 8: حفظ التغييرات
        print("\n💾 الخطوة 8: حفظ التغييرات...")
        conn.commit()
        print("✅ تم حفظ جميع التغييرات في قاعدة البيانات")
        
        # الخطوة 9: التحقق من الحفظ
        print("\n🔍 الخطوة 9: التحقق من الحفظ...")
        
        # التحقق من الفاتورة
        cursor.execute("SELECT * FROM invoices WHERE invoice_id = ?", (invoice_id,))
        saved_invoice = cursor.fetchone()
        
        if saved_invoice:
            print(f"✅ الفاتورة محفوظة: رقم {saved_invoice[0]}, المبلغ {saved_invoice[8]} ج.م")
        else:
            print("❌ الفاتورة غير محفوظة!")
        
        # التحقق من العناصر
        cursor.execute("SELECT COUNT(*) FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
        items_count = cursor.fetchone()[0]
        print(f"✅ عدد العناصر المحفوظة: {items_count}")
        
        # التحقق من الخزنة
        cursor.execute("SELECT COUNT(*) FROM cash_register WHERE reference_id = ?", (invoice_id,))
        cash_count = cursor.fetchone()[0]
        print(f"✅ عدد معاملات الخزنة: {cash_count}")
        
        conn.close()
        
        print(f"\n🎉 تم إنشاء الفاتورة رقم {invoice_id} بنجاح!")
        print("✅ جميع الخطوات تمت بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ الفاتورة: {str(e)}")
        import traceback
        print("تفاصيل الخطأ:")
        print(traceback.format_exc())
        return False

def check_current_invoices():
    """فحص الفواتير الحالية"""
    try:
        conn = get_db_connection()
        if conn is None:
            return

        cursor = conn.cursor()
        
        print("\n📊 فحص الفواتير الحالية...")
        
        # عدد الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoices")
        total_invoices = cursor.fetchone()[0]
        print(f"📋 إجمالي الفواتير: {total_invoices}")
        
        # آخر 3 فواتير
        cursor.execute("""
            SELECT TOP 3 invoice_id, customer_id, total_amount, payment_method, created_at
            FROM invoices
            ORDER BY created_at DESC
        """)
        
        recent_invoices = cursor.fetchall()
        
        if recent_invoices:
            print("📄 آخر الفواتير:")
            for invoice in recent_invoices:
                print(f"   • فاتورة {invoice[0]}: عميل {invoice[1]}, {invoice[2]} ج.م, {invoice[3]}, {invoice[4]}")
        else:
            print("📄 لا توجد فواتير")
        
        # عدد عناصر الفواتير
        cursor.execute("SELECT COUNT(*) FROM invoice_items")
        total_items = cursor.fetchone()[0]
        print(f"🛍️ إجمالي عناصر الفواتير: {total_items}")
        
        # عدد معاملات الخزنة
        cursor.execute("SELECT COUNT(*) FROM cash_register WHERE reference_type = 'invoice'")
        cash_transactions = cursor.fetchone()[0]
        print(f"💰 معاملات الخزنة من الفواتير: {cash_transactions}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الفواتير: {str(e)}")

if __name__ == "__main__":
    print("🧪 تشخيص مشكلة حفظ الفواتير")
    print("=" * 60)
    
    # فحص الفواتير الحالية
    check_current_invoices()
    
    print("\n" + "=" * 60)
    
    # اختبار حفظ فاتورة جديدة
    if test_invoice_save_step_by_step():
        print("\n✅ الاختبار نجح - النظام يعمل بشكل صحيح!")
    else:
        print("\n❌ الاختبار فشل - هناك مشكلة في النظام!")
    
    print("\n" + "=" * 60)
    
    # فحص الفواتير بعد الاختبار
    check_current_invoices()
    
    print("\n🏁 انتهى التشخيص")
    
    input("\nاضغط Enter للخروج...")
