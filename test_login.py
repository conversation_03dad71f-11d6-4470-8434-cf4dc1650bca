"""
اختبار تسجيل الدخول
Test Login Functionality
"""

import requests
import json

def test_login():
    """اختبار تسجيل الدخول"""
    
    # اختبار الاتصال بالـ API
    try:
        response = requests.get("http://localhost:8000/")
        print(f"✅ API متاح - Status: {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بالـ API: {e}")
        return
    
    # اختبار تسجيل الدخول
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        print("\n🔐 اختبار تسجيل الدخول...")
        response = requests.post(
            "http://localhost:8000/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ تسجيل الدخول ناجح!")
            print(f"Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"User: {data.get('user', {}).get('full_name', 'N/A')}")
        else:
            print(f"❌ فشل تسجيل الدخول - Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")

if __name__ == "__main__":
    test_login()
