#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث هيكل قاعدة البيانات لإضافة جدول الخزنة وتعديل جدول الفواتير
"""

import pyodbc
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    try:
        connection_string = (
            "DRIVER={ODBC Driver 17 for SQL Server};"
            "SERVER=alisamaraa.ddns.net,4100;"
            "DATABASE=SalonProManager_DB;"
            "UID=sa;"
            "PWD=@a123admin4;"
            "TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def create_cash_register_table():
    """إنشاء جدول الخزنة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'cash_register'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("🔄 إنشاء جدول cash_register...")
            
            create_table_sql = """
            CREATE TABLE cash_register (
                transaction_id INT IDENTITY(1,1) PRIMARY KEY,
                transaction_type NVARCHAR(20) NOT NULL, -- 'income' أو 'expense'
                amount DECIMAL(10,2) NOT NULL,
                description NVARCHAR(500),
                reference_type NVARCHAR(50), -- 'invoice', 'expense', 'opening_balance', etc.
                reference_id INT, -- معرف الفاتورة أو المرجع
                branch_id INT NOT NULL,
                employee_id INT,
                payment_method NVARCHAR(50) DEFAULT 'نقدي',
                transaction_date DATETIME DEFAULT GETDATE(),
                created_by NVARCHAR(100),
                notes NVARCHAR(MAX),
                is_deleted BIT DEFAULT 0
            )
            """
            
            cursor.execute(create_table_sql)
            print("✅ تم إنشاء جدول cash_register بنجاح")
            
            # إدراج رصيد افتتاحي
            cursor.execute("""
                INSERT INTO cash_register (
                    transaction_type, amount, description, reference_type, 
                    branch_id, created_by, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                'income', 1000.00, 'رصيد افتتاحي', 'opening_balance',
                1, 'admin', 'رصيد افتتاحي للفرع الرئيسي'
            ))
            
            print("✅ تم إدراج رصيد افتتاحي: 1000 ج.م")
        else:
            print("✅ جدول cash_register موجود بالفعل")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول cash_register: {str(e)}")
        return False

def add_employee_to_invoices():
    """إضافة حقل الموظف المسؤول لجدول الفواتير"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'invoices' AND COLUMN_NAME = 'responsible_employee'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("🔄 إضافة حقل responsible_employee لجدول invoices...")
            
            cursor.execute("""
                ALTER TABLE invoices 
                ADD responsible_employee NVARCHAR(100)
            """)
            
            print("✅ تم إضافة حقل responsible_employee")
        else:
            print("✅ حقل responsible_employee موجود بالفعل")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة حقل responsible_employee: {str(e)}")
        return False

def create_cash_register_functions():
    """إنشاء دوال مساعدة للخزنة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إنشاء دوال مساعدة للخزنة...")
        
        # دالة حساب رصيد الخزنة
        create_function_sql = """
        IF OBJECT_ID('dbo.GetCashBalance', 'FN') IS NOT NULL
            DROP FUNCTION dbo.GetCashBalance
        GO
        
        CREATE FUNCTION dbo.GetCashBalance(@branch_id INT)
        RETURNS DECIMAL(10,2)
        AS
        BEGIN
            DECLARE @balance DECIMAL(10,2)
            
            SELECT @balance = ISNULL(SUM(
                CASE 
                    WHEN transaction_type = 'income' THEN amount
                    WHEN transaction_type = 'expense' THEN -amount
                    ELSE 0
                END
            ), 0)
            FROM cash_register
            WHERE branch_id = @branch_id AND is_deleted = 0
            
            RETURN @balance
        END
        """
        
        # تنفيذ الدالة (قد تحتاج تنفيذ منفصل)
        try:
            cursor.execute(create_function_sql)
            print("✅ تم إنشاء دالة GetCashBalance")
        except:
            print("⚠️ دالة GetCashBalance موجودة أو هناك مشكلة في إنشائها")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء دوال الخزنة: {str(e)}")
        return False

def test_cash_register():
    """اختبار جدول الخزنة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔍 اختبار جدول الخزنة...")
        
        # عدد المعاملات
        cursor.execute("SELECT COUNT(*) FROM cash_register")
        transaction_count = cursor.fetchone()[0]
        print(f"📊 عدد المعاملات: {transaction_count}")
        
        # رصيد الخزنة
        cursor.execute("""
            SELECT SUM(
                CASE 
                    WHEN transaction_type = 'income' THEN amount
                    WHEN transaction_type = 'expense' THEN -amount
                    ELSE 0
                END
            ) as balance
            FROM cash_register
            WHERE branch_id = 1 AND is_deleted = 0
        """)
        
        balance = cursor.fetchone()[0] or 0
        print(f"💰 رصيد الخزنة للفرع 1: {balance} ج.م")
        
        # آخر معاملة
        cursor.execute("""
            SELECT TOP 1 transaction_type, amount, description, transaction_date
            FROM cash_register
            ORDER BY transaction_date DESC
        """)
        
        last_transaction = cursor.fetchone()
        if last_transaction:
            print(f"📄 آخر معاملة: {last_transaction[0]} - {last_transaction[1]} ج.م - {last_transaction[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخزنة: {str(e)}")
        return False

def add_sample_cash_transactions():
    """إضافة معاملات تجريبية للخزنة"""
    try:
        conn = get_db_connection()
        if conn is None:
            return False

        cursor = conn.cursor()
        
        print("🔄 إضافة معاملات تجريبية للخزنة...")
        
        # معاملات تجريبية
        transactions = [
            ('expense', 50.00, 'شراء مواد تنظيف', 'expense', None, 1, 'admin', 'مصروفات يومية'),
            ('expense', 100.00, 'فاتورة كهرباء', 'expense', None, 1, 'admin', 'فواتير شهرية'),
            ('income', 200.00, 'دفعة نقدية من عميل', 'cash_payment', None, 1, 'admin', 'دفعة مباشرة')
        ]
        
        for transaction in transactions:
            cursor.execute("""
                INSERT INTO cash_register (
                    transaction_type, amount, description, reference_type, 
                    reference_id, branch_id, created_by, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, transaction)
        
        print(f"✅ تم إضافة {len(transactions)} معاملة تجريبية")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المعاملات التجريبية: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 تحديث هيكل قاعدة البيانات")
    print("=" * 50)
    
    # إنشاء جدول الخزنة
    if create_cash_register_table():
        print("✅ جدول الخزنة جاهز")
    
    # إضافة حقل الموظف المسؤول للفواتير
    if add_employee_to_invoices():
        print("✅ حقل الموظف المسؤول جاهز")
    
    # إنشاء دوال مساعدة
    if create_cash_register_functions():
        print("✅ دوال الخزنة جاهزة")
    
    # إضافة معاملات تجريبية
    if add_sample_cash_transactions():
        print("✅ معاملات تجريبية جاهزة")
    
    # اختبار النظام
    if test_cash_register():
        print("✅ نظام الخزنة يعمل بشكل صحيح")
    
    print("\n" + "=" * 50)
    print("🎉 تم تحديث قاعدة البيانات بنجاح!")
    print("💡 الآن يمكن:")
    print("   • اختيار الموظف المسؤول في الفواتير")
    print("   • تتبع معاملات الخزنة")
    print("   • حساب رصيد الخزنة تلقائياً")
    
    input("\nاضغط Enter للخروج...")
